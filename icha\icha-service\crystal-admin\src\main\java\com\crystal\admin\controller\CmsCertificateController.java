package com.crystal.admin.controller;

import com.crystal.common.model.cms.CmsCertificateEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsCertificateService;
import com.crystal.service.service.SystemAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;

/**
 * 证书 控制器
 * | Author: 陈佳音
 * ｜ @date Mon Apr 29 10:10:51 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/certificate")
public class CmsCertificateController {
    @Autowired
    private CmsCertificateService cmsCertificateService;
    @Autowired
    private SystemAttachmentService systemAttachmentService;


    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('cmscertificate:list')")
    public CommonResult<CommonPage<CmsCertificateEntity>> list(@Validated CmsCertificateEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsCertificateEntity> page = CommonPage.restPage(cmsCertificateService.queryPage(request,pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('cmscertificate:info')")
    public CommonResult<CmsCertificateEntity> info(@PathVariable("id") Long id){
        CmsCertificateEntity cmsCertificate = cmsCertificateService.getById(id);
        return CommonResult.success(cmsCertificate);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('cmscertificate:save')")
    public CommonResult<String> save(@RequestBody CmsCertificateEntity cmsCertificate){
        cmsCertificate.setAddTime(new Date());
        cmsCertificate.setImage(systemAttachmentService.clearPrefix(cmsCertificate.getImage()));
        cmsCertificateService.save(cmsCertificate);
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('cmscertificate:update')")
    public CommonResult<String> update(@RequestBody CmsCertificateEntity cmsCertificate){
        cmsCertificate.setImage(systemAttachmentService.clearPrefix(cmsCertificate.getImage()));
        cmsCertificateService.updateById(cmsCertificate);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('cmscertificate:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids){
        if (cmsCertificateService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 