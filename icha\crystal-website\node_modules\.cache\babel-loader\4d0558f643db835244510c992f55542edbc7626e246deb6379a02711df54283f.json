{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n// import Try from \"./components/try\";\nimport AppFunctions from \"@/utils/AppFunctions\";\nimport { isMobilePhone } from \"@/utils/index\";\nexport default {\n  name: \"Header\",\n  components: {\n    // Try,\n  },\n  data() {\n    return {\n      isLoggedIn: false,\n      userInfo: {},\n      isMobilePhone: isMobilePhone(),\n      AppFunctions,\n      mobileMenuActive: false,\n      navItems: [{\n        name: '主页',\n        path: '/index'\n      }, {\n        name: '关于我们',\n        path: '/about'\n      }, {\n        name: '认证课程',\n        path: '/course'\n      }, {\n        name: '工作坊',\n        path: '/workshop'\n      },\n      // { name: '脉轮测试', path: '/chakra-test' },\n      {\n        name: '证书查询',\n        path: '/certificate'\n      }, {\n        name: '资料下载',\n        path: '/download'\n      }, {\n        name: '加入我们',\n        path: '/join'\n      }]\n    };\n  },\n  methods: {\n    checkLoginStatus() {\n      const token = localStorage.getItem(\"token\");\n      if (token) {\n        try {\n          const userInfoStr = localStorage.getItem(\"userInfo\");\n          if (userInfoStr) {\n            this.userInfo = JSON.parse(userInfoStr);\n            this.isLoggedIn = true;\n          } else {\n            // 有token但没有用户信息，尝试获取用户信息\n            this.getUserInfo();\n          }\n        } catch (error) {\n          console.error(\"解析用户信息失败\", error);\n          this.clearLoginInfo();\n        }\n      }\n    },\n    goLogin() {\n      this.$router.push({\n        path: '/login'\n      });\n    },\n    tryHandle() {\n      this.$router.push({\n        name: 'try'\n      });\n      // \tthis.tryVisible = true;\n      // \tthis.$nextTick(() => {\n      // \t\tthis.$refs.try.init();\n      // \t});\n    },\n\n    toggleMenu() {\n      this.mobileMenuActive = !this.mobileMenuActive;\n      // 控制body滚动\n      if (this.mobileMenuActive) {\n        document.body.style.overflow = 'hidden';\n      } else {\n        document.body.style.overflow = '';\n      }\n    },\n    getMobileItemStyle(index) {\n      // 仅在移动端添加动画延迟\n      if (this.isMobilePhone) {\n        return {\n          transitionDelay: `${index * 0.05}s`\n        };\n      }\n      return {};\n    },\n    toggleStickyHeader() {\n      if (isMobilePhone()) {\n        return;\n      }\n      const scrolled = document.documentElement.scrollTop;\n      if (scrolled > 100) {\n        AppFunctions.addClass(\".header-default\", \"sticky\");\n      } else if (scrolled <= 100) {\n        AppFunctions.removeClass(\".header-default\", \"sticky\");\n      }\n    },\n    clearLoginInfo() {\n      // 清除所有存储的登录信息\n      sessionStorage.removeItem(\"token\");\n      sessionStorage.removeItem(\"userInfo\");\n      localStorage.removeItem(\"token\");\n      localStorage.removeItem(\"userInfo\");\n      this.isLoggedIn = false;\n      this.userInfo = {};\n    }\n  },\n  created() {\n    window.addEventListener(\"scroll\", this.toggleStickyHeader);\n    this.checkLoginStatus();\n  },\n  mounted() {\n    this.toggleStickyHeader();\n  },\n  beforeDestroy() {\n    window.removeEventListener(\"scroll\", this.toggleStickyHeader);\n  }\n};", "map": {"version": 3, "names": ["AppFunctions", "isMobilePhone", "name", "components", "data", "isLoggedIn", "userInfo", "mobileMenuActive", "navItems", "path", "methods", "checkLoginStatus", "token", "localStorage", "getItem", "userInfoStr", "JSON", "parse", "getUserInfo", "error", "console", "clearLoginInfo", "goLogin", "$router", "push", "<PERSON><PERSON><PERSON><PERSON>", "toggleMenu", "document", "body", "style", "overflow", "getMobileItemStyle", "index", "transitionDelay", "toggle<PERSON><PERSON>yHeader", "scrolled", "documentElement", "scrollTop", "addClass", "removeClass", "sessionStorage", "removeItem", "created", "window", "addEventListener", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener"], "sources": ["src/components/common/header/Header.vue"], "sourcesContent": ["<template>\r\n\t<div class=\"header-wrapper\">\r\n\t\t<!-- 顶部栏 -->\r\n\t\t<div class=\"header\">\r\n\t\t\t<div class=\"header-content\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<img src=\"@/assets/images/logo.png\" alt=\"Logo\" />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"header-mid\">\r\n\t\t\t\t\t<div class=\"header-item\">\r\n\t\t\t\t\t\t<strong>国际水晶疗愈协会</strong>\r\n\t\t\t\t\t\t<span v-if=\"!isMobilePhone\" :style=\"'margin-left: 20px;'\">International Crystal Healing Association, <span style=\"font-size: 12px;font-weight: 600;\">ICHA</span></span>\r\n\t\t\t\t\t\t<span v-else>International Crystal Healing Association<br />ICHA</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div :class=\"isMobilePhone ? 'header-right-mobile' : 'header-right'\">\r\n\t\t\t\t\t<button type=\"button\" class=\"login-btn\" @click=\"goLogin\">{{userInfo && isLoggedIn ? (userInfo.nickname || userInfo.nickName) : '登录'}}</button>\r\n\t\t\t\t\t<!-- 移动端汉堡菜单按钮 -->\r\n\t\t\t\t\t<div :class=\"['menu-toggle', {'active': mobileMenuActive}]\" @click=\"toggleMenu\">\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 导航菜单 -->\r\n\t\t<div :class=\"['nav-wrapper', 'header-default', {'mobile-active': mobileMenuActive}]\">\r\n\t\t\t<div class=\"nav\">\r\n\t\t\t\t<ul class=\"nav-list\">\r\n\t\t\t\t\t<li v-for=\"(item, index) in navItems\" :key=\"index\" :style=\"getMobileItemStyle(index)\">\r\n\t\t\t\t\t\t<router-link class=\"router\" :to=\"item.path\">{{ item.name }}</router-link>\r\n\t\t\t\t\t</li>\r\n\t\t\t\t</ul>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 移动菜单背景遮罩 -->\r\n\t\t<div \r\n\t\t\tclass=\"mobile-menu-overlay\" \r\n\t\t\tv-if=\"isMobilePhone\" \r\n\t\t\t:class=\"{'active': mobileMenuActive}\"\r\n\t\t\t@click=\"toggleMenu\"\r\n\t\t></div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n// import Try from \"./components/try\";\r\nimport AppFunctions from \"@/utils/AppFunctions\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nexport default {\r\n\tname: \"Header\",\r\n\tcomponents: {\r\n\t\t// Try,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLoggedIn: false,\r\n\t\t\tuserInfo: {},\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tAppFunctions,\r\n\t\t\tmobileMenuActive: false,\r\n\t\t\tnavItems: [\r\n\t\t\t\t{ name: '主页', path: '/index' },\r\n\t\t\t\t{ name: '关于我们', path: '/about' },\r\n\t\t\t\t{ name: '认证课程', path: '/course' },\r\n\t\t\t\t{ name: '工作坊', path: '/workshop' },\r\n\t\t\t\t// { name: '脉轮测试', path: '/chakra-test' },\r\n\t\t\t\t{ name: '证书查询', path: '/certificate' },\r\n\t\t\t\t{ name: '资料下载', path: '/download' },\r\n\t\t\t\t{ name: '加入我们', path: '/join' },\r\n\t\t\t]\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\tcheckLoginStatus() {\r\n\t\t\tconst token = localStorage.getItem(\"token\");\r\n\t\t\tif (token) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst userInfoStr = localStorage.getItem(\"userInfo\") ;\r\n\t\t\t\t\tif (userInfoStr) {\r\n\t\t\t\t\t\tthis.userInfo = JSON.parse(userInfoStr);\r\n\t\t\t\t\t\tthis.isLoggedIn = true;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 有token但没有用户信息，尝试获取用户信息\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"解析用户信息失败\", error);\r\n\t\t\t\t\tthis.clearLoginInfo();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tgoLogin() {\r\n\t\t\tthis.$router.push({\r\n\t\t\t\tpath: '/login'\r\n\t\t\t})\r\n\t\t},\r\n\t\ttryHandle() {\r\n\t\t\tthis.$router.push({\r\n\t\t\t\tname: 'try'\r\n\t\t\t})\r\n\t\t// \tthis.tryVisible = true;\r\n\t\t// \tthis.$nextTick(() => {\r\n\t\t// \t\tthis.$refs.try.init();\r\n\t\t// \t});\r\n\t\t},\r\n\t\ttoggleMenu() {\r\n\t\t\tthis.mobileMenuActive = !this.mobileMenuActive;\r\n\t\t\t// 控制body滚动\r\n\t\t\tif (this.mobileMenuActive) {\r\n\t\t\t\tdocument.body.style.overflow = 'hidden';\r\n\t\t\t} else {\r\n\t\t\t\tdocument.body.style.overflow = '';\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetMobileItemStyle(index) {\r\n\t\t\t// 仅在移动端添加动画延迟\r\n\t\t\tif (this.isMobilePhone) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttransitionDelay: `${index * 0.05}s`\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\treturn {};\r\n\t\t},\r\n\t\ttoggleStickyHeader() {\r\n\t\t\tif(isMobilePhone()){\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tconst scrolled = document.documentElement.scrollTop;\r\n\t\t\tif (scrolled > 100) {\r\n\t\t\t\tAppFunctions.addClass(\".header-default\", \"sticky\");\r\n\t\t\t} else if (scrolled <= 100) {\r\n\t\t\t\tAppFunctions.removeClass(\".header-default\", \"sticky\");\r\n\t\t\t}\r\n\t\t},\r\n\t\tclearLoginInfo() {\r\n\t\t\t// 清除所有存储的登录信息\r\n\t\t\tsessionStorage.removeItem(\"token\");\r\n\t\t\tsessionStorage.removeItem(\"userInfo\");\r\n\t\t\tlocalStorage.removeItem(\"token\");\r\n\t\t\tlocalStorage.removeItem(\"userInfo\");\r\n\t\t\tthis.isLoggedIn = false;\r\n\t\t\tthis.userInfo = {};\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\twindow.addEventListener(\"scroll\", this.toggleStickyHeader);\r\n\t\tthis.checkLoginStatus();\r\n\t},\r\n\tmounted() {\r\n\t\tthis.toggleStickyHeader();\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\twindow.removeEventListener(\"scroll\", this.toggleStickyHeader);\r\n\t},\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 基础样式 */\r\n.header-wrapper {\r\n\twidth: 100%;\r\n\tposition: relative;\r\n\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.header {\r\n\tpadding: 15px 20px;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.header-content {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmax-width: 1200px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n/* Logo区域 */\r\n.header-left img {\r\n\theight: 60px;\r\n\twidth: auto;\r\n}\r\n\r\n/* 中间标题区域 */\r\n.header-mid {\r\n\tflex: 1;\r\n\tmargin: 0 20px;\r\n}\r\n\r\n.header-mid .item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.header-mid strong {\r\n\tfont-size: 22px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 2px;\r\n}\r\n\r\n.header-mid span {\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 右侧登录按钮 */\r\n.header-right {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-left:100px;\r\n}\r\n.header-right-mobile {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.login-btn {\r\n\tbackground-color: #516790;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tpadding: 8px 22px;\r\n\tborder-radius: 4px;\r\n\tfont-size: 14px;\r\n\tcursor: pointer;\r\n\ttransition: background-color 0.3s;\r\n}\r\n\r\n.login-btn:hover {\r\n\tbackground-color: #3f5273;\r\n}\r\n\r\n/* 导航菜单 */\r\n.nav-wrapper {\r\n\tbackground-color: #f8f8f8;\r\n\tborder-top: 1px solid #eee;\r\n\ttransition: all 0.3s ease;\r\n\twidth: 100%;\r\n}\r\n\r\n.nav {\r\n\tmax-width: 1200px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n.nav-list {\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n\tlist-style: none;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n}\r\n\r\n.nav-list li {\r\n\tposition: relative;\r\n}\r\n\r\n.router {\r\n\tdisplay: block;\r\n\tpadding: 15px 10px;\r\n\tcolor: #333;\r\n\ttext-decoration: none;\r\n\tfont-size: 16px;\r\n\ttransition: color 0.3s;\r\n\ttext-align: center;\r\n}\r\n\r\n.router:hover {\r\n\tcolor: #516790;\r\n}\r\n\r\n.router.router-link-active {\r\n\tcursor: default;\r\n\tfont-weight: 600;\r\n\tcolor: #516790;\r\n}\r\n\r\n/* 粘性导航 */\r\n.sticky {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tz-index: 900;\r\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n\tanimation: slideDown 0.3s ease;\r\n}\r\n\r\n@keyframes slideDown {\r\n\tfrom {\r\n\t\ttransform: translateY(-100%);\r\n\t}\r\n\tto {\r\n\t\ttransform: translateY(0);\r\n\t}\r\n}\r\n\r\n/* 汉堡菜单 */\r\n.menu-toggle {\r\n\tdisplay: none;\r\n\tflex-direction: column;\r\n\tjustify-content: space-between;\r\n\twidth: 30px;\r\n\theight: 20px;\r\n\tcursor: pointer;\r\n\tmargin-left: 20px;\r\n\tposition: relative;\r\n\tz-index: 1001;\r\n}\r\n\r\n.menu-toggle span {\r\n\tdisplay: block;\r\n\theight: 2px;\r\n\twidth: 90%;\r\n\tbackground-color: #333;\r\n\tborder-radius: 3px;\r\n\ttransition: transform 0.3s ease, opacity 0.2s ease;\r\n\ttransform-origin: center;\r\n}\r\n\r\n/* 汉堡菜单变X动画 */\r\n.menu-toggle.active span:nth-child(1) {\r\n\ttransform: translateY(9px) rotate(45deg);\r\n}\r\n\r\n.menu-toggle.active span:nth-child(2) {\r\n\topacity: 0;\r\n}\r\n\r\n.menu-toggle.active span:nth-child(3) {\r\n\ttransform: translateY(-9px) rotate(-45deg);\r\n}\r\n\r\n/* 移动菜单背景遮罩 */\r\n.mobile-menu-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\tz-index: 999;\r\n\tvisibility: hidden;\r\n\topacity: 0;\r\n\ttransition: opacity 0.3s ease, visibility 0.3s ease;\r\n}\r\n\r\n.mobile-menu-overlay.active {\r\n\tvisibility: visible;\r\n\topacity: 1;\r\n}\r\n\r\n/* 媒体查询 - 移动端适配 */\r\n@media (max-width: 768px) {\r\n\t/* 移动端基础样式 */\r\n\t.header-wrapper {\r\n\t\toverflow-x: hidden;\r\n\t}\r\n\r\n\t.header-mid strong {\r\n\t\tfont-size: 18px;\r\n\t}\r\n\t\r\n\t.header-mid span {\r\n\t\tfont-size: 12px;\r\n\t}\r\n\t\r\n\t.menu-toggle {\r\n\t\tdisplay: flex;\r\n\t}\r\n\t\r\n\t.login-btn {\r\n\t\tpadding: 6px 15px;\r\n\t\tfont-size: 13px;\r\n\t}\r\n\t\r\n\t/* 移动导航样式增强 */\r\n\t.nav-wrapper {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tright: -280px;\r\n\t\twidth: 280px;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tz-index: 1000;\r\n\t\ttransition: transform 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);\r\n\t\tbox-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);\r\n\t\toverflow-y: auto;\r\n\t\tpadding-top: 60px;\r\n\t}\r\n\t\r\n\t.nav-wrapper.sticky {\r\n\t\tright: -280px;\r\n\t\tposition: fixed;\r\n\t\tanimation: none;\r\n\t}\r\n\t\r\n\t.nav-wrapper.mobile-active {\r\n\t\ttransform: translateX(-280px);\r\n\t}\r\n\t\r\n\t.nav-wrapper.sticky.mobile-active {\r\n\t\ttransform: translateX(-280px);\r\n\t}\r\n\t\r\n\t.nav {\r\n\t\tmax-width: 100%;\r\n\t}\r\n\t\r\n\t.nav-list {\r\n\t\tflex-direction: column;\r\n\t\theight: auto;\r\n\t\tpadding: 20px 0;\r\n\t}\r\n\t\r\n\t.nav-list li {\r\n\t\topacity: 0;\r\n\t\ttransform: translateX(20px);\r\n\t\ttransition: opacity 0.4s ease, transform 0.4s ease;\r\n\t}\r\n\t\r\n\t.mobile-active .nav-list li {\r\n\t\topacity: 1;\r\n\t\ttransform: translateX(0);\r\n\t}\r\n\t\r\n\t.router {\r\n\t\tpadding: 15px 25px;\r\n\t\ttext-align: left;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t}\r\n\t\r\n\t.router:active {\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n}\r\n\r\n/* 小屏幕手机适配 */\r\n@media (max-width: 480px) {\r\n\t.header-left img {\r\n\t\theight: 40px;\r\n\t}\r\n\t\r\n\t.header-mid {\r\n\t\tmargin: 0 10px;\r\n\t}\r\n\t\r\n\t.header-mid strong {\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t.header-mid span {\r\n\t\tfont-size: 10px;\r\n\t}\r\n\t\r\n\t.header {\r\n\t\tpadding: 10px 15px;\r\n\t}\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA;AACA,OAAAA,YAAA;AACA,SAAAC,aAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACA;EAAA,CACA;EACAC,KAAA;IACA;MACAC,UAAA;MACAC,QAAA;MACAL,aAAA,EAAAA,aAAA;MACAD,YAAA;MACAO,gBAAA;MACAC,QAAA,GACA;QAAAN,IAAA;QAAAO,IAAA;MAAA,GACA;QAAAP,IAAA;QAAAO,IAAA;MAAA,GACA;QAAAP,IAAA;QAAAO,IAAA;MAAA,GACA;QAAAP,IAAA;QAAAO,IAAA;MAAA;MACA;MACA;QAAAP,IAAA;QAAAO,IAAA;MAAA,GACA;QAAAP,IAAA;QAAAO,IAAA;MAAA,GACA;QAAAP,IAAA;QAAAO,IAAA;MAAA;IAEA;EACA;EACAC,OAAA;IACAC,iBAAA;MACA,MAAAC,KAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,IAAAF,KAAA;QACA;UACA,MAAAG,WAAA,GAAAF,YAAA,CAAAC,OAAA;UACA,IAAAC,WAAA;YACA,KAAAT,QAAA,GAAAU,IAAA,CAAAC,KAAA,CAAAF,WAAA;YACA,KAAAV,UAAA;UACA;YACA;YACA,KAAAa,WAAA;UACA;QACA,SAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,aAAAA,KAAA;UACA,KAAAE,cAAA;QACA;MACA;IACA;IACAC,QAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAf,IAAA;MACA;IACA;IACAgB,UAAA;MACA,KAAAF,OAAA,CAAAC,IAAA;QACAtB,IAAA;MACA;MACA;MACA;MACA;MACA;IACA;;IACAwB,WAAA;MACA,KAAAnB,gBAAA,SAAAA,gBAAA;MACA;MACA,SAAAA,gBAAA;QACAoB,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;MACA;QACAH,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;MACA;IACA;IACAC,mBAAAC,KAAA;MACA;MACA,SAAA/B,aAAA;QACA;UACAgC,eAAA,KAAAD,KAAA;QACA;MACA;MACA;IACA;IACAE,mBAAA;MACA,IAAAjC,aAAA;QACA;MACA;MACA,MAAAkC,QAAA,GAAAR,QAAA,CAAAS,eAAA,CAAAC,SAAA;MACA,IAAAF,QAAA;QACAnC,YAAA,CAAAsC,QAAA;MACA,WAAAH,QAAA;QACAnC,YAAA,CAAAuC,WAAA;MACA;IACA;IACAlB,eAAA;MACA;MACAmB,cAAA,CAAAC,UAAA;MACAD,cAAA,CAAAC,UAAA;MACA5B,YAAA,CAAA4B,UAAA;MACA5B,YAAA,CAAA4B,UAAA;MACA,KAAApC,UAAA;MACA,KAAAC,QAAA;IACA;EACA;EACAoC,QAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAV,kBAAA;IACA,KAAAvB,gBAAA;EACA;EACAkC,QAAA;IACA,KAAAX,kBAAA;EACA;EACAY,cAAA;IACAH,MAAA,CAAAI,mBAAA,gBAAAb,kBAAA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}