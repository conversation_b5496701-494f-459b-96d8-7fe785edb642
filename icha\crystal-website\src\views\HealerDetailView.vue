<template>
  <Layout>
    <!-- 页面顶部背景区域 -->
    <div class="healer-detail-hero">
      <div class="hero-overlay"></div>
      <div class="container hero-container">
        <div class="hero-nav">
          <button class="back-btn" @click="goBack">
            <i class="fa fa-arrow-left"></i> 返回
          </button>
        </div>
        <div class="hero-content">
          <h1 class="hero-title">疗愈师详情</h1>
          <div class="title-dots">
            <span></span><span></span><span></span>
          </div>
          <p class="hero-subtitle">
            <i class="fa fa-heart"></i> 了解您的专属疗愈指导者
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="section healer-detail-section">
      <div class="container">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <i class="fa fa-spinner fa-spin fa-3x"></i>
          <p>正在加载疗愈师信息...</p>
        </div>

        <!-- 详情内容 -->
        <div v-else-if="healer" class="healer-detail-container">
          <!-- 基本信息卡片 -->
          <div class="healer-info-card">
            <div class="healer-avatar">
              <img :src="healer.avatar" alt="疗愈师头像">
              <div class="healer-badge"><i class="fa fa-certificate"></i> 认证疗愈师</div>
            </div>
            <div class="healer-basic-info">
              <h2><i class="fa fa-user-circle-o"></i> {{healer.name}}</h2>
              <div class="healer-tags">
                <span v-for="(tag, i) in healer.tags" :key="i"><i class="fa fa-star-o"></i> {{tag}}</span>
              </div>
              <p class="healer-location"><i class="fa fa-map-marker"></i> {{healer.location}}</p>
              <div class="healer-intro">
                <p><i class="fa fa-quote-left quote-icon"></i> {{healer.intro}}</p>
              </div>
              <div class="healer-actions">
                <!-- <button class="crystal-btn contact-btn"><i class="fa fa-comments"></i> 联系咨询</button> -->
              </div>
            </div>
          </div>

          <!-- 详细介绍 -->
          <div class="content-wrapper">
            <div class="healer-description-card">
              <h3 class="card-title"><i class="fa fa-file-text-o"></i> 详细介绍</h3>
              <div class="rich-text-content" v-html="healer.content"></div>
            </div>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else class="error-container">
          <i class="fa fa-exclamation-circle fa-4x"></i>
          <h3>未找到疗愈师信息</h3>
          <p>该疗愈师可能不存在或已被移除</p>
          <button class="crystal-btn" @click="goBack"><i class="fa fa-arrow-left"></i> 返回列表</button>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";

export default {
  name: "HealerDetailView",
  components: { Layout },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      loading: true,
      healer: null,
      healerId: null,
      fromPath: ''
    }
  },
  created() {
    this.healerId = this.$route.params.id;
    this.fromPath = this.$route.query.from || '';
    this.getHealerDetail();
  },
  mounted() {
    this.$wxShare();
  },
  methods: {
    // 获取疗愈师详情
    getHealerDetail() {
      this.loading = true;
      
      this.getRequest(`/cms/healers/info?id=${this.healerId}`).then(resp => {
        this.loading = false;
        if (resp && resp.code == 200) {
          this.healer = resp.data;
          // 处理标签
          if (this.healer) {
            this.healer.tags = this.healer.tags ? this.healer.tags.split(',') : [];
            // 处理资质信息
            this.healer.certifications = this.healer.certifications ? this.healer.certifications.split(',') : [];
          }
        } else {
          this.healer = null;
        }
      }).catch(() => {
        this.loading = false;
        this.healer = null;
      });
    },
    // 返回上一页
    goBack() {
      if (this.$route.query.from === 'index') {
        this.$router.push('/index');
      } else {
        this.$router.push('/healers');
      }
    }
  }
}
</script>

<style scoped>
/* 页面顶部背景区域样式 */
.healer-detail-hero {
  height: 280px;
  background: linear-gradient(135deg, #28224e, #372f6a);
  position: relative;
  width: 100%;
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='400' viewBox='0 0 800 800'%3E%3Cg fill='none' stroke='%234E4585' stroke-width='1'%3E%3Cpath d='M769 229L1037 260.9M927 880L731 737 520 660 309 538 40 599 295 764 126.5 879.5 40 599-197 493 102 382-31 229 126.5 79.5-69-63'/%3E%3Cpath d='M-31 229L237 261 390 382 603 493 308.5 537.5 101.5 381.5M370 905L295 764'/%3E%3Cpath d='M520 660L578 842 731 737 840 599 603 493 520 660 295 764 309 538 390 382 539 269 769 229 577.5 41.5 370 105 295 -36 126.5 79.5 237 261 102 382 40 599 -69 737 127 880'/%3E%3Cpath d='M520-140L578.5 42.5 731-63M603 493L539 269 237 261 370 105M902 382L539 269M390 382L102 382'/%3E%3Cpath d='M-222 42L126.5 79.5 370 105 539 269 577.5 41.5 927 80 769 229 902 382 603 493 731 737M295-36L577.5 41.5M578 842L295 764M40-201L127 80M102 382L-261 269'/%3E%3C/g%3E%3Cg fill='%234E4585'%3E%3Ccircle cx='769' cy='229' r='5'/%3E%3Ccircle cx='539' cy='269' r='5'/%3E%3Ccircle cx='603' cy='493' r='5'/%3E%3Ccircle cx='731' cy='737' r='5'/%3E%3Ccircle cx='520' cy='660' r='5'/%3E%3Ccircle cx='309' cy='538' r='5'/%3E%3Ccircle cx='295' cy='764' r='5'/%3E%3Ccircle cx='40' cy='599' r='5'/%3E%3Ccircle cx='102' cy='382' r='5'/%3E%3Ccircle cx='127' cy='80' r='5'/%3E%3Ccircle cx='370' cy='105' r='5'/%3E%3Ccircle cx='578' cy='42' r='5'/%3E%3Ccircle cx='237' cy='261' r='5'/%3E%3Ccircle cx='390' cy='382' r='5'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.2;
}

.hero-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  position: relative;
  z-index: 2;
}

.hero-nav {
  padding-top: 20px;
  text-align: left;
}

.back-btn {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateX(-3px);
}

.back-btn i {
  margin-right: 8px;
}

.hero-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  text-align: right;
  padding-right: 15px;
  padding-bottom: 30px;
}

.hero-title {
  font-size: 36px;
  font-weight: 700;
  color: white;
  margin-bottom: 10px;
  position: relative;
  letter-spacing: 2px;
}

.title-dots {
  display: flex;
  margin-bottom: 15px;
  gap: 6px;
}

.title-dots span {
  width: 8px;
  height: 8px;
  background-color: #fff;
  border-radius: 50%;
  display: inline-block;
}

.title-dots span:nth-child(2) {
  opacity: 0.7;
}

.title-dots span:nth-child(3) {
  opacity: 0.4;
}

.hero-subtitle {
  font-size: 16px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.hero-subtitle i {
  margin-right: 8px;
  color: #ff6b8b;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

/* 主内容区域样式 */
.healer-detail-section {
  padding: 60px 0;
  background-color: #f8f5ff;
  min-height: 600px;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.loading-container i {
  color: #7b4397;
  margin-bottom: 20px;
}

.loading-container p {
  color: #666;
  font-size: 16px;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.error-container i {
  color: #dc2430;
  margin-bottom: 20px;
  opacity: 0.7;
}

.error-container h3 {
  font-size: 22px;
  color: #3a2c58;
  margin-bottom: 15px;
}

.error-container p {
  color: #666;
  margin-bottom: 25px;
}

/* 详情内容样式 */
.healer-detail-container {
  position: relative;
  z-index: 2;
}

.healer-info-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  margin-top: -80px;
  display: flex;
  position: relative;
  z-index: 3;
}

.healer-avatar {
  width: 300px;
  min-width: 300px;
  height: 350px;
  overflow: hidden;
  position: relative;
}

.healer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.healer-info-card:hover .healer-avatar img {
  transform: scale(1.05);
}

.healer-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(135deg, #7b4397, #dc2430);
  color: white;
  padding: 5px 12px;
  border-radius: 25px;
  font-size: 12px;
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.healer-badge i {
  margin-right: 5px;
}

.healer-basic-info {
  padding: 35px;
  flex: 1;
}

.healer-basic-info h2 {
  font-size: 28px;
  color: #3a2c58;
  margin-bottom: 15px;
  font-weight: 600;
}

.healer-basic-info h2 i {
  color: #7b4397;
  margin-right: 10px;
}

.healer-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.healer-tags span {
  background: linear-gradient(135deg, #ddd6f3, #faaca8);
  color: #3a2c58;
  font-size: 13px;
  padding: 6px 14px;
  border-radius: 20px;
  font-weight: 500;
}

.healer-tags span i {
  font-size: 11px;
  margin-right: 5px;
}

.healer-location {
  color: #666;
  font-size: 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.healer-location i {
  color: #7b4397;
  margin-right: 8px;
  font-size: 18px;
}

.healer-intro {
  color: #555;
  font-size: 16px;
  line-height: 1.7;
  margin-bottom: 30px;
  position: relative;
  padding: 15px 20px;
  background: #f9f6ff;
  border-radius: 10px;
  border-left: 4px solid #7b4397;
}

.healer-intro p {
  margin: 0;
  font-style: italic;
}

.quote-icon {
  font-size: 16px;
  margin-right: 5px;
  color: #7b4397;
  opacity: 0.7;
}

.healer-actions {
  display: flex;
  gap: 15px;
}

.contact-btn {
  font-size: 16px;
  padding: 12px 30px;
}

/* 详细介绍区域 */
.content-wrapper {
  margin-top: 40px;
}

.healer-description-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  padding: 35px;
}

.card-title {
  font-size: 22px;
  color: #3a2c58;
  margin-bottom: 25px;
  font-weight: 600;
  border-bottom: 2px solid #f0e6ff;
  padding-bottom: 15px;
  position: relative;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 80px;
  height: 2px;
  background: linear-gradient(to right, #7b4397, #dc2430);
}

.card-title i {
  color: #7b4397;
  margin-right: 10px;
}

.rich-text-content {
  color: #444;
  font-size: 16px;
  line-height: 1.8;
}

/* Vue 不支持 >>> 选择器的 scoped style，使用 /deep/ 或 ::v-deep 代替 */
.rich-text-content /deep/ img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.rich-text-content /deep/ h1,
.rich-text-content /deep/ h2,
.rich-text-content /deep/ h3,
.rich-text-content /deep/ h4,
.rich-text-content /deep/ h5,
.rich-text-content /deep/ h6 {
  color: #3a2c58;
  margin: 25px 0 15px;
  font-weight: 600;
}

.rich-text-content /deep/ p {
  margin-bottom: 18px;
}

.rich-text-content /deep/ a {
  color: #7b4397;
  text-decoration: none;
  border-bottom: 1px dotted #7b4397;
  transition: all 0.3s;
}

.rich-text-content /deep/ a:hover {
  color: #dc2430;
  border-bottom-color: #dc2430;
}

.rich-text-content /deep/ ul,
.rich-text-content /deep/ ol {
  margin-left: 25px;
  margin-bottom: 20px;
}

.rich-text-content /deep/ li {
  margin-bottom: 10px;
}

.rich-text-content /deep/ blockquote {
  border-left: 4px solid #ddd6f3;
  padding: 15px 20px;
  background: #f9f6ff;
  margin: 20px 0;
  font-style: italic;
  border-radius: 0 8px 8px 0;
}

/* 水晶按钮 */
.crystal-btn {
  background: linear-gradient(135deg, #7b4397, #dc2430);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(123, 67, 151, 0.3);
}

.crystal-btn:hover {
  background: linear-gradient(135deg, #dc2430, #7b4397);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(123, 67, 151, 0.4);
}

.crystal-btn i {
  margin-right: 8px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .healer-detail-hero {
    height: 220px;
  }
  
  .hero-content {
    align-items: center;
    text-align: center;
    padding-right: 0;
  }
  
  .hero-title {
    font-size: 28px;
  }
  
  .hero-subtitle {
    font-size: 14px;
    justify-content: center;
  }
  
  .healer-detail-section {
    padding: 30px 0 60px;
  }
  
  .healer-info-card {
    flex-direction: column;
    margin-top: -50px;
  }
  
  .healer-avatar {
    width: 100%;
    height: 280px;
  }
  
  .healer-basic-info {
    padding: 25px 20px;
  }
  
  .healer-description-card {
    padding: 25px 20px;
  }
  
  .back-btn {
    padding: 6px 15px;
    font-size: 14px;
  }
  
  .contact-btn {
    width: 100%;
  }
}
</style> 