
import request from '@/utils/request'

/**
 * 新增Question
 * @param pram
 */
export function QuestionCreateApi(data) {
    return request({
        url: 'question/save',
        method: 'POST',
        data
    })
}

/**
 * question更新
 * @param pram
 */
export function questionUpdateApi(data) {
    return request({
        url: 'question/update',
        method: 'POST',
        data
    })
}

/**
 * question详情
 * @param pram
 */
export function questionDetailApi(id) {
    return request({
        url: `question/info/${id}`,
        method: 'GET'
    })
}

/**
 * question删除
 * @param pram
 */
export function questionDeleteApi(id) {
    return request({
        url: `question/delete/${id}`,
        method: 'get'
    })
}


/**
 * question列表
 * @param pram
 */
export function questionListApi(params) {
    return request({
        url: 'question/list',
        method: 'GET',
        params
    })
}

