<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.crystal.service.dao.CmsDownloadLogDao">

    <!-- 根据包名 模块名 以及类名 生成Mapper XML 配置文件 -->
    <resultMap type="com.crystal.common.model.cms.CmsDownloadLogEntity" id="cmsDownloadLogMap">
        <result property="id" column="id"/>
        <result property="addTime" column="add_time"/>
        <result property="isDel" column="is_del"/>
        <result property="cmsDownloadId" column="cms_download_id"/>
        <result property="userId" column="user_id"/>
    </resultMap>

</mapper> 