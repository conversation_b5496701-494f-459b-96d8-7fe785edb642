<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <!-- <el-form-item label="课程ID" prop="cmsCourseId">
        <el-input v-model="dataForm.cmsCourseId" placeholder="课程ID"></el-input>
      </el-form-item> -->
      <el-form-item label="视频名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="视频名称"></el-input>
      </el-form-item>
      <el-form-item label="视频封面" prop="cover">
        <div class="upLoadPicBox" @click="modalPicTap('1')">
          <div v-if="dataForm.cover" class="pictrue"><img :src="dataForm.cover"></div>
          <div v-else class="upLoad">
            <i class="el-icon-camera cameraIconfont" />
          </div>
        </div>
        <!-- 尺寸提示 -->
        <div class="sizeTip">
          <span>尺寸提示：</span>
          <span>宽度：1920px</span>
          <span>高度：1080px</span>
        </div>
      </el-form-item>
      <el-form-item label="视频id" prop="fileId">
        <el-input v-model="dataForm.fileId" placeholder="视频id"></el-input>
        <div class="sizeTip">
          <span>提示：视频id从阿里云 云点播获取，输入即可自动获取视频相关信息</span>
        </div>
      </el-form-item>
      <!--  -->

      <!-- <el-form-item label="视频" prop="mediaUrl">
        <div class="upload-video">
          <el-input v-model="dataForm.mediaUrl" placeholder="视频链接" class="input-with-select">
            <el-button slot="append" @click="handleVideoUpload">上传</el-button>
          </el-input>
          <div v-if="dataForm.mediaUrl" class="preview-video">
            <video :src="dataForm.mediaUrl" controls style="max-width: 100%; max-height: 200px;"></video>
          </div>
        </div>
      </el-form-item> -->
      <el-form-item label="资料文件" prop="file">
        <upload-file v-model="dataForm.file"></upload-file>
        <div class="sizeTip">
          <span>提示：支持上传PDF、DOC、DOCX、PPT、PPTX等格式的资料文件</span>
        </div>
      </el-form-item>
      <el-form-item label="排序" prop="paixu">
        <el-input-number v-model="dataForm.paixu" :min="0" placeholder="排序"></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  cmsCourseVideoInfoApi,
  cmsCourseVideoAddApi,
  cmsCourseVideoUpdateApi
} from '@/api/cmsCourseVideo'
import { videoType } from '@/data/common'
import UploadFile from '@/components/Upload/uploadFile'

export default {
  components: {
    UploadFile
  },
  data() {
    return {
      visible: false,
      videoTypeOptions: videoType,
      dataForm: {
        id: 0,
        cmsCourseId: '',
        name: '',
        fileId: '',
        fileSize: null,
        duration: 0,
        mediaUrl: '',
        frameRate: null,
        paixu: 0,
        type: 0,
        cover: '',
        inviteCode: '',
        file: ''
      },
      dataRule: {
        cmsCourseId: [
          { required: true, message: '课程ID不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '视频名称不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '视频类型不能为空', trigger: 'change' }
        ],
        mediaUrl: [
          { required: true, message: '视频不能为空', trigger: 'blur' }
        ],
        paixu: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id, cmsCourseId) {
      this.dataForm.id = id || 0
      this.dataForm.cmsCourseId = cmsCourseId || ''
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          cmsCourseVideoInfoApi(this.dataForm.id).then(data => {
            if (data) {
              this.dataForm.cmsCourseId = data.cmsCourseId
              this.dataForm.name = data.name
              this.dataForm.fileId = data.fileId
              this.dataForm.fileSize = data.fileSize
              this.dataForm.duration = data.duration
              this.dataForm.mediaUrl = data.mediaUrl
              this.dataForm.frameRate = data.frameRate
              this.dataForm.paixu = data.paixu
              this.dataForm.type = data.type
              this.dataForm.inviteCode = data.inviteCode
              this.dataForm.cover = data.cover
              this.dataForm.file = data.file || ''
            }
          }).catch((res) => {
            this.$message.error(res.message)
          })
        }
      })
    },
    // 点击图片上传
    modalPicTap(field) {
      const _this = this;
      this.$modalUpload(function (img) {
        _this.dataForm.cover = img[0].sattDir
      }, field, 'content')
    },
    // 视频上传
    handleVideoUpload() {
      const _this = this
      this.$modalUpload(function (files) {
        if (files && files.length > 0) {
          const file = files[0]
          _this.dataForm.mediaUrl = file.sattDir
          _this.dataForm.fileId = file.name
          _this.dataForm.fileSize = file.size || 0

          // 获取视频时长可能需要额外处理
          const video = document.createElement('video')
          video.onloadedmetadata = function() {
            _this.dataForm.duration = video.duration || 0
            _this.dataForm.frameRate = 0 // 默认帧率，实际获取可能需要后端处理
          }
          video.src = file.sattDir
        }
      }, '1', 'video')
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const submitApi = this.dataForm.id ? cmsCourseVideoUpdateApi : cmsCourseVideoAddApi
          submitApi({ ...this.dataForm }).then(() => {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch((res) => {
            this.$message.error(res.message)
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.upload-video {
  width: 100%;
}
.input-with-select {
  margin-bottom: 10px;
}
.preview-video {
  margin-top: 10px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 10px;
  text-align: center;
}

</style>