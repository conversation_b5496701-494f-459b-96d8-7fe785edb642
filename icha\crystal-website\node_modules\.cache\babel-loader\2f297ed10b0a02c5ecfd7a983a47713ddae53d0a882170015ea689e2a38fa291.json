{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone } from \"@/utils/index\";\nimport CourseApplyDialog from '@/components/CourseApplyDialog.vue';\nimport '../assets/css/common-headers.css'; // 导入头部共用样式\n\nexport default {\n  name: \"CourseDetailView\",\n  components: {\n    Layout,\n    CourseApplyDialog\n  },\n  data() {\n    return {\n      isMobilePhone: isMobilePhone(),\n      courseId: null,\n      courseDetail: {},\n      videoList: [],\n      currentVideoId: null,\n      currentVideo: {},\n      activeTab: 'videos',\n      // 默认显示视频列表标签页\n      videoBlob: null,\n      // 存储转换后的Blob对象\n      currentTime: 0,\n      // 当前播放时间（秒）\n      currentDuration: 0,\n      // 视频总时长（秒）\n      showApplyDialog: false,\n      applyCourse: null,\n      currentVideoIndex: 0 // 当前播放视频的索引\n    };\n  },\n\n  filters: {\n    // 将秒转换为时分秒格式\n    formatDuration(seconds) {\n      if (!seconds || isNaN(seconds)) return '00:00';\n\n      // 将秒数转为整数\n      seconds = Math.floor(Number(seconds));\n      const hours = Math.floor(seconds / 3600);\n      const minutes = Math.floor(seconds % 3600 / 60);\n      const remainingSeconds = seconds % 60;\n\n      // 格式化时间\n      if (hours > 0) {\n        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n      } else {\n        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n      }\n    }\n  },\n  mounted() {\n    this.$wxShare();\n    this.courseId = this.$route.query.id;\n    if (this.courseId) {\n      this.getCourseDetail();\n      this.getCourseVideos();\n    }\n\n    // 添加全局事件监听，防止视频被下载\n    window.addEventListener('keydown', this.preventSaveVideo);\n    document.addEventListener('contextmenu', this.preventContextMenu);\n  },\n  methods: {\n    // 防止通过键盘保存视频\n    preventSaveVideo(e) {\n      // 防止Ctrl+S, Cmd+S等保存快捷键\n      if ((e.ctrlKey || e.metaKey) && (e.key === 's' || e.key === 'S')) {\n        e.preventDefault();\n        return false;\n      }\n    },\n    // 防止右键菜单\n    preventContextMenu(e) {\n      // 如果是在视频播放区域右键，阻止默认行为\n      const videoContainer = document.querySelector('.video-player-container');\n      if (videoContainer && videoContainer.contains(e.target)) {\n        e.preventDefault();\n        return false;\n      }\n    },\n    // 获取课程详情\n    getCourseDetail() {\n      let userInfoStr = localStorage.getItem(\"userInfo\") || '{}';\n      let userInfo = JSON.parse(userInfoStr);\n      this.getRequest(`/cms/course/info?id=${this.courseId}&userId=${userInfo.uid || ''}`).then(resp => {\n        if (resp && resp.code == 200) {\n          this.courseDetail = resp.data || {};\n        }\n      });\n    },\n    // 获取课程视频列表\n    getCourseVideos() {\n      this.getRequest(`/cms/course/videos?id=${this.courseId}`).then(resp => {\n        if (resp && resp.code == 200) {\n          this.videoList = resp.data || [];\n\n          // 默认播放第一个视频\n          if (this.videoList.length > 0) {\n            this.playVideo(this.videoList[0], 0);\n          }\n        }\n      });\n    },\n    // 播放视频\n    playVideo(video, index) {\n      this.currentVideoId = video.id;\n      this.currentVideo = video;\n      if (typeof index !== 'undefined') {\n        this.currentVideoIndex = index;\n      }\n      // 请求接口获取视频详细信息\n      this.getRequest(`/cms/course/videos/info?id=${video.id}`).then(resp => {\n        if (resp && resp.code == 200) {\n          this.currentVideo = resp.data || {};\n        }\n      });\n    },\n    // 播放下一个视频\n    playNextVideo() {\n      const currentIndex = this.videoList.findIndex(v => v.id === this.currentVideoId);\n      if (currentIndex > -1 && currentIndex < this.videoList.length - 1) {\n        this.playVideo(this.videoList[currentIndex + 1]);\n      }\n    },\n    // 报名课程\n    enrollCourse() {\n      this.applyCourse = this.courseDetail;\n      this.showApplyDialog = true;\n    },\n    // 关闭报名弹窗\n    closeApplyDialog() {\n      this.showApplyDialog = false;\n      this.applyCourse = null;\n    },\n    // 报名成功\n    applySuccess() {\n      this.getCourseDetail();\n    },\n    // 监听视频播放时间更新\n    onTimeUpdate(e) {\n      if (e.target) {\n        this.currentTime = Math.floor(e.target.currentTime);\n        if (!this.currentDuration && e.target.duration) {\n          this.currentDuration = Math.floor(e.target.duration);\n        }\n      }\n    },\n    // 打开课程封面\n    openCourseCover() {\n      // 实现打开课程封面的逻辑\n    },\n    // 下载文件\n    downloadFile(fileUrl) {\n      if (!fileUrl) {\n        this.$message.error('文件链接不存在');\n        return;\n      }\n\n      // 判断是否登录\n      const userInfoStr = localStorage.getItem(\"userInfo\") || '{}';\n      const userInfo = JSON.parse(userInfoStr);\n      if (!userInfo.uid) {\n        this.$message.error('请先登录');\n        this.$router.push('/login');\n        return;\n      }\n      this.$message.success('正在下载文件...');\n      window.open(fileUrl, '_blank');\n    }\n  },\n  beforeDestroy() {\n    // 移除事件监听\n    window.removeEventListener('keydown', this.preventSaveVideo);\n    document.removeEventListener('contextmenu', this.preventContextMenu);\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "CourseApplyDialog", "name", "components", "data", "courseId", "courseDetail", "videoList", "currentVideoId", "currentVideo", "activeTab", "videoBlob", "currentTime", "currentDuration", "showApplyDialog", "applyCourse", "currentVideoIndex", "filters", "formatDuration", "seconds", "isNaN", "Math", "floor", "Number", "hours", "minutes", "remainingSeconds", "toString", "padStart", "mounted", "$wxShare", "$route", "query", "id", "getCourseDetail", "getCourseVideos", "window", "addEventListener", "preventSaveVideo", "document", "preventContextMenu", "methods", "e", "ctrl<PERSON>ey", "metaKey", "key", "preventDefault", "videoContainer", "querySelector", "contains", "target", "userInfoStr", "localStorage", "getItem", "userInfo", "JSON", "parse", "getRequest", "uid", "then", "resp", "code", "length", "playVideo", "video", "index", "playNextVideo", "currentIndex", "findIndex", "v", "enrollCourse", "closeApplyDialog", "applySuccess", "onTimeUpdate", "duration", "openCourseCover", "downloadFile", "fileUrl", "$message", "error", "$router", "push", "success", "open", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener"], "sources": ["src/views/courseDetail.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 移动端布局（无Layout包装） -->\r\n    <div v-if=\"isMobilePhone && courseDetail.isAttend\" class=\"mobile-layout\">\r\n      <!-- 固定在顶部的视频播放器 -->\r\n      <div class=\"mobile-fixed-video\" v-if=\"currentVideo\">\r\n        <!-- 返回按钮 -->\r\n        <div class=\"mobile-back-btn\" @click=\"$router.go(-1)\">\r\n          <i class=\"fa fa-arrow-left\"></i>\r\n        </div>\r\n        <video\r\n          ref=\"videoPlayer\"\r\n          :src=\"currentVideo.mediaUrl\"\r\n          :poster=\"currentVideo.cover || courseDetail.cover\"\r\n          controlsList=\"nodownload noremoteplayback\"\r\n          disablePictureInPicture\r\n          disableRemotePlayback\r\n          controls\r\n          :autoplay=\"false\"\r\n          class=\"mobile-video-player\"\r\n          @ended=\"playNextVideo\"\r\n          oncontextmenu=\"return false\"\r\n          @copy.prevent\r\n          @dragstart.prevent\r\n          @timeupdate=\"onTimeUpdate\"\r\n        ></video>\r\n        <div class=\"video-time-info\" v-if=\"currentDuration > 0\">\r\n          {{ currentTime | formatDuration }} / {{ currentDuration | formatDuration }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 固定Tab导航 -->\r\n      <div class=\"mobile-fixed-tabs\">\r\n        <div class=\"mobile-tabs\">\r\n          <div class=\"mobile-tab\" :class=\"{ 'active': activeTab === 'videos' }\" @click=\"activeTab = 'videos'\">\r\n            <i class=\"fa fa-list\"></i>\r\n            <span>目录</span>\r\n          </div>\r\n          <div class=\"mobile-tab\" :class=\"{ 'active': activeTab === 'info' }\" @click=\"activeTab = 'info'\">\r\n            <i class=\"fa fa-info-circle\"></i>\r\n            <span>介绍</span>\r\n          </div>\r\n          <div v-if=\"currentVideo && currentVideo.file\" class=\"mobile-tab\" :class=\"{ 'active': activeTab === 'download' }\" @click=\"activeTab = 'download'\">\r\n            <i class=\"fa fa-download\"></i>\r\n            <span>资料</span>\r\n          </div>\r\n          <div v-if=\"courseDetail.url\" class=\"mobile-tab\" :class=\"{ 'active': activeTab === 'exam' }\" @click=\"activeTab = 'exam'\">\r\n            <i class=\"fa fa-clipboard-list\"></i>\r\n            <span>考试</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可滚动的内容区域 -->\r\n      <div class=\"mobile-content-area\">\r\n        <!-- 视频列表 -->\r\n        <div v-if=\"activeTab === 'videos'\" class=\"mobile-video-list\" ref=\"videoListContainer\">\r\n          <div v-for=\"(video, index) in videoList\" :key=\"index\"\r\n               class=\"mobile-video-item\"\r\n               :class=\"{ 'active': currentVideoId === video.id }\"\r\n               @click=\"playVideo(video, index)\">\r\n            <div class=\"video-thumbnail\">\r\n              <img :src=\"video.cover || courseDetail.cover\" :alt=\"video.name\" />\r\n              <div class=\"play-overlay\">\r\n                <i class=\"fa fa-play\"></i>\r\n              </div>\r\n              <div class=\"video-duration\">{{ video.duration | formatDuration }}</div>\r\n            </div>\r\n            <div class=\"video-info\">\r\n              <h4>{{ index + 1 }}. {{ video.name }}</h4>\r\n              <p v-if=\"video.description\">{{ video.description }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 课程介绍 -->\r\n        <div v-if=\"activeTab === 'info'\" class=\"mobile-course-info\">\r\n          <div class=\"course-description\">\r\n            <div v-html=\"courseDetail.content\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 资料下载 -->\r\n        <div v-if=\"activeTab === 'download'\" class=\"mobile-download-section\">\r\n          <div v-if=\"currentVideo && currentVideo.file\" class=\"download-item\">\r\n            <div class=\"download-info\">\r\n              <i class=\"fa fa-file-pdf-o download-icon\"></i>\r\n              <div class=\"download-details\">\r\n                <h4>{{ currentVideo.name }} - 课程资料</h4>\r\n                <p>点击下载相关学习资料</p>\r\n              </div>\r\n            </div>\r\n            <button class=\"download-btn\" @click=\"downloadFile(currentVideo.file)\">\r\n              <i class=\"fa fa-download\"></i> 下载\r\n            </button>\r\n          </div>\r\n          <div v-else class=\"no-data\">\r\n            <p>当前视频暂无资料下载</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 课程考试 -->\r\n        <div v-if=\"activeTab === 'exam'\" class=\"mobile-exam-section\">\r\n          <iframe :src=\"courseDetail.url\" class=\"mobile-course-iframe\"></iframe>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 移动端未报名提示 -->\r\n    <div v-if=\"isMobilePhone && !courseDetail.isAttend\" class=\"mobile-no-attend-container\">\r\n      <!-- 返回按钮 -->\r\n      <div class=\"mobile-back-btn-fixed\" @click=\"$router.go(-1)\">\r\n        <i class=\"fa fa-arrow-left\"></i>\r\n      </div>\r\n      <div class=\"mobile-no-attend-tip\">\r\n        <i class=\"fa fa-lock\"></i>\r\n        <h3>需要报名观看</h3>\r\n        <p>¥{{ courseDetail.price }}</p>\r\n        <button class=\"crystal-btn\" @click=\"enrollCourse\">立即报名</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 移动端课程报名弹窗复用组件 -->\r\n    <CourseApplyDialog v-if=\"isMobilePhone\" :visible=\"showApplyDialog\" :course=\"applyCourse\" @close=\"closeApplyDialog\" @success=\"applySuccess\" />\r\n\r\n    <!-- 桌面端布局（使用Layout包装） -->\r\n    <Layout v-if=\"!isMobilePhone\">\r\n      <!-- 桌面端头部 -->\r\n      <div class=\"layout-container\" style=\"width: 100%\">\r\n        <!-- 课程详情页头部 -->\r\n        <div class=\"hero-header-section course-detail-header\">\r\n          <div class=\"hero-content\">\r\n            <h1 class=\"hero-title\"><i class=\"fa fa-play-circle fa-spin-pulse\"></i> {{ courseDetail.title }}</h1>\r\n            <p class=\"hero-subtitle\">{{ courseDetail.brief }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 桌面端内容 -->\r\n      <div class=\"section\">\r\n        <div class=\"container\" style=\"max-width: 1160px\">\r\n          <div class=\"course-detail-container\">\r\n            <!-- 课程信息导航栏 -->\r\n            <div class=\"course-nav-bar\">\r\n              <div class=\"course-basic-info\">\r\n                <div class=\"course-cover-img\" v-if=\"courseDetail.cover\" @click=\"openCourseCover\">\r\n                  <img :src=\"courseDetail.cover\" alt=\"课程封面\" />\r\n                  <div class=\"cover-mask\"></div>\r\n                  <div class=\"cover-label\">课程封面</div>\r\n                </div>\r\n                <div class=\"course-title-container\">\r\n                  <h2>{{ courseDetail.title }}</h2>\r\n                  <div class=\"course-stats\">\r\n                    <span><i class=\"fa fa-users\"></i> {{ courseDetail.studentCount || 300 }}人已学习</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"course-price-container\">\r\n                  <div class=\"course-price\">¥{{ courseDetail.price }}</div>\r\n                  <button v-if=\"!courseDetail.isAttend\" class=\"crystal-btn\" @click=\"enrollCourse\">立即报名</button>\r\n                  <button v-else class=\"crystal-btn-secondary\" disabled>已报名</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div v-if=\"courseDetail.isAttend\" class=\"course-content-area\">\r\n              <!-- 视频播放区域 -->\r\n              <div class=\"video-player-container\" v-if=\"currentVideo\">\r\n                <video\r\n                  ref=\"videoPlayer\"\r\n                  :src=\"currentVideo.mediaUrl\"\r\n                  :poster=\"currentVideo.cover || courseDetail.cover\"\r\n                  controlsList=\"nodownload noremoteplayback\"\r\n                  disablePictureInPicture\r\n                  disableRemotePlayback\r\n                  controls\r\n                  :autoplay=\"false\"\r\n                  class=\"video-player\"\r\n                  @ended=\"playNextVideo\"\r\n                  oncontextmenu=\"return false\"\r\n                  @copy.prevent\r\n                  @dragstart.prevent\r\n                  @timeupdate=\"onTimeUpdate\"\r\n                ></video>\r\n                <div class=\"video-time-info\" v-if=\"currentDuration > 0\">\r\n                  {{ currentTime | formatDuration }} / {{ currentDuration | formatDuration }}\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 视频与课程信息区域 -->\r\n              <div class=\"video-info-container\">\r\n                <!-- 桌面端Tab -->\r\n                <div class=\"tabs\">\r\n                  <div class=\"tab\" :class=\"{ 'active': activeTab === 'videos' }\" @click=\"activeTab = 'videos'\">\r\n                    <i class=\"fa fa-list\"></i> 课程目录\r\n                  </div>\r\n                  <div class=\"tab\" :class=\"{ 'active': activeTab === 'info' }\" @click=\"activeTab = 'info'\">\r\n                    <i class=\"fa fa-info-circle\"></i> 课程介绍\r\n                  </div>\r\n                  <div v-if=\"currentVideo && currentVideo.file\" class=\"tab\" :class=\"{ 'active': activeTab === 'download' }\" @click=\"activeTab = 'download'\">\r\n                    <i class=\"fa fa-download\"></i> 资料下载\r\n                  </div>\r\n                  <div v-if=\"courseDetail.url\" class=\"tab\" :class=\"{ 'active': activeTab === 'exam' }\" @click=\"activeTab = 'exam'\">\r\n                    <i class=\"fa fa-clipboard-list\"></i> 课程考试\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 视频列表标签页 -->\r\n                <div class=\"tab-content\" v-if=\"activeTab === 'videos'\">\r\n                  <div class=\"video-list\">\r\n                    <div v-for=\"(video, index) in videoList\" :key=\"index\" class=\"video-item\"\r\n                      :class=\"{ 'active': currentVideoId === video.id }\" @click=\"playVideo(video)\">\r\n                      <div class=\"video-item-index\">{{ index + 1 }}</div>\r\n                      <div class=\"video-item-info\">\r\n                        <h4>{{ video.name }}</h4>\r\n                        <p>{{ video.duration | formatDuration }}</p>\r\n                      </div>\r\n                      <div class=\"video-item-status\">\r\n                        <i class=\"fa\" :class=\"currentVideoId === video.id ? 'fa-pause' : 'fa-play'\"></i>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 无数据提示 -->\r\n                  <div v-if=\"videoList.length === 0\" class=\"no-data\">\r\n                    <p>暂无视频数据</p>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 课程信息标签页 -->\r\n                <div class=\"tab-content\" v-if=\"activeTab === 'info'\">\r\n                  <div class=\"course-description\">\r\n                    <div v-html=\"courseDetail.content\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 资料下载标签页 -->\r\n                <div class=\"tab-content\" v-if=\"activeTab === 'download'\">\r\n                  <div class=\"download-section\">\r\n                    <div v-if=\"currentVideo && currentVideo.file\" class=\"download-item\">\r\n                      <div class=\"download-info\">\r\n                        <i class=\"fa fa-file-pdf-o download-icon\"></i>\r\n                        <div class=\"download-details\">\r\n                          <h4>{{ currentVideo.name }} - 课程资料</h4>\r\n                          <p>点击下载相关学习资料</p>\r\n                        </div>\r\n                      </div>\r\n                      <button class=\"download-btn\" @click=\"downloadFile(currentVideo.file)\">\r\n                        <i class=\"fa fa-download\"></i> 下载\r\n                      </button>\r\n                    </div>\r\n                    <div v-else class=\"no-data\">\r\n                      <p>当前视频暂无资料下载</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 课程考试标签页 -->\r\n                <div class=\"tab-content\" v-if=\"activeTab === 'exam'\">\r\n                  <div >\r\n                    <!-- iframe -->\r\n                    <iframe :src=\"courseDetail.url\" class=\"course-iframe\"></iframe>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div v-else class=\"course-content-area no-attend-mask\">\r\n              <!-- 桌面端未报名提示 -->\r\n              <div class=\"no-attend-tip\">\r\n                <i class=\"fa fa-lock\"></i>\r\n                <p>请先报名后观看课程内容</p>\r\n                <button class=\"crystal-btn\" @click=\"enrollCourse\">立即报名</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <hr class=\"section_divider -narrow\">\r\n\r\n        <!-- 课程报名弹窗复用组件 -->\r\n        <CourseApplyDialog :visible=\"showApplyDialog\" :course=\"applyCourse\" @close=\"closeApplyDialog\" @success=\"applySuccess\" />\r\n      </div>\r\n    </Layout>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport CourseApplyDialog from '@/components/CourseApplyDialog.vue';\r\nimport '../assets/css/common-headers.css'; // 导入头部共用样式\r\n\r\nexport default {\r\n  name: \"CourseDetailView\",\r\n  components: { Layout, CourseApplyDialog },\r\n  data() {\r\n    return {\r\n      isMobilePhone: isMobilePhone(),\r\n      courseId: null,\r\n      courseDetail: {},\r\n      videoList: [],\r\n      currentVideoId: null,\r\n      currentVideo: {},\r\n      activeTab: 'videos', // 默认显示视频列表标签页\r\n      videoBlob: null, // 存储转换后的Blob对象\r\n      currentTime: 0,    // 当前播放时间（秒）\r\n      currentDuration: 0, // 视频总时长（秒）\r\n      showApplyDialog: false,\r\n      applyCourse: null,\r\n      currentVideoIndex: 0 // 当前播放视频的索引\r\n    }\r\n  },\r\n  filters: {\r\n    // 将秒转换为时分秒格式\r\n    formatDuration(seconds) {\r\n      if (!seconds || isNaN(seconds)) return '00:00';\r\n      \r\n      // 将秒数转为整数\r\n      seconds = Math.floor(Number(seconds));\r\n      \r\n      const hours = Math.floor(seconds / 3600);\r\n      const minutes = Math.floor((seconds % 3600) / 60);\r\n      const remainingSeconds = seconds % 60;\r\n      \r\n      // 格式化时间\r\n      if (hours > 0) {\r\n        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n      } else {\r\n        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$wxShare();\r\n    this.courseId = this.$route.query.id;\r\n    if (this.courseId) {\r\n      this.getCourseDetail();\r\n      this.getCourseVideos();\r\n    }\r\n\r\n    // 添加全局事件监听，防止视频被下载\r\n    window.addEventListener('keydown', this.preventSaveVideo);\r\n    document.addEventListener('contextmenu', this.preventContextMenu);\r\n  },\r\n  methods: {\r\n    // 防止通过键盘保存视频\r\n    preventSaveVideo(e) {\r\n      // 防止Ctrl+S, Cmd+S等保存快捷键\r\n      if ((e.ctrlKey || e.metaKey) && (e.key === 's' || e.key === 'S')) {\r\n        e.preventDefault();\r\n        return false;\r\n      }\r\n    },\r\n    \r\n    // 防止右键菜单\r\n    preventContextMenu(e) {\r\n      // 如果是在视频播放区域右键，阻止默认行为\r\n      const videoContainer = document.querySelector('.video-player-container');\r\n      if (videoContainer && videoContainer.contains(e.target)) {\r\n        e.preventDefault();\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 获取课程详情\r\n    getCourseDetail() {\r\n      let userInfoStr = localStorage.getItem(\"userInfo\") || '{}';\r\n      let userInfo = JSON.parse(userInfoStr);\r\n      this.getRequest(`/cms/course/info?id=${this.courseId}&userId=${userInfo.uid || ''}`).then(resp => {\r\n        if (resp && resp.code == 200) {\r\n          this.courseDetail = resp.data || {};\r\n        }\r\n      });\r\n    },\r\n\r\n    // 获取课程视频列表\r\n    getCourseVideos() {\r\n      this.getRequest(`/cms/course/videos?id=${this.courseId}`).then(resp => {\r\n        if (resp && resp.code == 200) {\r\n          this.videoList = resp.data || [];\r\n\r\n          // 默认播放第一个视频\r\n          if (this.videoList.length > 0) {\r\n            this.playVideo(this.videoList[0], 0);\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 播放视频\r\n    playVideo(video, index) {\r\n      this.currentVideoId = video.id;\r\n      this.currentVideo = video;\r\n      if (typeof index !== 'undefined') {\r\n        this.currentVideoIndex = index;\r\n      }\r\n      // 请求接口获取视频详细信息\r\n      this.getRequest(`/cms/course/videos/info?id=${video.id}`).then(resp => {\r\n        if (resp && resp.code == 200) {\r\n          this.currentVideo = resp.data || {};\r\n        }\r\n      });\r\n    },\r\n    // 播放下一个视频\r\n    playNextVideo() {\r\n      const currentIndex = this.videoList.findIndex(v => v.id === this.currentVideoId);\r\n      if (currentIndex > -1 && currentIndex < this.videoList.length - 1) {\r\n        this.playVideo(this.videoList[currentIndex + 1]);\r\n      }\r\n    },\r\n\r\n    // 报名课程\r\n    enrollCourse() {\r\n      this.applyCourse = this.courseDetail;\r\n      this.showApplyDialog = true;\r\n    },\r\n\r\n    // 关闭报名弹窗\r\n    closeApplyDialog() {\r\n      this.showApplyDialog = false;\r\n      this.applyCourse = null;\r\n    },\r\n\r\n    // 报名成功\r\n    applySuccess() {\r\n      this.getCourseDetail();\r\n    },\r\n\r\n    // 监听视频播放时间更新\r\n    onTimeUpdate(e) {\r\n      if (e.target) {\r\n        this.currentTime = Math.floor(e.target.currentTime);\r\n        if (!this.currentDuration && e.target.duration) {\r\n          this.currentDuration = Math.floor(e.target.duration);\r\n        }\r\n      }\r\n    },\r\n\r\n    // 打开课程封面\r\n    openCourseCover() {\r\n      // 实现打开课程封面的逻辑\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(fileUrl) {\r\n      if (!fileUrl) {\r\n        this.$message.error('文件链接不存在');\r\n        return;\r\n      }\r\n\r\n      // 判断是否登录\r\n      const userInfoStr = localStorage.getItem(\"userInfo\") || '{}';\r\n      const userInfo = JSON.parse(userInfoStr);\r\n      if (!userInfo.uid) {\r\n        this.$message.error('请先登录');\r\n        this.$router.push('/login');\r\n        return;\r\n      }\r\n\r\n      this.$message.success('正在下载文件...');\r\n      window.open(fileUrl, '_blank');\r\n    },\r\n\r\n\r\n  },\r\n  beforeDestroy() {\r\n    // 移除事件监听\r\n    window.removeEventListener('keydown', this.preventSaveVideo);\r\n    document.removeEventListener('contextmenu', this.preventContextMenu);\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n/* 移动端布局 */\r\n.mobile-layout {\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: #f8f9fa;\r\n}\r\n\r\n/* 移动端固定视频播放器 */\r\n.mobile-fixed-video {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 1000;\r\n  background: black;\r\n}\r\n\r\n.mobile-video-player {\r\n  width: 100%;\r\n  height: 250px;\r\n  display: block;\r\n}\r\n\r\n/* 移动端返回按钮 */\r\n.mobile-back-btn {\r\n  position: absolute;\r\n  top: 15px;\r\n  left: 15px;\r\n  z-index: 1001;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.mobile-back-btn:hover {\r\n  background: rgba(0, 0, 0, 0.8);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.mobile-back-btn i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 移动端未报名容器 */\r\n.mobile-no-attend-container {\r\n  height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #f8f9fa;\r\n  padding: 20px;\r\n  position: relative;\r\n}\r\n\r\n/* 移动端固定返回按钮 */\r\n.mobile-back-btn-fixed {\r\n  position: fixed;\r\n  top: 20px;\r\n  left: 20px;\r\n  z-index: 1001;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(123, 67, 151, 0.9);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 4px 12px rgba(123, 67, 151, 0.3);\r\n}\r\n\r\n.mobile-back-btn-fixed:hover {\r\n  background: rgba(123, 67, 151, 1);\r\n  transform: scale(1.1);\r\n  box-shadow: 0 6px 16px rgba(123, 67, 151, 0.4);\r\n}\r\n\r\n.mobile-back-btn-fixed i {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 课程详情页头部样式 */\r\n.course-detail-header {\r\n  background-image: url('https://img.freepik.com/free-photo/abstract-luxury-gradient-blue-background-smooth-dark-blue-with-black-vignette-studio-banner_1258-63452.jpg') !important;\r\n}\r\n\r\n/* 课程信息导航栏 */\r\n.course-nav-bar {\r\n  background: white;\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.course-basic-info {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  gap: 30px;\r\n}\r\n\r\n.course-title-container h2 {\r\n  font-size: 22px;\r\n  color: #3a2c58;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.course-stats span {\r\n  display: inline-block;\r\n  margin-right: 20px;\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.course-stats i {\r\n  margin-right: 8px;\r\n  color: #7b4397;\r\n}\r\n\r\n.course-price-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.course-price {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #dc2430;\r\n}\r\n\r\n/* 课程内容区域 */\r\n.course-content-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n/* 移动端固定Tab */\r\n.mobile-fixed-tabs {\r\n  position: fixed;\r\n  top: 250px; /* 视频播放器高度 */\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  background: white;\r\n  border-bottom: 1px solid #eee;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.mobile-tabs {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  padding: 0;\r\n}\r\n\r\n.mobile-tab {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 12px 8px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  color: #666;\r\n  transition: all 0.3s;\r\n  font-size: 12px;\r\n}\r\n\r\n.mobile-tab i {\r\n  font-size: 16px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.mobile-tab.active {\r\n  color: #7b4397;\r\n}\r\n\r\n.mobile-tab.active:after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 20px;\r\n  height: 3px;\r\n  background: linear-gradient(135deg, #7b4397, #dc2430);\r\n  border-radius: 2px;\r\n}\r\n\r\n/* 移动端内容区域 */\r\n.mobile-content-area {\r\n  flex: 1;\r\n  margin-top: 310px; /* 视频播放器高度 + Tab高度 */\r\n  overflow: hidden;\r\n}\r\n\r\n/* 移动端视频列表 */\r\n.mobile-video-list {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 0;\r\n  scroll-behavior: smooth;\r\n}\r\n\r\n.mobile-video-item {\r\n  display: flex;\r\n  padding: 15px;\r\n  background: white;\r\n  border-bottom: 1px solid #eee;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.mobile-video-item:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.mobile-video-item.active {\r\n  background: linear-gradient(135deg, rgba(123, 67, 151, 0.05), rgba(220, 36, 48, 0.05));\r\n  border-left: 4px solid #7b4397;\r\n}\r\n\r\n.video-thumbnail {\r\n  position: relative;\r\n  width: 120px;\r\n  height: 80px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  margin-right: 15px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.video-thumbnail img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.play-overlay {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 30px;\r\n  height: 30px;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 12px;\r\n}\r\n\r\n.video-duration {\r\n  position: absolute;\r\n  bottom: 5px;\r\n  right: 5px;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  color: white;\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.video-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n\r\n.video-info h4 {\r\n  margin: 0 0 8px 0;\r\n  font-size: 16px;\r\n  color: #333;\r\n  line-height: 1.4;\r\n}\r\n\r\n.video-info p {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 移动端课程介绍 */\r\n.mobile-course-info {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: white;\r\n}\r\n\r\n/* 移动端下载区域 */\r\n.mobile-download-section {\r\n  height: 100%;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  background: white;\r\n}\r\n\r\n/* 移动端考试区域 */\r\n.mobile-exam-section {\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.mobile-course-iframe {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: none;\r\n}\r\n\r\n/* 视频播放器 */\r\n.video-player-container {\r\n  background: black;\r\n  overflow: hidden;\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.video-player-container::before {\r\n  /* content: \"禁止下载，请在当前页面观看\"; */\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  color: white;\r\n  padding: 5px 0;\r\n  text-align: center;\r\n  font-size: 14px;\r\n  z-index: 1;\r\n  pointer-events: none;\r\n  opacity: 0;\r\n  transition: opacity 0.3s;\r\n}\r\n\r\n.video-player-container:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n.video-player {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n\r\n/* 视频信息区域 */\r\n.video-info-container {\r\n  background: white;\r\n  border-radius: 15px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n  min-height: 600px; /* 固定最小高度，避免切换Tab时高度变化 */\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 标签页 */\r\n.tabs {\r\n  display: flex;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.tab {\r\n  padding: 15px 25px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  font-weight: 500;\r\n  color: #666;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.tab.active {\r\n  color: #7b4397;\r\n}\r\n\r\n.tab.active:after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 3px;\r\n  background: linear-gradient(135deg, #7b4397, #dc2430);\r\n}\r\n\r\n.tab-content {\r\n  padding: 20px;\r\n  flex: 1; /* 占据剩余空间 */\r\n  overflow: hidden; /* 防止内容溢出 */\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 视频列表 */\r\n.video-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px; /* 减小间距 */\r\n  max-height: 480px; /* 固定最大高度 */\r\n  overflow-y: auto; /* 添加滚动 */\r\n  padding-right: 8px; /* 为滚动条留出空间 */\r\n  flex: 1;\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.video-list::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.video-list::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.video-list::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(135deg, #7b4397, #dc2430);\r\n  border-radius: 3px;\r\n}\r\n\r\n.video-list::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(135deg, #dc2430, #7b4397);\r\n}\r\n\r\n.video-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px; /* 减小内边距 */\r\n  border-radius: 8px; /* 减小圆角 */\r\n  background: #f8f9fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  min-height: 60px; /* 固定最小高度 */\r\n  flex-shrink: 0; /* 防止压缩 */\r\n}\r\n\r\n.video-item:hover {\r\n  background: #f1f1f1;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.video-item.active {\r\n  background: linear-gradient(135deg, rgba(123, 67, 151, 0.1), rgba(220, 36, 48, 0.1));\r\n  border-left: 4px solid #7b4397;\r\n}\r\n\r\n.video-item-index {\r\n  width: 32px; /* 减小尺寸 */\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #7b4397, #dc2430);\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 12px; /* 减小间距 */\r\n  font-weight: bold;\r\n  font-size: 14px; /* 减小字体 */\r\n  flex-shrink: 0; /* 防止压缩 */\r\n}\r\n\r\n.video-item-info {\r\n  flex: 1;\r\n}\r\n\r\n.video-item-info h4 {\r\n  font-size: 15px; /* 稍微减小字体 */\r\n  color: #333;\r\n  margin-bottom: 4px; /* 减小间距 */\r\n  line-height: 1.3; /* 调整行高 */\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap; /* 防止标题过长 */\r\n}\r\n\r\n.video-item-info p {\r\n  font-size: 13px; /* 减小字体 */\r\n  color: #666;\r\n  margin: 0;\r\n  line-height: 1.2;\r\n}\r\n\r\n.video-item-status {\r\n  color: #7b4397;\r\n  font-size: 16px; /* 减小图标尺寸 */\r\n  flex-shrink: 0; /* 防止压缩 */\r\n}\r\n\r\n/* 课程描述 */\r\n.course-description {\r\n  color: #555;\r\n  line-height: 1.6;\r\n  max-height: 480px; /* 与视频列表保持一致的高度 */\r\n  overflow-y: auto; /* 添加滚动 */\r\n  padding-right: 8px; /* 为滚动条留出空间 */\r\n  flex: 1;\r\n  /deep/ img {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n/* 课程描述滚动条样式 */\r\n.course-description::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.course-description::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.course-description::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(135deg, #7b4397, #dc2430);\r\n  border-radius: 3px;\r\n}\r\n\r\n.course-description::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(135deg, #dc2430, #7b4397);\r\n}\r\n.course-iframe {\r\n  width: 100%;\r\n  height: 480px; /* 与其他内容保持一致的高度 */\r\n  border: none;\r\n  border-radius: 8px;\r\n  flex: 1;\r\n}\r\n\r\n.course-cover-img {\r\n  position: relative;\r\n  width: 140px;\r\n  min-width: 100px;\r\n  max-width: 180px;\r\n  height: 100px;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 6px 24px rgba(0,0,0,0.13);\r\n  margin-right: 32px;\r\n  background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: transform 0.25s cubic-bezier(.4,2,.6,1);\r\n  border: 2.5px solid #7b4397;\r\n  cursor: pointer;\r\n}\r\n.course-cover-img img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  display: block;\r\n  border-radius: 14px;\r\n  z-index: 1;\r\n  transition: transform 0.25s cubic-bezier(.4,2,.6,1);\r\n}\r\n.course-cover-img:hover {\r\n  transform: scale(1.045);\r\n  box-shadow: 0 10px 32px rgba(123,67,151,0.18);\r\n}\r\n.course-cover-img .cover-mask {\r\n  position: absolute;\r\n  left: 0; top: 0; right: 0; bottom: 0;\r\n  background: linear-gradient(135deg,rgba(123,67,151,0.08),rgba(220,36,48,0.08));\r\n  z-index: 2;\r\n  pointer-events: none;\r\n}\r\n.course-cover-img .cover-label {\r\n  position: absolute;\r\n  right: 8px;\r\n  bottom: 8px;\r\n  background: rgba(123,67,151,0.85);\r\n  color: #fff;\r\n  font-size: 12px;\r\n  padding: 2px 10px;\r\n  border-radius: 10px;\r\n  z-index: 3;\r\n  letter-spacing: 1px;\r\n  box-shadow: 0 2px 8px rgba(123,67,151,0.10);\r\n  user-select: none;\r\n  pointer-events: none;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 资料下载样式 */\r\n.download-section {\r\n  padding: 0; /* 移除内边距，由tab-content统一控制 */\r\n  max-height: 480px; /* 与其他内容保持一致的高度 */\r\n  overflow-y: auto; /* 添加滚动 */\r\n  flex: 1;\r\n}\r\n\r\n/* 下载区域滚动条样式 */\r\n.download-section::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.download-section::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.download-section::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(135deg, #7b4397, #dc2430);\r\n  border-radius: 3px;\r\n}\r\n\r\n.download-section::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(135deg, #dc2430, #7b4397);\r\n}\r\n\r\n.download-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 20px;\r\n  background: #f8f9fa; /* 与视频项保持一致的背景色 */\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  margin-bottom: 15px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.download-item:hover {\r\n  background: #f1f1f1;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.download-info {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.download-icon {\r\n  font-size: 32px;\r\n  color: #dc2430;\r\n  margin-right: 15px;\r\n}\r\n\r\n.download-details h4 {\r\n  margin: 0 0 5px 0;\r\n  color: #333;\r\n  font-size: 16px;\r\n}\r\n\r\n.download-details p {\r\n  margin: 0;\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.download-btn {\r\n  background: linear-gradient(135deg, #7b4397, #dc2430);\r\n  color: white;\r\n  border: none;\r\n  padding: 10px 20px;\r\n  border-radius: 20px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.download-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(123, 67, 151, 0.3);\r\n}\r\n\r\n/* 移动端简化的未报名提示 */\r\n.mobile-no-attend-tip {\r\n  text-align: center;\r\n  color: #333;\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 40px 20px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n  max-width: 300px;\r\n  width: 100%;\r\n}\r\n\r\n.mobile-no-attend-tip i {\r\n  font-size: 48px;\r\n  color: #7b4397;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.mobile-no-attend-tip h3 {\r\n  font-size: 20px;\r\n  margin: 0 0 15px 0;\r\n  color: #333;\r\n}\r\n\r\n.mobile-no-attend-tip p {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #dc2430;\r\n  margin: 15px 0 25px 0;\r\n}\r\n\r\n.no-data {\r\n  text-align: center;\r\n  padding: 60px 20px; /* 增加内边距 */\r\n  color: #888;\r\n  flex: 1;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-direction: column;\r\n}\r\n\r\n.no-data p {\r\n  margin: 0;\r\n  font-size: 16px;\r\n}\r\n\r\n.no-data i {\r\n  font-size: 48px;\r\n  color: #ddd;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 响应式适配 */\r\n@media (max-width: 992px) {\r\n  .course-basic-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n\r\n  .course-price-container {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .video-player {\r\n    height: 400px;\r\n  }\r\n\r\n  .course-cover-img {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  /* 桌面端在移动设备上的样式调整 */\r\n  .section {\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .course-detail-container {\r\n    margin: 0;\r\n  }\r\n\r\n  .tabs {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .tab {\r\n    padding: 12px 15px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .video-player {\r\n    height: 250px;\r\n    border-radius: 0;\r\n  }\r\n\r\n  .video-player-container {\r\n    border-radius: 8px;\r\n    margin-bottom: 15px;\r\n  }\r\n\r\n  .course-content-area {\r\n    gap: 15px;\r\n  }\r\n\r\n  .video-watermark {\r\n    font-size: 14px;\r\n    bottom: 40px;\r\n    right: 10px;\r\n  }\r\n\r\n  .tab-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  .video-info-container {\r\n    border-radius: 8px;\r\n  }\r\n\r\n  .download-item {\r\n    margin: 0 0 15px 0;\r\n    border-radius: 8px;\r\n  }\r\n\r\n  /* 移动端布局样式 */\r\n  .mobile-layout {\r\n    height: 100vh;\r\n  }\r\n\r\n  .mobile-video-player {\r\n    height: 200px; /* 在小屏幕上稍微减小高度 */\r\n  }\r\n\r\n  .mobile-fixed-tabs {\r\n    top: 200px; /* 对应调整Tab位置 */\r\n  }\r\n\r\n  .mobile-content-area {\r\n    margin-top: 260px; /* 对应调整内容区域位置 */\r\n  }\r\n\r\n  .video-thumbnail {\r\n    width: 100px;\r\n    height: 70px;\r\n  }\r\n\r\n  .video-info h4 {\r\n    font-size: 15px;\r\n  }\r\n\r\n  .video-info p {\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n/* 按钮样式 */\r\n.crystal-btn {\r\n  background: linear-gradient(135deg, #7b4397, #dc2430);\r\n  color: white;\r\n  border: none;\r\n  padding: 10px 20px;\r\n  border-radius: 25px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 4px 15px rgba(123, 67, 151, 0.3);\r\n}\r\n\r\n.crystal-btn:hover {\r\n  background: linear-gradient(135deg, #dc2430, #7b4397);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(123, 67, 151, 0.4);\r\n}\r\n\r\n.crystal-btn i {\r\n  margin-right: 8px;\r\n}\r\n\r\n.video-watermark {\r\n  position: absolute;\r\n  bottom: 60px;\r\n  right: 20px;\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-size: 18px;\r\n  pointer-events: none;\r\n  user-select: none;\r\n  z-index: 10;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n  opacity: 0.7;\r\n}\r\n\r\n.video-time-info {\r\n  position: absolute;\r\n  bottom: 10px;\r\n  right: 15px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 14px;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  padding: 3px 8px;\r\n  border-radius: 4px;\r\n  z-index: 5;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 未报名遮罩提示 */\r\n.no-attend-mask {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 300px;\r\n}\r\n.no-attend-tip {\r\n  text-align: center;\r\n  color: #888;\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 18px rgba(0,0,0,0.08);\r\n  padding: 60px 40px 40px 40px;\r\n  max-width: 400px;\r\n}\r\n.no-attend-tip i {\r\n  font-size: 48px;\r\n  color: #7b4397;\r\n  margin-bottom: 18px;\r\n}\r\n.no-attend-tip p {\r\n  font-size: 18px;\r\n  margin-bottom: 28px;\r\n}\r\n\r\n.crystal-btn-secondary {\r\n  background: linear-gradient(135deg, #2bff00, #1900ff);\r\n  color: white;\r\n  border: none;\r\n  padding: 10px 20px;\r\n  border-radius: 25px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 4px 15px rgba(180, 180, 180, 0.18);\r\n  margin-left: 10px;\r\n  animation: none;\r\n}\r\n.crystal-btn-secondary:hover {\r\n  background: linear-gradient(135deg, #2bff00, #1900ff);\r\n  color: white;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(180, 180, 180, 0.28);\r\n}\r\n\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8RA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA,OAAAC,iBAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAJ,MAAA;IAAAE;EAAA;EACAG,KAAA;IACA;MACAJ,aAAA,EAAAA,aAAA;MACAK,QAAA;MACAC,YAAA;MACAC,SAAA;MACAC,cAAA;MACAC,YAAA;MACAC,SAAA;MAAA;MACAC,SAAA;MAAA;MACAC,WAAA;MAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MACAC,WAAA;MACAC,iBAAA;IACA;EACA;;EACAC,OAAA;IACA;IACAC,eAAAC,OAAA;MACA,KAAAA,OAAA,IAAAC,KAAA,CAAAD,OAAA;;MAEA;MACAA,OAAA,GAAAE,IAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAJ,OAAA;MAEA,MAAAK,KAAA,GAAAH,IAAA,CAAAC,KAAA,CAAAH,OAAA;MACA,MAAAM,OAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAH,OAAA;MACA,MAAAO,gBAAA,GAAAP,OAAA;;MAEA;MACA,IAAAK,KAAA;QACA,UAAAA,KAAA,CAAAG,QAAA,GAAAC,QAAA,YAAAH,OAAA,CAAAE,QAAA,GAAAC,QAAA,YAAAF,gBAAA,CAAAC,QAAA,GAAAC,QAAA;MACA;QACA,UAAAH,OAAA,CAAAE,QAAA,GAAAC,QAAA,YAAAF,gBAAA,CAAAC,QAAA,GAAAC,QAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;IACA,KAAAzB,QAAA,QAAA0B,MAAA,CAAAC,KAAA,CAAAC,EAAA;IACA,SAAA5B,QAAA;MACA,KAAA6B,eAAA;MACA,KAAAC,eAAA;IACA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,iBAAAC,gBAAA;IACAC,QAAA,CAAAF,gBAAA,qBAAAG,kBAAA;EACA;EACAC,OAAA;IACA;IACAH,iBAAAI,CAAA;MACA;MACA,KAAAA,CAAA,CAAAC,OAAA,IAAAD,CAAA,CAAAE,OAAA,MAAAF,CAAA,CAAAG,GAAA,YAAAH,CAAA,CAAAG,GAAA;QACAH,CAAA,CAAAI,cAAA;QACA;MACA;IACA;IAEA;IACAN,mBAAAE,CAAA;MACA;MACA,MAAAK,cAAA,GAAAR,QAAA,CAAAS,aAAA;MACA,IAAAD,cAAA,IAAAA,cAAA,CAAAE,QAAA,CAAAP,CAAA,CAAAQ,MAAA;QACAR,CAAA,CAAAI,cAAA;QACA;MACA;IACA;IAEA;IACAZ,gBAAA;MACA,IAAAiB,WAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,WAAA;MACA,KAAAM,UAAA,6BAAApD,QAAA,WAAAiD,QAAA,CAAAI,GAAA,UAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAvD,YAAA,GAAAsD,IAAA,CAAAxD,IAAA;QACA;MACA;IACA;IAEA;IACA+B,gBAAA;MACA,KAAAsB,UAAA,+BAAApD,QAAA,IAAAsD,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAtD,SAAA,GAAAqD,IAAA,CAAAxD,IAAA;;UAEA;UACA,SAAAG,SAAA,CAAAuD,MAAA;YACA,KAAAC,SAAA,MAAAxD,SAAA;UACA;QACA;MACA;IACA;IAEA;IACAwD,UAAAC,KAAA,EAAAC,KAAA;MACA,KAAAzD,cAAA,GAAAwD,KAAA,CAAA/B,EAAA;MACA,KAAAxB,YAAA,GAAAuD,KAAA;MACA,WAAAC,KAAA;QACA,KAAAjD,iBAAA,GAAAiD,KAAA;MACA;MACA;MACA,KAAAR,UAAA,+BAAAO,KAAA,CAAA/B,EAAA,IAAA0B,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAApD,YAAA,GAAAmD,IAAA,CAAAxD,IAAA;QACA;MACA;IACA;IACA;IACA8D,cAAA;MACA,MAAAC,YAAA,QAAA5D,SAAA,CAAA6D,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAApC,EAAA,UAAAzB,cAAA;MACA,IAAA2D,YAAA,SAAAA,YAAA,QAAA5D,SAAA,CAAAuD,MAAA;QACA,KAAAC,SAAA,MAAAxD,SAAA,CAAA4D,YAAA;MACA;IACA;IAEA;IACAG,aAAA;MACA,KAAAvD,WAAA,QAAAT,YAAA;MACA,KAAAQ,eAAA;IACA;IAEA;IACAyD,iBAAA;MACA,KAAAzD,eAAA;MACA,KAAAC,WAAA;IACA;IAEA;IACAyD,aAAA;MACA,KAAAtC,eAAA;IACA;IAEA;IACAuC,aAAA/B,CAAA;MACA,IAAAA,CAAA,CAAAQ,MAAA;QACA,KAAAtC,WAAA,GAAAS,IAAA,CAAAC,KAAA,CAAAoB,CAAA,CAAAQ,MAAA,CAAAtC,WAAA;QACA,UAAAC,eAAA,IAAA6B,CAAA,CAAAQ,MAAA,CAAAwB,QAAA;UACA,KAAA7D,eAAA,GAAAQ,IAAA,CAAAC,KAAA,CAAAoB,CAAA,CAAAQ,MAAA,CAAAwB,QAAA;QACA;MACA;IACA;IAEA;IACAC,gBAAA;MACA;IAAA,CACA;IAEA;IACAC,aAAAC,OAAA;MACA,KAAAA,OAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,MAAA5B,WAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,MAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,WAAA;MACA,KAAAG,QAAA,CAAAI,GAAA;QACA,KAAAoB,QAAA,CAAAC,KAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;QACA;MACA;MAEA,KAAAH,QAAA,CAAAI,OAAA;MACA9C,MAAA,CAAA+C,IAAA,CAAAN,OAAA;IACA;EAGA;EACAO,cAAA;IACA;IACAhD,MAAA,CAAAiD,mBAAA,iBAAA/C,gBAAA;IACAC,QAAA,CAAA8C,mBAAA,qBAAA7C,kBAAA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}