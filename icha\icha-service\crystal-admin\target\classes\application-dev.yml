
spring:
  servlet:
    multipart:
      max-file-size: 50MB #设置单个文件大小
      max-request-size: 50MB #设置单次请求文件的总大小
  application:
    name: crystal-admin #这个很重要，这在以后的服务与服务之间相互调用一般都是根据这个name
  jackson:
    locale: zh_CN
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  #  数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: icha
    password: Icha@123

  redis:
    host: ************* # Redis服务器地址
    database: 8 # Redis数据库索引（默认为0）
    port: 6379 # Redis服务器连接端口
    password: <PERSON><PERSON><PERSON><PERSON>@888 # Redis服务器连接密码（默认为空）
    timeout: 30000 # 连接超时时间（毫秒）
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
        time-between-eviction-runs: -1 #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1

