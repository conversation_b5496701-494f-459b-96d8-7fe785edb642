package com.crystal.common.enums;

/**
 * 用户作答状态枚举类
 * 0-未提交，1-已提交，2-已通过，3-未通过，4-已超时，5-作废
 */
public enum ExamActivityUserStatusEnum {
    NOT_COMMIT(0, "未提交"),
    COMMIT(1, "已提交"),
    PASS(2, "已通过"),
    NO_PASS(3, "未通过"),
    EXPIRED(4, "已超时"),
    CANCEL(5, "作废");

    private Integer code;
    private String msg;

    ExamActivityUserStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
    public static String getValueByCode(int code) {
        for (ExamActivityUserStatusEnum typeEnum : ExamActivityUserStatusEnum.values()) {
            if (typeEnum.code == code) {
                return typeEnum.msg;
            }
        }
        throw new IllegalArgumentException("No element matches " + code);
    }
    public static Integer getValueByMsg(String code) {
        for (ExamActivityUserStatusEnum typeEnum : ExamActivityUserStatusEnum.values()) {
            if (typeEnum.msg.equals(code)) {
                return typeEnum.code;
            }
        }
        throw new IllegalArgumentException("No element matches " + code);
    }
}
