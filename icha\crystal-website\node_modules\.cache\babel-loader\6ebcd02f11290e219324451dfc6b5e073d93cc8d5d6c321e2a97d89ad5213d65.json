{"ast": null, "code": "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone } from \"@/utils/index\";\nimport '../assets/css/common-headers.css'; // 导入头部共用样式f\n\nexport default {\n  name: \"AboutView\",\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      aboutLshiminglogo: ['fas fa-heart', 'fas fa-certificate', 'fas fa-globe-asia', 'fas fa-microscope', 'fas fa-leaf'],\n      isMobilePhone: isMobilePhone(),\n      companyInfo: {},\n      designers: [],\n      aboutXuanyan: '',\n      aboutYuanjing: '',\n      aboutHexin: [],\n      aboutLshiming: [],\n      aboutLogo: '',\n      aboutText: '',\n      aboutDesc: '',\n      aboutTitle: ''\n    };\n  },\n  mounted() {\n    // this.getCompanyInfo()\n    this.$wxShare();\n    this.getAboutConfig();\n    // this.getDesigners()\n  },\n\n  methods: {\n    getAboutConfig() {\n      this.getRequest(\"/cms/config/about\").then(resp => {\n        this.aboutXuanyan = resp.data.aboutXuanyan;\n        this.aboutYuanjing = resp.data.aboutYuanjing;\n        this.aboutHexin = resp.data.aboutHexin;\n        this.aboutLshiming = resp.data.aboutLshiming;\n        this.aboutLogo = resp.data.aboutLogo;\n        this.aboutText = resp.data.aboutText;\n        this.aboutDesc = resp.data.aboutDesc;\n        this.aboutTitle = resp.data.aboutTitle;\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "name", "components", "data", "aboutLshiminglogo", "companyInfo", "designers", "aboutXuanyan", "aboutYuanjing", "aboutHexin", "aboutLshiming", "aboutLogo", "aboutText", "aboutDesc", "aboutTitle", "mounted", "$wxShare", "getAboutConfig", "methods", "getRequest", "then", "resp"], "sources": ["src/views/AboutView.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t<div class=\"about-container\">\r\n\t\t\t<!-- 美化后的页面头部 -->\r\n\t\t\t<div class=\"hero-header-section about-header\">\r\n\t\t\t\t<div class=\"hero-content\">\r\n\t\t\t\t\t<h1 class=\"hero-title\"><i class=\"fas fa-info-circle fa-spin-pulse\"></i> {{aboutTitle}}</h1>\r\n\t\t\t\t\t<p class=\"hero-subtitle\">{{aboutDesc}}</p>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<!-- 协会简介 -->\r\n\t\t\t<section class=\"about-section intro-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">国际水晶疗愈协会</h2>\r\n\t\t\t\t\t<div class=\"intro-content\">\r\n\t\t\t\t\t\t<div class=\"intro-text\">\r\n\t\t\t\t\t\t\t<p v-for=\"item in aboutText\" :key=\"item\">{{item}}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div v-if=\"!isMobilePhone\" class=\"intro-decoration\">\r\n\t\t\t\t\t\t\t<img :src=\"aboutLogo\" alt=\"协会logo\" class=\"crystal-shape\">\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section>\r\n\r\n\t\t\t<!-- 我们的使命 -->\r\n\t\t\t<section class=\"about-section mission-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">我们的使命</h2>\r\n\t\t\t\t\t<div class=\"mission-cards\">\r\n\t\t\t\t\t\t<div class=\"mission-card\" v-for=\"(item, index) in aboutHexin\" :key=\"index\">\r\n\t\t\t\t\t\t\t<div class=\"card-icon\">\r\n\t\t\t\t\t\t\t\t<i :class=\"aboutLshiminglogo[index]\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"card-content\">\r\n\t\t\t\t\t\t\t\t<p>{{item}}</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section>\r\n\r\n\t\t\t<!-- 核心服务 -->\r\n\t\t\t<section class=\"about-section services-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">核心服务</h2>\r\n\t\t\t\t\t<div class=\"services-grid\">\r\n\t\t\t\t\t\t<div class=\"service-item\">\r\n\t\t\t\t\t\t\t<div class=\"service-icon service-icon-1\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-certificate\" style=\"font-size: 36px; color: white;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>疗愈师认证体系</h3>\r\n\t\t\t\t\t\t\t<p>从初阶到高级，系统培训水晶疗愈技能，颁发国际认可证书</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"service-item\">\r\n\t\t\t\t\t\t\t<div class=\"service-icon service-icon-2\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-globe-asia\" style=\"font-size: 36px; color: white;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>全球讲师网络</h3>\r\n\t\t\t\t\t\t\t<p>邀请国际知名水晶疗愈导师授课，提供多语言、多文化的教学支持</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"service-item\">\r\n\t\t\t\t\t\t\t<div class=\"service-icon service-icon-3\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-gem\" style=\"font-size: 36px; color: white;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>疗愈工具开发</h3>\r\n\t\t\t\t\t\t\t<p>联合各地工作坊开发疗愈水晶阵、冥想水晶套组等专业工具</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"service-item\">\r\n\t\t\t\t\t\t\t<div class=\"service-icon service-icon-4\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-users\" style=\"font-size: 36px; color: white;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>全球社群连接</h3>\r\n\t\t\t\t\t\t\t<p>举办线上线下疗愈论坛、静心营、年度水晶大会等活动</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"service-item\">\r\n\t\t\t\t\t\t\t<div class=\"service-icon service-icon-5\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-book-open\" style=\"font-size: 36px; color: white;\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>研究与出版</h3>\r\n\t\t\t\t\t\t\t<p>推动水晶能量疗愈在心理学、能量医学、整合医学领域的交叉研究</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section>\r\n\r\n\t\t\t<!-- 协会愿景 -->\r\n\t\t\t<section class=\"about-section vision-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">协会愿景</h2>\r\n\t\t\t\t\t<div class=\"vision-content\">\r\n\t\t\t\t\t\t<div class=\"vision-statement\">\r\n\t\t\t\t\t\t\t<p>{{aboutYuanjing}}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"vision-beliefs\">\r\n\t\t\t\t\t\t\t<h3>我们相信：</h3>\r\n\t\t\t\t\t\t\t<div class=\"beliefs-list\">\r\n\t\t\t\t\t\t\t\t<div class=\"belief-item\">\r\n\t\t\t\t\t\t\t\t\t<span class=\"crystal-icon\"><i class=\"fas fa-gem\"></i></span>\r\n\t\t\t\t\t\t\t\t\t<p>每一块水晶都是地球的智慧化身</p>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"belief-item\">\r\n\t\t\t\t\t\t\t\t\t<span class=\"crystal-icon\"><i class=\"fas fa-spa\"></i></span>\r\n\t\t\t\t\t\t\t\t\t<p>每一次疗愈，都是向内走的觉醒</p>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"belief-item\">\r\n\t\t\t\t\t\t\t\t\t<span class=\"crystal-icon\"><i class=\"fas fa-hands-helping\"></i></span>\r\n\t\t\t\t\t\t\t\t\t<p>每一个人，都值得被温柔对待</p>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section>\r\n\r\n\t\t\t<!-- 组织宣言 -->\r\n\t\t\t<section class=\"about-section manifesto-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">组织宣言</h2>\r\n\t\t\t\t\t<div class=\"manifesto-content\">\r\n\t\t\t\t\t\t<div class=\"manifesto-quote\">\r\n\t\t\t\t\t\t\t<p v-for=\"item in aboutXuanyan\" :key=\"item\">{{item}}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section>\r\n\r\n\t\t\t<!-- 组织架构 -->\r\n\t\t\t<section class=\"about-section structure-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">组织架构</h2>\r\n\t\t\t\t\t<div class=\"structure-content\">\r\n\t\t\t\t\t\t<div class=\"structure-item\">\r\n\t\t\t\t\t\t\t<div class=\"structure-header\">\r\n\t\t\t\t\t\t\t\t<div class=\"structure-number\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"fas fa-sitemap\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<h3>理事会（Council）</h3>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"structure-body\">\r\n\t\t\t\t\t\t\t\t<p>职能：协会最高决策机构，负责制定战略方向、全球发展、品牌维护与伦理审查。</p>\r\n\t\t\t\t\t\t\t\t<p>成员：</p>\r\n\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t<li>名誉理事长（Honorary Chairperson）</li>\r\n\t\t\t\t\t\t\t\t\t<li>会长（President）</li>\r\n\t\t\t\t\t\t\t\t\t<li>副会长（Vice Presidents：国际事务、教育事务、资源事务）</li>\r\n\t\t\t\t\t\t\t\t\t<li>创始理事（Founding Council Members）</li>\r\n\t\t\t\t\t\t\t\t\t<li>荣誉顾问团（Spiritual/Energy Healing Advisors）</li>\r\n\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"structure-item\">\r\n\t\t\t\t\t\t\t<div class=\"structure-header\">\r\n\t\t\t\t\t\t\t\t<div class=\"structure-number\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"fas fa-building\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<h3>执行团队（Executive Office）</h3>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"structure-body\">\r\n\t\t\t\t\t\t\t\t<p>负责日常运营与项目落地，设以下部门：</p>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"sub-structure\">\r\n\t\t\t\t\t\t\t\t\t<h4>2.1 教育认证部（Education & Accreditation）</h4>\r\n\t\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t\t<li>制定疗愈师培训体系、课程标准</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>认证全球疗愈师、讲师与合作机构</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>维护ICHA水晶疗愈师等级制度（初级/中级/高级/导师）</li>\r\n\t\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"sub-structure\">\r\n\t\t\t\t\t\t\t\t\t<h4>2.2 全球事务部（International Development）</h4>\r\n\t\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t\t<li>拓展各国分部与授权学院</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>建立地区性合作网络（如东亚分部、欧洲分部等）</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>跨文化课程翻译与适配</li>\r\n\t\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"sub-structure\">\r\n\t\t\t\t\t\t\t\t\t<h4>2.3 项目策划与活动部（Programs & Events）</h4>\r\n\t\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t\t<li>策划疗愈论坛、水晶大会、能量节、静修营等</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>安排导师巡回讲座与跨国合作</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>线上冥想/疗愈仪式/工作坊统筹</li>\r\n\t\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"sub-structure\">\r\n\t\t\t\t\t\t\t\t\t<h4>2.4 品牌与内容部（Brand & Media）</h4>\r\n\t\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t\t<li>网站、社群媒体、出版物、教学资料制作</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>负责视觉设计、品牌形象维护</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>与自媒体、疗愈KOL合作传播</li>\r\n\t\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"sub-structure\">\r\n\t\t\t\t\t\t\t\t\t<h4>2.5 产品与工具研发部（Healing Tools & Product Lab）</h4>\r\n\t\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t\t<li>开发水晶疗愈套装、水晶证书、冥想音频等辅助工具</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>研究不同文化中的水晶疗愈方法并商品化整合</li>\r\n\t\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"sub-structure\">\r\n\t\t\t\t\t\t\t\t\t<h4>2.6 秘书与行政部（Secretariat & Finance）</h4>\r\n\t\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t\t<li>负责成员管理、财务报表、合规与合同</li>\r\n\t\t\t\t\t\t\t\t\t\t<li>会员系统维护、组织章程更新</li>\r\n\t\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"structure-item\">\r\n\t\t\t\t\t\t\t<div class=\"structure-header\">\r\n\t\t\t\t\t\t\t\t<div class=\"structure-number\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"fas fa-map-marked-alt\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<h3>地区代表处（Regional Chapters）</h3>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"structure-body\">\r\n\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t<li>每个大洲/国家/城市设地区代表或认证合作方</li>\r\n\t\t\t\t\t\t\t\t\t<li>定期组织线下沙龙、疗愈会、师资培训班</li>\r\n\t\t\t\t\t\t\t\t\t<li>支持全球语言与文化本地化</li>\r\n\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"structure-item\">\r\n\t\t\t\t\t\t\t<div class=\"structure-header\">\r\n\t\t\t\t\t\t\t\t<div class=\"structure-number\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"fas fa-users-cog\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<h3>顾问团（Advisory Board）</h3>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"structure-body\">\r\n\t\t\t\t\t\t\t\t<p>由以下专业背景人员组成，提供战略建议与专业支持：</p>\r\n\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t<li>能量疗愈导师</li>\r\n\t\t\t\t\t\t\t\t\t<li>天然矿物学专家</li>\r\n\t\t\t\t\t\t\t\t\t<li>身心灵教育顾问</li>\r\n\t\t\t\t\t\t\t\t\t<li>心理学或整合医学专家</li>\r\n\t\t\t\t\t\t\t\t\t<li>可持续采矿伦理专家</li>\r\n\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"structure-item\">\r\n\t\t\t\t\t\t\t<div class=\"structure-header\">\r\n\t\t\t\t\t\t\t\t<div class=\"structure-number\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"fas fa-user-friends\"></i>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<h3>成员体系（Membership）</h3>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"structure-body\">\r\n\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t<li>疗愈师成员（Certified Crystal Healers）</li>\r\n\t\t\t\t\t\t\t\t\t<li>导师成员（Accredited Instructors）</li>\r\n\t\t\t\t\t\t\t\t\t<li>行业成员（水晶供应商、疗愈产品开发者）</li>\r\n\t\t\t\t\t\t\t\t\t<li>终身会员/赞助会员（提供资金或平台支持者）</li>\r\n\t\t\t\t\t\t\t\t\t<li>学习会员（正在参与认证课程的学员）</li>\r\n\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section>\r\n\t\t</div>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport '../assets/css/common-headers.css'; // 导入头部共用样式f\r\n\r\nexport default {\r\n\tname: \"AboutView\",\r\n\tcomponents: { Layout },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\taboutLshiminglogo: ['fas fa-heart', 'fas fa-certificate', 'fas fa-globe-asia', 'fas fa-microscope', 'fas fa-leaf'],\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tcompanyInfo: {},\r\n\t\t\tdesigners: [],\r\n\t\t\taboutXuanyan: '',\r\n\t\t\taboutYuanjing: '',\r\n\t\t\taboutHexin: [],\r\n\t\t\taboutLshiming: [],\r\n\t\t\taboutLogo: '',\r\n\t\t\taboutText: '',\r\n\t\t\taboutDesc: '',\r\n\t\t\taboutTitle: '',\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\t// this.getCompanyInfo()\r\n\t\tthis.$wxShare();\r\n\t\tthis.getAboutConfig();\r\n\t\t// this.getDesigners()\r\n\t},\r\n\tmethods: {\r\n\t\tgetAboutConfig() {\r\n\t\t\tthis.getRequest(\"/cms/config/about\").then(resp => {\r\n\t\t\t\tthis.aboutXuanyan = resp.data.aboutXuanyan\r\n\t\t\t\tthis.aboutYuanjing = resp.data.aboutYuanjing\r\n\t\t\t\tthis.aboutHexin = resp.data.aboutHexin\r\n\t\t\t\tthis.aboutLshiming = resp.data.aboutLshiming\r\n\t\t\t\tthis.aboutLogo = resp.data.aboutLogo\r\n\t\t\t\tthis.aboutText = resp.data.aboutText\r\n\t\t\t\tthis.aboutDesc = resp.data.aboutDesc\r\n\t\t\t\tthis.aboutTitle = resp.data.aboutTitle\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 美化后的页面头部样式 */\r\n.about-header {\r\n\tbackground-image: url('https://img.freepik.com/free-photo/watercolor-pastel-texture-background_53876-98173.jpg') !important;\r\n}\r\n\r\n/* 全局页面样式 */\r\n.about-container {\r\n\twidth: 100%;\r\n\tcolor: #333;\r\n\tfont-family: \"Helvetica Neue\", Helvetica, Arial, \"PingFang SC\", \"Hiragino Sans GB\", \"Heiti SC\", \"Microsoft YaHei\", \"WenQuanYi Micro Hei\", sans-serif;\r\n}\r\n\r\n.content-wrapper {\r\n\tmax-width: 1200px;\r\n\tmargin: 0 auto;\r\n\tpadding: 0 20px;\r\n}\r\n\r\n.about-section {\r\n\tpadding: 70px 0;\r\n\tposition: relative;\r\n}\r\n\r\n.section-title {\r\n\ttext-align: center;\r\n\tfont-size: 36px;\r\n\tmargin-bottom: 50px;\r\n\tcolor: #333;\r\n\tfont-weight: 300;\r\n\tposition: relative;\r\n}\r\n\r\n.section-title:after {\r\n\tcontent: \"\";\r\n\tdisplay: block;\r\n\twidth: 60px;\r\n\theight: 3px;\r\n\tbackground: linear-gradient(135deg, #7b68ee, #b19cd9);\r\n\tmargin: 15px auto 0;\r\n}\r\n\r\n/* 页面标题区域 */\r\n.page-banner {\r\n\theight: 280px;\r\n\tbackground: linear-gradient(rgba(108, 95, 187, 0.9), rgba(171, 151, 216, 0.8)), url('https://images.unsplash.com/photo-1507652313519-d4e9174996dd?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') center/cover;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tcolor: white;\r\n\ttext-align: center;\r\n}\r\n\r\n.page-banner-content h1 {\r\n\tfont-size: 48px;\r\n\tletter-spacing: 2px;\r\n\tfont-weight: 300;\r\n\tmargin: 0;\r\n}\r\n\r\n.banner-decoration {\r\n\twidth: 100px;\r\n\theight: 2px;\r\n\tbackground-color: rgba(255, 255, 255, 0.8);\r\n\tmargin: 20px auto 0;\r\n}\r\n\r\n/* 简介部分 */\r\n.intro-section {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.intro-content {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\talign-items: center;\r\n}\r\n\r\n.intro-text {\r\n\tflex: 1;\r\n\tmin-width: 300px;\r\n\tpadding-right: 50px;\r\n}\r\n\r\n.intro-text p {\r\n\tline-height: 1.8;\r\n\tfont-size: 16px;\r\n\tmargin-bottom: 20px;\r\n\tcolor: #555;\r\n}\r\n\r\n.intro-decoration {\r\n\tflex: 0 0 200px;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\tmargin: 20px 0;\r\n}\r\n\r\n.crystal-shape {\r\n\twidth: 180px;\r\n\theight: 180px;\r\n\tborder-radius: 50%;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 使命部分 */\r\n.mission-section {\r\n\tbackground-color: #f8f9fa;\r\n}\r\n\r\n.mission-cards {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 20px;\r\n\tmax-width: 800px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n.mission-card {\r\n\tdisplay: flex;\r\n\tbackground-color: white;\r\n\tborder-radius: 8px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n\ttransition: transform 0.3s ease;\r\n}\r\n\r\n.mission-card:hover {\r\n\ttransform: translateY(-5px);\r\n}\r\n\r\n.card-icon {\r\n\twidth: 60px;\r\n\tmin-width: 60px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground: linear-gradient(to bottom, #7b68ee, #b19cd9);\r\n\tcolor: white;\r\n\tfont-size: 24px;\r\n}\r\n\r\n.card-content {\r\n\tpadding: 20px;\r\n}\r\n\r\n.card-content p {\r\n\tmargin: 0;\r\n\tline-height: 1.6;\r\n\tcolor: #555;\r\n}\r\n\r\n/* 核心服务部分 */\r\n.services-section {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.services-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(auto-fit, minmax(270px, 1fr));\r\n\tgap: 30px;\r\n}\r\n\r\n.service-item {\r\n\tbackground-color: white;\r\n\tborder-radius: 8px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n\ttransition: transform 0.3s ease, box-shadow 0.3s ease;\r\n\ttext-align: center;\r\n\tpadding: 30px 20px;\r\n}\r\n\r\n.service-item:hover {\r\n\ttransform: translateY(-10px);\r\n\tbox-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.service-icon {\r\n\twidth: 80px;\r\n\theight: 80px;\r\n\tmargin: 0 auto 20px;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #f0f0f0;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.service-item h3 {\r\n\tfont-size: 20px;\r\n\tmargin-bottom: 15px;\r\n\tcolor: #444;\r\n}\r\n\r\n.service-item p {\r\n\tcolor: #666;\r\n\tline-height: 1.6;\r\n}\r\n\r\n/* 服务图标 - 不同颜色和背景 */\r\n.service-icon-1 {\r\n\tbackground: linear-gradient(45deg, #a18cd1, #fbc2eb);\r\n}\r\n\r\n.service-icon-2 {\r\n\tbackground: linear-gradient(45deg, #84fab0, #8fd3f4);\r\n}\r\n\r\n.service-icon-3 {\r\n\tbackground: linear-gradient(45deg, #ff9a9e, #fad0c4);\r\n}\r\n\r\n.service-icon-4 {\r\n\tbackground: linear-gradient(45deg, #ffecd2, #fcb69f);\r\n}\r\n\r\n.service-icon-5 {\r\n\tbackground: linear-gradient(45deg, #a1c4fd, #c2e9fb);\r\n}\r\n\r\n/* 愿景部分 */\r\n.vision-section {\r\n\tbackground-color: #f8f9fa;\r\n\ttext-align: center;\r\n}\r\n\r\n.vision-statement {\r\n\tmax-width: 800px;\r\n\tmargin: 0 auto 50px;\r\n\tpadding: 30px;\r\n\tbackground-color: white;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.vision-statement p {\r\n\tfont-size: 20px;\r\n\tline-height: 1.6;\r\n\tcolor: #555;\r\n\tfont-style: italic;\r\n}\r\n\r\n.vision-beliefs h3 {\r\n\tfont-size: 22px;\r\n\tmargin-bottom: 30px;\r\n\tcolor: #444;\r\n}\r\n\r\n.beliefs-list {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tjustify-content: center;\r\n\tgap: 30px;\r\n}\r\n\r\n.belief-item {\r\n\tflex: 1;\r\n\tmin-width: 250px;\r\n\tmax-width: 300px;\r\n\tpadding: 20px;\r\n\tbackground-color: white;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.crystal-icon {\r\n\tfont-size: 28px;\r\n\tdisplay: block;\r\n\tmargin-bottom: 15px;\r\n\tcolor: #7b68ee;\r\n}\r\n\r\n.crystal-icon i {\r\n\tfont-size: 32px;\r\n}\r\n\r\n.belief-item p {\r\n\tcolor: #555;\r\n\tline-height: 1.6;\r\n}\r\n\r\n/* 宣言部分 */\r\n.manifesto-section {\r\n\tbackground: linear-gradient(rgba(108, 95, 187, 0.9), rgba(171, 151, 216, 0.9)), url('https://images.unsplash.com/photo-1519431940815-70facd7b434e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') center/cover;\r\n\tcolor: white;\r\n}\r\n\r\n.manifesto-section .section-title {\r\n\tcolor: white;\r\n}\r\n\r\n.manifesto-section .section-title:after {\r\n\tbackground-color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n.manifesto-content {\r\n\tmax-width: 800px;\r\n\tmargin: 0 auto;\r\n\ttext-align: center;\r\n\tline-height: 2;\r\n}\r\n\r\n.manifesto-quote {\r\n\tfont-size: 18px;\r\n\tline-height: 1.8;\r\n}\r\n\r\n.manifesto-quote p {\r\n\tmargin-bottom: 25px;\r\n}\r\n\r\n/* 组织架构 */\r\n.structure-section {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.structure-content {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30px;\r\n}\r\n\r\n.structure-item {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.structure-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20px;\r\n\tbackground: linear-gradient(to right, #f0f0f0, #fff);\r\n\tborder-bottom: 1px solid #eee;\r\n}\r\n\r\n.structure-number {\r\n\twidth: 40px;\r\n\theight: 40px;\r\n\tborder-radius: 50%;\r\n\tbackground: linear-gradient(135deg, #7b68ee, #b19cd9);\r\n\tcolor: white;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-weight: bold;\r\n\tmargin-right: 15px;\r\n\tfont-size: 20px;\r\n}\r\n\r\n.structure-header h3 {\r\n\tmargin: 0;\r\n\tfont-size: 20px;\r\n\tcolor: #444;\r\n}\r\n\r\n.structure-body {\r\n\tpadding: 25px;\r\n}\r\n\r\n.structure-body p {\r\n\tmargin-bottom: 15px;\r\n\tline-height: 1.6;\r\n\tcolor: #555;\r\n}\r\n\r\n.structure-body ul {\r\n\tlist-style-type: none;\r\n\tpadding: 0;\r\n\tmargin: 0;\r\n}\r\n\r\n.structure-body li {\r\n\tposition: relative;\r\n\tpadding-left: 20px;\r\n\tmargin-bottom: 10px;\r\n\tline-height: 1.6;\r\n\tcolor: #555;\r\n}\r\n\r\n.structure-body li:before {\r\n\tcontent: \"•\";\r\n\tcolor: #7b68ee;\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\tfont-size: 20px;\r\n\tline-height: 1;\r\n}\r\n\r\n.sub-structure {\r\n\tmargin-top: 25px;\r\n}\r\n\r\n.sub-structure h4 {\r\n\tcolor: #7b68ee;\r\n\tmargin-bottom: 15px;\r\n\tfont-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n\t.page-banner {\r\n\t\theight: 200px;\r\n\t}\r\n\t\r\n\t.page-banner-content h1 {\r\n\t\tfont-size: 36px;\r\n\t}\r\n\t\r\n\t.about-section {\r\n\t\tpadding: 50px 0;\r\n\t}\r\n\t\r\n\t.section-title {\r\n\t\tfont-size: 28px;\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n\t\r\n\t.intro-text {\r\n\t\tpadding-right: 0;\r\n\t}\r\n\t\r\n\t.belief-item {\r\n\t\tmin-width: 100%;\r\n\t}\r\n\t\r\n\t.manifesto-quote {\r\n\t\tfont-size: 16px;\r\n\t}\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqRA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAH;EAAA;EACAI,KAAA;IACA;MACAC,iBAAA;MACAJ,aAAA,EAAAA,aAAA;MACAK,WAAA;MACAC,SAAA;MACAC,YAAA;MACAC,aAAA;MACAC,UAAA;MACAC,aAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,KAAAC,QAAA;IACA,KAAAC,cAAA;IACA;EACA;;EACAC,OAAA;IACAD,eAAA;MACA,KAAAE,UAAA,sBAAAC,IAAA,CAAAC,IAAA;QACA,KAAAd,YAAA,GAAAc,IAAA,CAAAlB,IAAA,CAAAI,YAAA;QACA,KAAAC,aAAA,GAAAa,IAAA,CAAAlB,IAAA,CAAAK,aAAA;QACA,KAAAC,UAAA,GAAAY,IAAA,CAAAlB,IAAA,CAAAM,UAAA;QACA,KAAAC,aAAA,GAAAW,IAAA,CAAAlB,IAAA,CAAAO,aAAA;QACA,KAAAC,SAAA,GAAAU,IAAA,CAAAlB,IAAA,CAAAQ,SAAA;QACA,KAAAC,SAAA,GAAAS,IAAA,CAAAlB,IAAA,CAAAS,SAAA;QACA,KAAAC,SAAA,GAAAQ,IAAA,CAAAlB,IAAA,CAAAU,SAAA;QACA,KAAAC,UAAA,GAAAO,IAAA,CAAAlB,IAAA,CAAAW,UAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}