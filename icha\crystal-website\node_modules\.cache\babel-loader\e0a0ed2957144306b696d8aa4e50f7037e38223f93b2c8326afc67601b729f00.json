{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from '@/components/common/Layout.vue';\nexport default {\n  components: {\n    Layout\n  },\n  name: 'ChakraTestBalance',\n  data() {\n    return {\n      balanceMethods: [{\n        name: '冥想练习',\n        icon: 'smile-o',\n        color: '#c9ab79',\n        description: '通过专注的冥想练习，可以帮助我们感知和平衡脉轮能量。',\n        steps: ['找一个安静舒适的地方坐下', '闭上眼睛，深呼吸几次放松身心', '将注意力集中在需要平衡的脉轮位置', '想象该脉轮发出温暖的光芒', '保持专注15-20分钟']\n      }, {\n        name: '瑜伽体式',\n        icon: 'user-o',\n        color: '#5f9057',\n        description: '特定的瑜伽体式可以帮助激活和平衡不同的脉轮。',\n        steps: ['选择针对特定脉轮的瑜伽体式', '在练习前进行热身运动', '保持正确的呼吸节奏', '在体式中保持专注和觉知', '练习后进行放松和整合']\n      }, {\n        name: '水晶疗愈',\n        icon: 'diamond-o',\n        color: '#2c3485',\n        description: '不同颜色的水晶对应不同的脉轮，可以帮助平衡脉轮能量。',\n        steps: ['选择与脉轮颜色对应的水晶', '清洁和净化水晶能量', '将水晶放在对应的脉轮位置', '静躺15-30分钟感受能量', '结束后感谢水晶的帮助']\n      }, {\n        name: '声音疗愈',\n        icon: 'music-o',\n        color: '#5b8aa4',\n        description: '通过特定的音频、咒语或唱诵来平衡脉轮。',\n        steps: ['选择对应脉轮的音频或咒语', '找一个不被打扰的环境', '专注聆听或跟随唱诵', '感受声音在身体中的振动', '让声音的能量流遍全身']\n      }],\n      tips: ['每天花10-15分钟进行脉轮平衡练习', '保持规律的练习比偶尔长时间练习更有效', '在练习前后多喝水帮助能量流动', '注意身体的感受，不要强迫任何体验', '结合健康的生活方式效果更佳', '如有不适请停止练习并咨询专业人士']\n    };\n  },\n  methods: {\n    goToTest() {\n      this.$router.push('/chakra-test');\n    },\n    goToIntro() {\n      this.$router.push('/chakra-test/intro');\n    },\n    // 返回上一页\n    goBack() {\n      this.$router.back();\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "components", "name", "data", "balanceMethods", "icon", "color", "description", "steps", "tips", "methods", "goToTest", "$router", "push", "goToIntro", "goBack", "back"], "sources": ["src/views/ChakraTestBalance.vue"], "sourcesContent": ["<template>\r\n  <Layout>\r\n    <div class=\"chakra-balance-page\">\r\n    <!-- 标题栏 -->\r\n    <div class=\"nav-title\">\r\n      <div class=\"nav-left\">\r\n        <van-button \r\n          class=\"back-button\"\r\n          @click=\"goBack\"\r\n          plain\r\n        >\r\n          <van-icon name=\"arrow-left\" size=\"18\" />\r\n        </van-button>\r\n        <div class=\"color-bar\"></div>\r\n        <div class=\"title-text\">平衡脉轮</div>\r\n      </div>\r\n      <div class=\"nav-right\">\r\n        <van-button \r\n          class=\"test-button-small\"\r\n          @click=\"goToTest\"\r\n          size=\"small\"\r\n        >\r\n          <van-icon name=\"play\" size=\"14\" />\r\n          <span>开始测试</span>\r\n        </van-button>\r\n        <van-button \r\n          class=\"intro-button-small\"\r\n          @click=\"goToIntro\"\r\n          size=\"small\"\r\n          type=\"primary\"\r\n        >\r\n          <van-icon name=\"info-o\" size=\"14\" />\r\n          <span>脉轮简介</span>\r\n        </van-button>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 内容区域 -->\r\n    <div class=\"content-container\">\r\n      <div class=\"intro-section\">\r\n        <h3 class=\"section-title\">脉轮平衡的重要性</h3>\r\n        <p class=\"intro-text\">\r\n          脉轮是人体能量系统的重要组成部分，当脉轮平衡时，我们的身心灵都会处于和谐状态。\r\n          通过冥想、瑜伽、水晶疗愈等方法，可以帮助我们平衡和激活脉轮能量。\r\n        </p>\r\n      </div>\r\n      \r\n      <div class=\"methods-section\">\r\n        <h3 class=\"section-title\">平衡脉轮的方法</h3>\r\n        \r\n        <div class=\"method-card\" v-for=\"(method, index) in balanceMethods\" :key=\"index\">\r\n          <div class=\"method-header\">\r\n            <van-icon :name=\"method.icon\" size=\"24\" :color=\"method.color\" />\r\n            <h4 class=\"method-name\">{{ method.name }}</h4>\r\n          </div>\r\n          <div class=\"method-description\">\r\n            <p>{{ method.description }}</p>\r\n          </div>\r\n          <div class=\"method-steps\">\r\n            <h5>具体步骤：</h5>\r\n            <ul>\r\n              <li v-for=\"(step, stepIndex) in method.steps\" :key=\"stepIndex\">\r\n                {{ step }}\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"tips-section\">\r\n        <h3 class=\"section-title\">平衡脉轮的小贴士</h3>\r\n        <div class=\"tips-list\">\r\n          <div class=\"tip-item\" v-for=\"(tip, index) in tips\" :key=\"index\">\r\n            <van-icon name=\"info-o\" size=\"16\" color=\"#c9ab79\" />\r\n            <span>{{ tip }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    </div>\r\n  </Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from '@/components/common/Layout.vue'\r\n\r\nexport default {\r\n  components: {\r\n    Layout\r\n  },\r\n  name: 'ChakraTestBalance',\r\n  data() {\r\n    return {\r\n      balanceMethods: [\r\n        {\r\n          name: '冥想练习',\r\n          icon: 'smile-o',\r\n          color: '#c9ab79',\r\n          description: '通过专注的冥想练习，可以帮助我们感知和平衡脉轮能量。',\r\n          steps: [\r\n            '找一个安静舒适的地方坐下',\r\n            '闭上眼睛，深呼吸几次放松身心',\r\n            '将注意力集中在需要平衡的脉轮位置',\r\n            '想象该脉轮发出温暖的光芒',\r\n            '保持专注15-20分钟'\r\n          ]\r\n        },\r\n        {\r\n          name: '瑜伽体式',\r\n          icon: 'user-o',\r\n          color: '#5f9057',\r\n          description: '特定的瑜伽体式可以帮助激活和平衡不同的脉轮。',\r\n          steps: [\r\n            '选择针对特定脉轮的瑜伽体式',\r\n            '在练习前进行热身运动',\r\n            '保持正确的呼吸节奏',\r\n            '在体式中保持专注和觉知',\r\n            '练习后进行放松和整合'\r\n          ]\r\n        },\r\n        {\r\n          name: '水晶疗愈',\r\n          icon: 'diamond-o',\r\n          color: '#2c3485',\r\n          description: '不同颜色的水晶对应不同的脉轮，可以帮助平衡脉轮能量。',\r\n          steps: [\r\n            '选择与脉轮颜色对应的水晶',\r\n            '清洁和净化水晶能量',\r\n            '将水晶放在对应的脉轮位置',\r\n            '静躺15-30分钟感受能量',\r\n            '结束后感谢水晶的帮助'\r\n          ]\r\n        },\r\n        {\r\n          name: '声音疗愈',\r\n          icon: 'music-o',\r\n          color: '#5b8aa4',\r\n          description: '通过特定的音频、咒语或唱诵来平衡脉轮。',\r\n          steps: [\r\n            '选择对应脉轮的音频或咒语',\r\n            '找一个不被打扰的环境',\r\n            '专注聆听或跟随唱诵',\r\n            '感受声音在身体中的振动',\r\n            '让声音的能量流遍全身'\r\n          ]\r\n        }\r\n      ],\r\n      tips: [\r\n        '每天花10-15分钟进行脉轮平衡练习',\r\n        '保持规律的练习比偶尔长时间练习更有效',\r\n        '在练习前后多喝水帮助能量流动',\r\n        '注意身体的感受，不要强迫任何体验',\r\n        '结合健康的生活方式效果更佳',\r\n        '如有不适请停止练习并咨询专业人士'\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    goToTest() {\r\n      this.$router.push('/chakra-test')\r\n    },\r\n    \r\n    goToIntro() {\r\n      this.$router.push('/chakra-test/intro')\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      this.$router.back()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.chakra-balance-page {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n}\r\n\r\n.nav-title {\r\n  padding: 15px 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  height: 60px;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n  box-shadow: 0 4px 15px rgba(86, 70, 128, 0.1);\r\n  margin-bottom: 20px;\r\n  border-radius: 0 0 20px 20px;\r\n}\r\n\r\n.nav-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.nav-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.back-button {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background: rgba(86, 70, 128, 0.08);\r\n  border: 1px solid rgba(86, 70, 128, 0.2);\r\n  color: #564680;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.back-button:hover {\r\n  background: rgba(86, 70, 128, 0.15);\r\n  border-color: rgba(86, 70, 128, 0.4);\r\n  transform: translateX(-2px);\r\n}\r\n\r\n.color-bar {\r\n  width: 6px;\r\n  height: 25px;\r\n  background: linear-gradient(135deg, #564680, #516790, #c9ab79);\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(86, 70, 128, 0.3);\r\n}\r\n\r\n.title-text {\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.test-button-small {\r\n  background: linear-gradient(135deg, #564680, #516790);\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 6px 12px;\r\n  color: white;\r\n  font-weight: 600;\r\n  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.test-button-small:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);\r\n}\r\n\r\n.test-button-small span {\r\n  margin-left: 4px;\r\n}\r\n\r\n.intro-button-small {\r\n  background: linear-gradient(135deg, #c9ab79, #b8996a);\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 6px 12px;\r\n  color: white;\r\n  font-weight: 600;\r\n  box-shadow: 0 3px 10px rgba(201, 171, 121, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.intro-button-small:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(201, 171, 121, 0.4);\r\n}\r\n\r\n.intro-button-small span {\r\n  margin-left: 4px;\r\n}\r\n\r\n.content-container {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.intro-section, .methods-section, .tips-section {\r\n  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);\r\n  border-radius: 16px;\r\n  padding: 25px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 8px 25px rgba(86, 70, 128, 0.08);\r\n  border: 2px solid transparent;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.intro-section::before, .methods-section::before, .tips-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, #564680, #516790, #c9ab79);\r\n  border-radius: 16px 16px 0 0;\r\n}\r\n\r\n.intro-section:hover, .methods-section:hover, .tips-section:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 12px 35px rgba(86, 70, 128, 0.12);\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  margin: 0 0 20px 0;\r\n  padding-bottom: 15px;\r\n  border-bottom: 3px solid transparent;\r\n  background: linear-gradient(90deg, #564680, #516790, #c9ab79) bottom;\r\n  background-size: 80px 3px;\r\n  background-repeat: no-repeat;\r\n  position: relative;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.intro-text {\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  color: #555;\r\n  margin: 0;\r\n}\r\n\r\n.method-card {\r\n  border: 2px solid rgba(86, 70, 128, 0.1);\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n  \r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 8px 20px rgba(86, 70, 128, 0.1);\r\n    border-color: rgba(86, 70, 128, 0.2);\r\n  }\r\n}\r\n\r\n.method-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid rgba(86, 70, 128, 0.1);\r\n}\r\n\r\n.method-name {\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  margin: 0 0 0 10px;\r\n  letter-spacing: 0.3px;\r\n}\r\n\r\n.method-description {\r\n  margin-bottom: 15px;\r\n  \r\n  p {\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n    color: #555;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n.method-steps {\r\n  h5 {\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    margin: 0 0 8px 0;\r\n  }\r\n  \r\n  ul {\r\n    margin: 0;\r\n    padding-left: 20px;\r\n    \r\n    li {\r\n      font-size: 13px;\r\n      line-height: 1.5;\r\n      color: #666;\r\n      margin-bottom: 4px;\r\n      \r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.tips-list {\r\n  .tip-item {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n    color: #555;\r\n    \r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n    \r\n    span {\r\n      margin-left: 8px;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 768px) {\r\n  .intro-section, .methods-section, .tips-section {\r\n    padding: 20px;\r\n    margin-bottom: 16px;\r\n    border-radius: 12px;\r\n  }\r\n  \r\n  .method-card {\r\n    padding: 16px;\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .section-title {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .method-name {\r\n    font-size: 15px;\r\n  }\r\n  \r\n  .nav-title {\r\n    padding: 12px 20px;\r\n    height: 55px;\r\n  }\r\n  \r\n  .nav-left {\r\n    gap: 8px;\r\n  }\r\n  \r\n  .nav-right {\r\n    gap: 6px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n  \r\n  .title-text {\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .test-button-small,\r\n  .intro-button-small {\r\n    padding: 5px 10px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .nav-title {\r\n    padding: 10px 15px;\r\n    height: 50px;\r\n  }\r\n  \r\n  .nav-left {\r\n    gap: 6px;\r\n  }\r\n  \r\n  .nav-right {\r\n    gap: 4px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 32px;\r\n    height: 32px;\r\n  }\r\n  \r\n  .color-bar {\r\n    width: 4px;\r\n    height: 20px;\r\n  }\r\n  \r\n  .title-text {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .test-button-small,\r\n  .intro-button-small {\r\n    padding: 4px 8px;\r\n    font-size: 11px;\r\n  }\r\n  \r\n  .content-container {\r\n    padding: 0 15px 20px;\r\n  }\r\n  \r\n  .intro-section, .methods-section, .tips-section {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .method-card {\r\n    padding: 14px;\r\n  }\r\n  \r\n  .section-title {\r\n    font-size: 15px;\r\n    background-size: 60px 3px;\r\n  }\r\n  \r\n  .method-name {\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .method-description p {\r\n    font-size: 13px;\r\n  }\r\n  \r\n  .method-steps {\r\n    h5 {\r\n      font-size: 13px;\r\n    }\r\n    \r\n    ul li {\r\n      font-size: 12px;\r\n    }\r\n  }\r\n  \r\n  .tip-item {\r\n    font-size: 13px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA,OAAAA,MAAA;AAEA;EACAC,UAAA;IACAD;EACA;EACAE,IAAA;EACAC,KAAA;IACA;MACAC,cAAA,GACA;QACAF,IAAA;QACAG,IAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA,GACA,gBACA,kBACA,oBACA,gBACA;MAEA,GACA;QACAN,IAAA;QACAG,IAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA,GACA,iBACA,cACA,aACA,eACA;MAEA,GACA;QACAN,IAAA;QACAG,IAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA,GACA,gBACA,aACA,gBACA,iBACA;MAEA,GACA;QACAN,IAAA;QACAG,IAAA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA,GACA,gBACA,cACA,aACA,eACA;MAEA,EACA;MACAC,IAAA,GACA,sBACA,sBACA,kBACA,oBACA,iBACA;IAEA;EACA;EACAC,OAAA;IACAC,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IAEAC,UAAA;MACA,KAAAF,OAAA,CAAAC,IAAA;IACA;IAEA;IACAE,OAAA;MACA,KAAAH,OAAA,CAAAI,IAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}