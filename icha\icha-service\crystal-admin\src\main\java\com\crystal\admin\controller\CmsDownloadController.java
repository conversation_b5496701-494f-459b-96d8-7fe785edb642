package com.crystal.admin.controller;

import com.crystal.common.model.cms.CmsDownloadEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsDownloadService;
import com.crystal.service.service.SystemAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;

/**
 * 下载 控制器
 * | Author: 陈佳音
 * ｜ @date Mon Apr 29 10:10:51 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/download")
public class CmsDownloadController {
    @Autowired
    private CmsDownloadService cmsDownloadService;
    @Autowired
    private SystemAttachmentService systemAttachmentService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('cmsdownload:list')")
    public CommonResult<CommonPage<CmsDownloadEntity>> list(@Validated CmsDownloadEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsDownloadEntity> page = CommonPage.restPage(cmsDownloadService.queryPage(request,pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('cmsdownload:info')")
    public CommonResult<CmsDownloadEntity> info(@PathVariable("id") Long id){
        CmsDownloadEntity cmsDownload = cmsDownloadService.getById(id);
        return CommonResult.success(cmsDownload);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('cmsdownload:save')")
    public CommonResult<String> save(@RequestBody CmsDownloadEntity cmsDownload){
        cmsDownload.setAddTime(new Date());
        cmsDownload.setFileUrl(systemAttachmentService.clearPrefix(cmsDownload.getFileUrl()));
        cmsDownloadService.save(cmsDownload);
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('cmsdownload:update')")
    public CommonResult<String> update(@RequestBody CmsDownloadEntity cmsDownload){
        cmsDownload.setFileUrl(systemAttachmentService.clearPrefix(cmsDownload.getFileUrl()));
        cmsDownloadService.updateById(cmsDownload);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('cmsdownload:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids){
        if (cmsDownloadService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 