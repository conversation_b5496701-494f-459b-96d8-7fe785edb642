import request from '@/utils/request'

/**
 * 新增工作坊
 * @param data
 */
export function cmsWorkshopCreateApi(data) {
    return request({
        url: '/admin/cms/workshop/save',
        method: 'POST',
        data
    })
}

/**
 * 工作坊更新
 * @param data
 */
export function cmsWorkshopUpdateApi(data) {
    return request({
        url: '/admin/cms/workshop/update',
        method: 'POST',
        data
    })
}

/**
 * 工作坊详情
 * @param id
 */
export function cmsWorkshopDetailApi(id) {
    return request({
        url: `/admin/cms/workshop/info/${id}`,
        method: 'GET'
    })
}

/**
 * 工作坊删除
 * @param ids 要删除的id数组
 */
export function cmsWorkshopDeleteApi(ids) {
    return request({
        url: `/admin/cms/workshop/delete`,
        method: 'POST',
        data: ids
    })
}

/**
 * 工作坊列表
 * @param params
 */
export function cmsWorkshopListApi(params) {
    return request({
        url: '/admin/cms/workshop/list',
        method: 'GET',
        params
    })
} 