<template>
  <div v-if="visible" class="apply-dialog-mask">
    <div class="apply-dialog">
      <div class="apply-dialog-header">
        <span>课程报名</span>
        <i class="fa fa-close apply-dialog-close" @click="closeDialog"></i>
      </div>
      <div class="apply-dialog-body">
        <p>课程名称：<b>{{course && course.title}}</b></p>
        <p v-if="course && (course.type == 1 || course.type == 2)">
          邀请码：<input v-model="inviteCode" placeholder="请输入邀请码" class="invite-input" />
        </p>
      </div>
      <div class="apply-dialog-footer">
        <button class="crystal-btn" :disabled="loading" @click="confirmApply">{{ loading ? '提交中...' : '确认报名' }}</button>
        <button class=" crystal-btn-secondary" style="margin-left:10px;" :disabled="loading" @click="closeDialog">取消</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CourseApplyDialog',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    course: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      inviteCode: '',
      loading: false
    }
  },
  watch: {
    visible(val) {
      if (val) this.inviteCode = '';
    },
    course(val) {
      this.inviteCode = '';
    }
  },
  methods: {
    closeDialog() {
      this.$emit('close');
    },
    confirmApply() {
      if (!this.course) return;
      if (this.course.type == 1 && !this.inviteCode) {
        this.$message && this.$message.warning('请输入邀请码');
        return;
      }
      this.loading = true;
      const params = {
        cmsCourseId: this.course.id
      };
      if (this.course.type == 1 || this.course.type == 2) {
        params.inviteCode = this.inviteCode;
      }
      // 支持postRequest/getRequest两种调用
      const req = this.postRequest || this.getRequest;
      if (!req) {
        this.$message && this.$message.error('未注入请求方法');
        this.loading = false;
        return;
      }
      req('/cmsAttend/course', params).then(resp => {
        this.loading = false;
        if (resp && resp.code == 200) {
          this.$message && this.$message.success('报名成功');
          this.$emit('success', this.course);
          this.closeDialog();
        } else {
          this.$message && this.$message.error(resp.message || '报名失败');
        }
      }).catch(() => {
        this.loading = false;
      });
    }
  },
  inject: ['postRequest', 'getRequest', '$message']
}
</script>

<style scoped>
.apply-dialog-mask {
  position: fixed;
  z-index: 9999;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  display: flex;
  align-items: center;
  justify-content: center;
}
.apply-dialog {
  background: #fff;
  border-radius: 12px;
  padding: 30px 30px 20px 30px;
  min-width: 320px;
  max-width: 90vw;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  position: relative;
}
.apply-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 18px;
}
.apply-dialog-close {
  cursor: pointer;
  font-size: 20px;
  color: #888;
  transition: color 0.2s;
}
.apply-dialog-close:hover {
  color: #dc2430;
}
.apply-dialog-body {
  margin-bottom: 18px;
  font-size: 15px;
}
.invite-input {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 15px;
  margin-left: 8px;
}
.apply-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.crystal-btn-secondary {
  background: linear-gradient(135deg, #2bff00, #1900ff);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(180, 180, 180, 0.18);
  margin-left: 10px;
  animation: none;
}
.crystal-btn-secondary:hover {
  background: linear-gradient(135deg, #2bff00, #1900ff);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(180, 180, 180, 0.28);
}

/* 按钮样式 */
.crystal-btn {
  background: linear-gradient(135deg, #7b4397, #dc2430);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(123, 67, 151, 0.3);
}

.crystal-btn:hover {
  background: linear-gradient(135deg, #dc2430, #7b4397);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(123, 67, 151, 0.4);
}

.crystal-btn i {
  margin-right: 8px;
}

</style> 