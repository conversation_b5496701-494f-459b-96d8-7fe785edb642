<template>
  <!-- 基于 Element UI 新增和修改弹窗 -->
  <el-dialog
    :title="!dataForm.id ? '添加-ADD' : '修改-EDITE'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <!-- 新增和创建表单表单 -->
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataSubmit()" label-width="80px">
    <el-form-item label="创建时间" prop="addTime">
      <el-input v-model="dataForm.addTime" placeholder="创建时间"></el-input>
    </el-form-item>
    <el-form-item label="更新时间" prop="updateTime">
      <el-input v-model="dataForm.updateTime" placeholder="更新时间"></el-input>
    </el-form-item>
    <el-form-item label="用户ID" prop="userId">
      <el-input v-model="dataForm.userId" placeholder="用户ID"></el-input>
    </el-form-item>
    <el-form-item label="海底輪" prop="root">
      <el-input v-model="dataForm.root" placeholder="海底輪"></el-input>
    </el-form-item>
    <el-form-item label="本我輪" prop="sacral">
      <el-input v-model="dataForm.sacral" placeholder="本我輪"></el-input>
    </el-form-item>
    <el-form-item label="臍輪" prop="navel">
      <el-input v-model="dataForm.navel" placeholder="臍輪"></el-input>
    </el-form-item>
    <el-form-item label="心輪" prop="heart">
      <el-input v-model="dataForm.heart" placeholder="心輪"></el-input>
    </el-form-item>
    <el-form-item label="喉輪" prop="throat">
      <el-input v-model="dataForm.throat" placeholder="喉輪"></el-input>
    </el-form-item>
    <el-form-item label="三眼輪" prop="thirdEye">
      <el-input v-model="dataForm.thirdEye" placeholder="三眼輪"></el-input>
    </el-form-item>
    <el-form-item label="頂輪" prop="crown">
      <el-input v-model="dataForm.crown" placeholder="頂輪"></el-input>
    </el-form-item>
    <el-form-item label="问题id" prop="questionId">
      <el-input v-model="dataForm.questionId" placeholder="问题id"></el-input>
    </el-form-item>
    <el-form-item label="答案" prop="optionId">
      <el-input v-model="dataForm.optionId" placeholder="答案"></el-input>
    </el-form-item>
    <el-form-item label="正确答案" prop="realOptionId">
      <el-input v-model="dataForm.realOptionId" placeholder="正确答案"></el-input>
    </el-form-item>
    <el-form-item label="答案状态，0-不正确，1-正确" prop="status">
      <el-input v-model="dataForm.status" placeholder="答案状态，0-不正确，1-正确"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import * as api from './.questionuseroptionapi.js'
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          addTime: '' , 
          updateTime: '' , 
          userId: '' , 
          root: '' , 
          sacral: '' , 
          navel: '' , 
          heart: '' , 
          throat: '' , 
          thirdEye: '' , 
          crown: '' , 
          questionId: '' , 
          optionId: '' , 
          realOptionId: '' , 
          status: '' 
        },
        dataRule: {
          addTime: [
            { required: true, message: '创建时间  为必填项', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '更新时间  为必填项', trigger: 'blur' }
          ],
          userId: [
            { required: true, message: '用户ID  为必填项', trigger: 'blur' }
          ],
          root: [
            { required: true, message: '海底輪  为必填项', trigger: 'blur' }
          ],
          sacral: [
            { required: true, message: '本我輪  为必填项', trigger: 'blur' }
          ],
          navel: [
            { required: true, message: '臍輪  为必填项', trigger: 'blur' }
          ],
          heart: [
            { required: true, message: '心輪  为必填项', trigger: 'blur' }
          ],
          throat: [
            { required: true, message: '喉輪  为必填项', trigger: 'blur' }
          ],
          thirdEye: [
            { required: true, message: '三眼輪  为必填项', trigger: 'blur' }
          ],
          crown: [
            { required: true, message: '頂輪  为必填项', trigger: 'blur' }
          ],
          questionId: [
            { required: true, message: '问题id  为必填项', trigger: 'blur' }
          ],
          optionId: [
            { required: true, message: '答案  为必填项', trigger: 'blur' }
          ],
          realOptionId: [
            { required: true, message: '正确答案  为必填项', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '答案状态，0-不正确，1-正确  为必填项', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) { // 初始化表单验证规则
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            api.questionuseroptionDetailApi(id).then(res => {
                this.dataForm = res;
            })
          }
        })
      },
      // 表单数据提交
      dataSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
                  api.QuestionUserOptionCreateApi().then(res =>{
                      // TODO 保存数据
                  });
          }
        })
      }
    }
  }
</script>
