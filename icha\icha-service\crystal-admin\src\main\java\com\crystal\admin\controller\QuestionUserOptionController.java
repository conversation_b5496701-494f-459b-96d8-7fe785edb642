package com.crystal.admin.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.crystal.common.enums.ExamQuestionTypeEnum;
import com.crystal.common.model.question.QuestionEntity;
import com.crystal.common.model.question.QuestionOptionEntity;
import com.crystal.common.model.question.QuestionUserOptionEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.common.utils.TextUtils;
import com.crystal.service.service.QuestionOptionService;
import com.crystal.service.service.QuestionService;
import com.crystal.service.service.QuestionUserOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * 用户手串 控制器
 * | Author: 陈佳音
 * ｜ @date Fri Nov 22 16:43:13 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/questionuseroption")
public class QuestionUserOptionController {
    @Autowired
    private QuestionUserOptionService questionUserOptionService;
    @Autowired
    private QuestionService questionService;
    @Autowired
    private QuestionOptionService questionOptionService;

    @GetMapping("/findByQuestionUserId/{questionUserId}")
    public CommonResult findByQuestionUserId(@PathVariable("questionUserId") Long questionUserId) {
        List<QuestionUserOptionEntity> questionUserOptionEntities = questionUserOptionService.findByQuestionUserId(questionUserId);
        if (!CollectionUtils.isEmpty(questionUserOptionEntities)) {
            // 找到所有题目
            List<QuestionEntity> examQuestionEntities = questionService.list();
            // 找到所有选项
            List<QuestionOptionEntity> examQuestionOptionEntities = questionOptionService.list();
            questionUserOptionEntities.forEach(e-> {
                examQuestionEntities.forEach(f-> {
                    if (f.getId().equals(e.getQuestionId())) {
                        e.setQuestionName(TextUtils.decodeText(f.getName()));
                        if (f.getType().equals(ExamQuestionTypeEnum.CHECKBOX.getCode())) {
                            // 如果是多选
                            String[] optionIds = e.getOptionId().split(",");
                            String[] realOptionIds = e.getRealOptionId().split(",");
                            List<String> optionName = new ArrayList<>();
                            List<String> realOptionName = new ArrayList<>();
                            examQuestionOptionEntities.forEach(g-> {
                                for (String optionId : optionIds) {
                                    if (optionId.equals(g.getId().toString())) {
                                        optionName.add(g.getOptionId() + ":" + g.getName());
                                    }
                                }
                                for (String optionId : realOptionIds) {
                                    if (optionId.equals(g.getId().toString())) {
                                        realOptionName.add(g.getOptionId() + ":" + g.getName());
                                    }
                                }
                            });
                            e.setOptionName(String.join(",", optionName));
                            e.setRealOptionName(String.join(",", realOptionName));
                        } else if (f.getType().equals(ExamQuestionTypeEnum.RADIO.getCode())) {
                            // 如果是单选
                            examQuestionOptionEntities.forEach(g-> {
                                if (e.getOptionId().equals(g.getId().toString())) {
                                    e.setOptionName(g.getOptionId() + ":" + g.getName());
                                }
                                if (e.getRealOptionId().equals(g.getId().toString())) {
                                    e.setRealOptionName(g.getOptionId() + ":" + g.getName());
                                }
                            });
                        } else {
                            e.setOptionName(e.getOptionId());
                            e.setRealOptionName(e.getRealOptionId());
                        }
                    }
                });
            });
        }
        return CommonResult.success(questionUserOptionEntities);
    }
    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('userbraceletsitem:list')")
    public CommonResult<CommonPage<QuestionUserOptionEntity>> list(@Validated QuestionUserOptionEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<QuestionUserOptionEntity> page = CommonPage.restPage(questionUserOptionService.queryPage(request,pageParamRequest));

        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('userbraceletsitem:info')")
    public CommonResult<QuestionUserOptionEntity> info(@PathVariable("id") Integer id){
		QuestionUserOptionEntity questionUserOption = questionUserOptionService.getById(id);

        return CommonResult.success(questionUserOption);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
    @Transactional(rollbackFor = Exception.class)
//    @PreAuthorize("hasAuthority('userbraceletsitem:save')")
    public CommonResult<String> save(@RequestBody QuestionUserOptionEntity questionUserOption){
        questionUserOption.setAddTime(new Date());
        if (questionUserOptionService.save(questionUserOption)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
    @Transactional(rollbackFor = Exception.class)
//    @PreAuthorize("hasAuthority('userbraceletsitem:update')")
    public CommonResult<String> update(@RequestBody QuestionUserOptionEntity questionUserOption){
        questionUserOption.setUpdateTime(new Date());
        if (questionUserOptionService.updateById(questionUserOption)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('userbraceletsitem:delete')")
    public CommonResult<String> delete(@RequestBody Integer[] ids){
        if (questionUserOptionService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
