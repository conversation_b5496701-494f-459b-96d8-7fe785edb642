<template>
  <el-dialog :title="!dataForm.id ? '新增邀请码' : '修改邀请码'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="课程ID" prop="cmsCourseId">
        <el-input v-model="dataForm.cmsCourseId" placeholder="课程ID"></el-input>
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户ID"></el-input>
      </el-form-item>
      <el-form-item label="邀请码" prop="inviteCode">
        <el-input v-model="dataForm.inviteCode" placeholder="邀请码"></el-input>
      </el-form-item>
      <el-form-item label="是否使用" prop="isUse">
        <el-select v-model="dataForm.isUse" placeholder="请选择">
          <el-option :value="0" label="未使用"></el-option>
          <el-option :value="1" label="已使用"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="使用时间" prop="useTime" v-if="dataForm.isUse === 1">
        <el-date-picker v-model="dataForm.useTime" type="datetime" placeholder="选择使用时间" style="width: 100%;" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  cmsCourseCodeInfoApi,
  cmsCourseCodeAddApi,
  cmsCourseCodeUpdateApi
} from '@/api/cmsCourseCode'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        cmsCourseId: '',
        userId: '',
        inviteCode: '',
        isUse: 0,
        useTime: null
      },
      dataRule: {
        cmsCourseId: [
          { required: true, message: '课程ID不能为空', trigger: 'blur' }
        ],
        userId: [
          { required: true, message: '用户ID不能为空', trigger: 'blur' }
        ],
        inviteCode: [
          { required: true, message: '邀请码不能为空', trigger: 'blur' }
        ],
        isUse: [
          { required: true, message: '请选择是否使用', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init(id, cmsCourseId) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        // 如果是新增并且传入了课程ID，则自动填充
        if (!this.dataForm.id && cmsCourseId) {
          this.dataForm.cmsCourseId = cmsCourseId
        }
        
        // 如果是编辑，则加载邀请码详情
        if (this.dataForm.id) {
          cmsCourseCodeInfoApi(this.dataForm.id).then(data => {
            if (data) {
              this.dataForm.cmsCourseId = data.cmsCourseId
              this.dataForm.userId = data.userId
              this.dataForm.inviteCode = data.inviteCode
              this.dataForm.isUse = data.isUse
              this.dataForm.useTime = data.useTime
            }
          }).catch((res) => {
            this.$message.error(res.message)
          })
        } else {
          // 新增时自动生成随机邀请码
          this.dataForm.inviteCode = this.generateInviteCode()
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 未使用状态时，清空使用时间
          if (this.dataForm.isUse === 0) {
            this.dataForm.useTime = null
          }
          
          const submitApi = this.dataForm.id ? cmsCourseCodeUpdateApi : cmsCourseCodeAddApi
          submitApi({ ...this.dataForm }).then(() => {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch((res) => {
            this.$message.error(res.message)
          })
        }
      })
    },
    // 生成随机邀请码
    generateInviteCode() {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
      let result = ''
      for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    }
  }
}
</script> 