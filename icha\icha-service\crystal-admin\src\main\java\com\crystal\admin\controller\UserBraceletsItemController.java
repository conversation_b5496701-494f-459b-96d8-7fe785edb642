package com.crystal.admin.controller;

import com.crystal.common.model.user.UserBraceletsItemEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.UserBraceletsItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;


/**
 * 用户手串 控制器
 * | Author: 陈佳音
 * ｜ @date Fri Nov 22 16:43:13 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("userbraceletsitem")
public class UserBraceletsItemController {
    @Autowired
    private UserBraceletsItemService userBraceletsItemService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('userbraceletsitem:list')")
    public CommonResult<CommonPage<UserBraceletsItemEntity>> list(@Validated UserBraceletsItemEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<UserBraceletsItemEntity> page = CommonPage.restPage(userBraceletsItemService.queryPage(request,pageParamRequest));

        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('userbraceletsitem:info')")
    public CommonResult<UserBraceletsItemEntity> info(@PathVariable("id") Integer id){
		UserBraceletsItemEntity userBraceletsItem = userBraceletsItemService.getById(id);

        return CommonResult.success(userBraceletsItem);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('userbraceletsitem:save')")
    public CommonResult<String> save(@RequestBody UserBraceletsItemEntity userBraceletsItem){
        if (userBraceletsItemService.save(userBraceletsItem)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('userbraceletsitem:update')")
    public CommonResult<String> update(@RequestBody UserBraceletsItemEntity userBraceletsItem){
        if (userBraceletsItemService.updateById(userBraceletsItem)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('userbraceletsitem:delete')")
    public CommonResult<String> delete(@RequestBody Integer[] ids){
        if (userBraceletsItemService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
