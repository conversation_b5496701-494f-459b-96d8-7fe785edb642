import Layout from '@/layout'

const cmsRouter = {
  path: '/cms',
  component: Layout,
  redirect: '/cms',
  name: 'cms',
  meta: {
    title: '内容管理',
    icon: 'clipboard'
  },
  children: [
    {
      path: 'course',
      component: () => import('@/views/cms/course'),
        name: 'course',
      meta: { title: '课程管理', icon: '' }
    },
    {
      path: 'certificate',
      component: () => import('@/views/cms/certificate'),
      name: 'certificate',
      meta: { title: '证书管理', icon: '' }
    },
    {
      path: 'download',
      component: () => import('@/views/cms/download'),
      name: 'download',
      meta: { title: '下载管理', icon: '' }
    },
    {
      path: 'healers',
      component: () => import('@/views/cms/healers'),
      name: 'healers',
      meta: { title: '疗愈师管理', icon: '' }
    },
    {
      path: 'workshop',
      component: () => import('@/views/cms/workshop'),
      name: 'workshop',
      meta: { title: '工作坊管理', icon: '' }
    },
    {
      path: 'workshopAttend',
      component: () => import('@/views/cms/workshop-attend'),
      name: 'workshopAttend',
      meta: { title: '工作坊报名管理', icon: '' }
    },
    {
      path: 'courseVideo',
      component: () => import('@/views/cms/course-video'),
      name: 'courseVideo',
      meta: { title: '课程视频管理', icon: '' }
    },  
    {
      path: 'courseAttend',
      component: () => import('@/views/cms/course-attend'),
      name: 'courseAttend',
      meta: { title: '课程报名管理', icon: '' }
    },
    {
      path: 'courseCode',
      component: () => import('@/views/cms/course-code'),
      name: 'courseCode',
      meta: { title: '课程邀请码管理', icon: '' }
    },
  ]
}

export default cmsRouter
