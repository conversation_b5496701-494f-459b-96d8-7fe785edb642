<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <!-- <el-form-item label="课程ID" prop="cmsCourseId">
        <el-input v-model="dataForm.cmsCourseId" placeholder="课程ID"></el-input>
      </el-form-item> -->
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户ID"></el-input>
      </el-form-item>
      <!-- <el-form-item label="报名状态" prop="status">
        <el-select v-model="dataForm.status" placeholder="报名状态">
          <el-option v-for="item in attendStatusOptions" :key="item.key" :label="item.value" :value="item.key">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="邀请码" prop="inviteCode">
        <el-input v-model="dataForm.inviteCode" placeholder="邀请码"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { 
  cmsCourseAttendInfoApi, 
  cmsCourseAttendAddApi, 
  cmsCourseAttendUpdateApi 
} from '@/api/cmsCourseAttend'
import { attendStatus } from '@/data/common'

export default {
  data() {
    return {
      visible: false,
      attendStatusOptions: attendStatus,
      dataForm: {
        id: 0,
        cmsCourseId: '',
        userId: '',
        status: 0,
        inviteCode: ''
      },
      dataRule: {
        cmsCourseId: [
          { required: true, message: '课程ID不能为空', trigger: 'blur' }
        ],
        userId: [
          { required: true, message: '用户ID不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '报名状态不能为空', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init(id, cmsCourseId) {
      this.dataForm.id = id || 0
      this.dataForm.cmsCourseId = cmsCourseId || ''
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          cmsCourseAttendInfoApi(this.dataForm.id).then(data => {
            if (data) {
              this.dataForm.cmsCourseId = data.cmsCourseId
              this.dataForm.userId = data.userId
              this.dataForm.status = data.status
              this.dataForm.inviteCode = data.inviteCode
            }
          }).catch((res) => {
            this.$message.error(res.message)
          })
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const submitApi = this.dataForm.id ? cmsCourseAttendUpdateApi : cmsCourseAttendAddApi
          submitApi({ ...this.dataForm }).then(() => {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch((res) => {
            this.$message.error(res.message)
          })
        }
      })
    }
  }
}
</script> 