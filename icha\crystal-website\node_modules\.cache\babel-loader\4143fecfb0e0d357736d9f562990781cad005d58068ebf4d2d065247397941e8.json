{"ast": null, "code": "// 引入 axios\nimport request from \"./request\";\n\n//传送json格式的get请求\nexport const getRequest = (url, params) => {\n  return request({\n    method: 'get',\n    url: `${url}`,\n    params: params,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n};\n//传送json格式的get请求\nexport const getWxRequest = (url, params) => {\n  return request({\n    method: 'get',\n    url: `${url}`,\n    params: params,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669',\n      'wx-client-href': location.href\n    }\n  });\n};\n//传送json格式的get请求\nexport const postRequest = (url, data) => {\n  return request({\n    method: 'post',\n    url: `${url}`,\n    data: data,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n};\nexport const postRequestParams = (url, data) => {\n  return request({\n    method: 'post',\n    url: `${url}`,\n    params: data,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n};\n\n/**\r\n * 脉轮测试配置\r\n */\nexport function mailunConfig() {\n  return request({\n    method: 'get',\n    url: 'mailunConfig',\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n}\n\n/**\r\n * 开始脉轮测试\r\n */\nexport function questionStartExam(data) {\n  return request({\n    method: 'get',\n    url: 'question/startExam',\n    params: data,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n}\n\n/**\r\n * 获取脉轮测试详情\r\n */\nexport function questionDetail(data) {\n  return request({\n    method: 'get',\n    url: 'question/detail',\n    params: data,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n}\n\n/**\r\n * 提交脉轮测试\r\n */\nexport function questionSubmitExam(data) {\n  return request({\n    method: 'post',\n    url: 'question/submitExam',\n    data: data,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n}\n\n/**\r\n * 暂存脉轮测试\r\n */\nexport function questionSaveExam(data) {\n  return request({\n    method: 'post',\n    url: 'question/saveExam',\n    data: data,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n}\n\n/**\r\n * 获取脉轮测试结果详情\r\n */\nexport function questionFinishDetail(data) {\n  return request({\n    method: 'get',\n    url: 'question/finishDetail',\n    params: data,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n}\n\n/**\r\n * 获取脉轮测试列表\r\n */\nexport function questionList(data) {\n  return request({\n    method: 'get',\n    url: 'question/list',\n    params: data,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n}\n\n/**\r\n * 删除脉轮测试记录\r\n */\nexport function questionUserDelete(data) {\n  return request({\n    method: 'get',\n    url: 'question/questionUserDelete',\n    params: data,\n    headers: {\n      'appid': 'wx6a7f38e0347e6669'\n    }\n  });\n}", "map": {"version": 3, "names": ["request", "getRequest", "url", "params", "method", "headers", "getWxRequest", "location", "href", "postRequest", "data", "postRequestParams", "mailunConfig", "questionStartExam", "questionDetail", "questionSubmitExam", "questionSaveExam", "questionFinishDetail", "questionList", "questionUserDelete"], "sources": ["C:/Users/<USER>/Desktop/code/crystal-mall/icha/crystal-website/src/api/api.js"], "sourcesContent": ["// 引入 axios\r\nimport request from \"./request\";\r\n\r\n\r\n\r\n//传送json格式的get请求\r\nexport const getRequest=(url,params)=>{\r\n    return request({\r\n        method:'get',\r\n        url:`${url}`,\r\n        params: params,\r\n        headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n    })\r\n}\r\n//传送json格式的get请求\r\nexport const getWxRequest=(url,params)=>{\r\n    return request({\r\n        method:'get',\r\n        url:`${url}`,\r\n        params: params,\r\n        headers: { 'appid': 'wx6a7f38e0347e6669' ,\r\n        'wx-client-href':location.href},\r\n    })\r\n}\r\n//传送json格式的get请求\r\nexport const postRequest=(url,data)=>{\r\n    return request({\r\n        method:'post',\r\n        url:`${url}`,\r\n        data: data,\r\n        headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n\r\n    })\r\n}\r\nexport const postRequestParams=(url,data)=>{\r\n    return request({\r\n        method:'post',\r\n        url:`${url}`,\r\n        params: data,\r\n        headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n\r\n    })\r\n}\r\n\r\n/**\r\n * 脉轮测试配置\r\n */\r\nexport function mailunConfig() {\r\n  return request({\r\n    method: 'get',\r\n    url: 'mailunConfig',\r\n    headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n  });\r\n}\r\n\r\n/**\r\n * 开始脉轮测试\r\n */\r\nexport function questionStartExam(data) {\r\n  return request({\r\n    method: 'get',\r\n    url: 'question/startExam',\r\n    params: data,\r\n    headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n  });\r\n}\r\n\r\n/**\r\n * 获取脉轮测试详情\r\n */\r\nexport function questionDetail(data) {\r\n  return request({\r\n    method: 'get',\r\n    url: 'question/detail',\r\n    params: data,\r\n    headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n  });\r\n}\r\n\r\n/**\r\n * 提交脉轮测试\r\n */\r\nexport function questionSubmitExam(data) {\r\n  return request({\r\n    method: 'post',\r\n    url: 'question/submitExam',\r\n    data: data,\r\n    headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n  });\r\n}\r\n\r\n/**\r\n * 暂存脉轮测试\r\n */\r\nexport function questionSaveExam(data) {\r\n  return request({\r\n    method: 'post',\r\n    url: 'question/saveExam',\r\n    data: data,\r\n    headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n  });\r\n}\r\n\r\n/**\r\n * 获取脉轮测试结果详情\r\n */\r\nexport function questionFinishDetail(data) {\r\n  return request({\r\n    method: 'get',\r\n    url: 'question/finishDetail',\r\n    params: data,\r\n    headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n  });\r\n}\r\n\r\n/**\r\n * 获取脉轮测试列表\r\n */\r\nexport function questionList(data) {\r\n  return request({\r\n    method: 'get',\r\n    url: 'question/list',\r\n    params: data,\r\n    headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n  });\r\n}\r\n\r\n/**\r\n * 删除脉轮测试记录\r\n */\r\nexport function questionUserDelete(data) {\r\n  return request({\r\n    method: 'get',\r\n    url: 'question/questionUserDelete',\r\n    params: data,\r\n    headers: { 'appid': 'wx6a7f38e0347e6669' },\r\n  });\r\n}\r\n"], "mappings": "AAAA;AACA,OAAOA,OAAO,MAAM,WAAW;;AAI/B;AACA,OAAO,MAAMC,UAAU,GAACA,CAACC,GAAG,EAACC,MAAM,KAAG;EAClC,OAAOH,OAAO,CAAC;IACXI,MAAM,EAAC,KAAK;IACZF,GAAG,EAAE,GAAEA,GAAI,EAAC;IACZC,MAAM,EAAEA,MAAM;IACdE,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAC7C,CAAC,CAAC;AACN,CAAC;AACD;AACA,OAAO,MAAMC,YAAY,GAACA,CAACJ,GAAG,EAACC,MAAM,KAAG;EACpC,OAAOH,OAAO,CAAC;IACXI,MAAM,EAAC,KAAK;IACZF,GAAG,EAAE,GAAEA,GAAI,EAAC;IACZC,MAAM,EAAEA,MAAM;IACdE,OAAO,EAAE;MAAE,OAAO,EAAE,oBAAoB;MACxC,gBAAgB,EAACE,QAAQ,CAACC;IAAI;EAClC,CAAC,CAAC;AACN,CAAC;AACD;AACA,OAAO,MAAMC,WAAW,GAACA,CAACP,GAAG,EAACQ,IAAI,KAAG;EACjC,OAAOV,OAAO,CAAC;IACXI,MAAM,EAAC,MAAM;IACbF,GAAG,EAAE,GAAEA,GAAI,EAAC;IACZQ,IAAI,EAAEA,IAAI;IACVL,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAE7C,CAAC,CAAC;AACN,CAAC;AACD,OAAO,MAAMM,iBAAiB,GAACA,CAACT,GAAG,EAACQ,IAAI,KAAG;EACvC,OAAOV,OAAO,CAAC;IACXI,MAAM,EAAC,MAAM;IACbF,GAAG,EAAE,GAAEA,GAAI,EAAC;IACZC,MAAM,EAAEO,IAAI;IACZL,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAE7C,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACA,OAAO,SAASO,YAAYA,CAAA,EAAG;EAC7B,OAAOZ,OAAO,CAAC;IACbI,MAAM,EAAE,KAAK;IACbF,GAAG,EAAE,cAAc;IACnBG,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAC3C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,SAASQ,iBAAiBA,CAACH,IAAI,EAAE;EACtC,OAAOV,OAAO,CAAC;IACbI,MAAM,EAAE,KAAK;IACbF,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAEO,IAAI;IACZL,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAC3C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,SAASS,cAAcA,CAACJ,IAAI,EAAE;EACnC,OAAOV,OAAO,CAAC;IACbI,MAAM,EAAE,KAAK;IACbF,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAEO,IAAI;IACZL,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAC3C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,SAASU,kBAAkBA,CAACL,IAAI,EAAE;EACvC,OAAOV,OAAO,CAAC;IACbI,MAAM,EAAE,MAAM;IACdF,GAAG,EAAE,qBAAqB;IAC1BQ,IAAI,EAAEA,IAAI;IACVL,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAC3C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,SAASW,gBAAgBA,CAACN,IAAI,EAAE;EACrC,OAAOV,OAAO,CAAC;IACbI,MAAM,EAAE,MAAM;IACdF,GAAG,EAAE,mBAAmB;IACxBQ,IAAI,EAAEA,IAAI;IACVL,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAC3C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,SAASY,oBAAoBA,CAACP,IAAI,EAAE;EACzC,OAAOV,OAAO,CAAC;IACbI,MAAM,EAAE,KAAK;IACbF,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAEO,IAAI;IACZL,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAC3C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,SAASa,YAAYA,CAACR,IAAI,EAAE;EACjC,OAAOV,OAAO,CAAC;IACbI,MAAM,EAAE,KAAK;IACbF,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAEO,IAAI;IACZL,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAC3C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,OAAO,SAASc,kBAAkBA,CAACT,IAAI,EAAE;EACvC,OAAOV,OAAO,CAAC;IACbI,MAAM,EAAE,KAAK;IACbF,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAEO,IAAI;IACZL,OAAO,EAAE;MAAE,OAAO,EAAE;IAAqB;EAC3C,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}