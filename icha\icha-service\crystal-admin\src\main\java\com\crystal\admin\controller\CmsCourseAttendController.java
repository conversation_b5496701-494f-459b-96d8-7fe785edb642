package com.crystal.admin.controller;

import com.crystal.common.model.cms.CmsCourseAttendEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsCourseAttendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 课程报名 控制器
 * | Author: 陈佳音
 * ｜ @date Mon May 14 22:50:31 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/course/attend")
public class CmsCourseAttendController {
    @Autowired
    private CmsCourseAttendService cmsCourseAttendService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('cmscoursesattend:list')")
    public CommonResult<CommonPage<CmsCourseAttendEntity>> list(@Validated CmsCourseAttendEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsCourseAttendEntity> page = CommonPage.restPage(cmsCourseAttendService.queryPage(request,pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('cmscoursesattend:info')")
    public CommonResult<CmsCourseAttendEntity> info(@PathVariable("id") Long id){
        CmsCourseAttendEntity cmsCourseAttend = cmsCourseAttendService.getById(id);
        return CommonResult.success(cmsCourseAttend);
    }
    
    /**
     * 根据课程ID查询报名列表
     */
    @RequestMapping("/course/{cmsCourseId}")
//    @PreAuthorize("hasAuthority('cmscoursesattend:list')")
    public CommonResult<List<CmsCourseAttendEntity>> listByCourseId(@PathVariable("cmsCourseId") Long cmsCourseId){
        List<CmsCourseAttendEntity> list = cmsCourseAttendService.queryByCourseId(cmsCourseId);
        return CommonResult.success(list);
    }
    
    /**
     * 根据用户ID查询报名列表
     */
    @RequestMapping("/user/{userId}")
//    @PreAuthorize("hasAuthority('cmscoursesattend:list')")
    public CommonResult<List<CmsCourseAttendEntity>> listByUserId(@PathVariable("userId") Integer userId){
        List<CmsCourseAttendEntity> list = cmsCourseAttendService.queryByUserId(userId);
        return CommonResult.success(list);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('cmscoursesattend:save')")
    public CommonResult<String> save(@RequestBody CmsCourseAttendEntity cmsCourseAttend){
        cmsCourseAttend.setAddTime(new Date());
        cmsCourseAttendService.save(cmsCourseAttend);
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('cmscoursesattend:update')")
    public CommonResult<String> update(@RequestBody CmsCourseAttendEntity cmsCourseAttend){
        cmsCourseAttendService.updateById(cmsCourseAttend);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('cmscoursesattend:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids){
        if (cmsCourseAttendService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 