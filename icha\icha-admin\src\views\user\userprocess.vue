<template>
  <div class="divBox relative">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
      <el-form-item style="float:right">
        <el-button @click="$router.go(-1)" type="primary">返回上一页</el-button>
      </el-form-item>
    </el-form>
    <div class="block">
      <el-timeline>
        <el-timeline-item v-for="item in dataList" :timestamp="item.doTime || item.addTime" placement="top">
          <el-card>
            <div style="height: 30px;line-height: 30px;font-size: 20px;font-weight: bold;">{{ item.name }}</div>
            <div style="height: 30px;line-height: 30px;font-size: 18px;font-weight: bold;">当前用户状态：{{ item.userStatus }}</div>
            <div  style="margin-top: 10px;" v-html="item.remarks"></div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import { userprocessFindByUserId } from '@/api/userprocess'
import AddOrUpdate from './userprocess-add-and-update'
export default {
  data() {
    return {
      userId: '',
      dataList: [],
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  mounted() {
    this.userId = this.$route.query.id
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      userprocessFindByUserId({ userId: this.userId }).then(res => {
        this.dataList = res || [];
        this.dataListLoading = false
      }).catch(() => {
        this.dataList = []
        this.dataListLoading = false
      })
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, this.userId)
      })
    },
    // 删除
    deleteHandle(id) {
      // var ids = id ? [id] : this.dataListSelections.map(item => {
      //   return item.id
      // })
      this.$confirm(`确定删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        questionDeleteApi(id).then(() => {
          this.$message.success("删除成功");
          this.getDataList();
        }).catch((res) => {
          this.$message.error(res.message)
        });
      })
    }
  }
}
</script>
