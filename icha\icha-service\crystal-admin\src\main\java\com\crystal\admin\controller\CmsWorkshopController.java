package com.crystal.admin.controller;

import com.crystal.common.model.cms.CmsWorkshopEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsWorkshopService;
import com.crystal.service.service.SystemAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;

/**
 * 工作坊 控制器
 * | Author: 陈佳音
 * ｜ @date Mon Apr 29 10:10:51 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/workshop")
public class CmsWorkshopController {
    @Autowired
    private CmsWorkshopService cmsWorkshopService;
    @Autowired
    private SystemAttachmentService systemAttachmentService;
    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('cmsworkshop:list')")
    public CommonResult<CommonPage<CmsWorkshopEntity>> list(@Validated CmsWorkshopEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsWorkshopEntity> page = CommonPage.restPage(cmsWorkshopService.queryPage(request,pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('cmsworkshop:info')")
    public CommonResult<CmsWorkshopEntity> info(@PathVariable("id") Long id){
        CmsWorkshopEntity cmsWorkshop = cmsWorkshopService.getById(id);
        return CommonResult.success(cmsWorkshop);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('cmsworkshop:save')")
    public CommonResult<String> save(@RequestBody CmsWorkshopEntity cmsWorkshop){
        cmsWorkshop.setAddTime(new Date());
        cmsWorkshop.setAvatar(systemAttachmentService.clearPrefix(cmsWorkshop.getAvatar()));
        cmsWorkshopService.save(cmsWorkshop);
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('cmsworkshop:update')")
    public CommonResult<String> update(@RequestBody CmsWorkshopEntity cmsWorkshop){
        cmsWorkshop.setAvatar(systemAttachmentService.clearPrefix(cmsWorkshop.getAvatar()));
        cmsWorkshopService.updateById(cmsWorkshop);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('cmsworkshop:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids){
        if (cmsWorkshopService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 