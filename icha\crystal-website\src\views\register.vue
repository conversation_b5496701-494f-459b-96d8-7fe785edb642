<template>
	<Layout>
		<div class="register-page">
			<div class="register-container">
				<div class="register-left">
					<div class="register-logo">
						<img src="@/assets/images/logo.png" alt="国际水晶疗愈协会" class="logo-image" />
					</div>
					<div class="register-welcome">
						<h2>欢迎加入</h2>
						<h1>国际水晶疗愈协会</h1>
						<p>成为会员后享受专业的水晶疗愈服务和丰富的学习资源</p>
					</div>
					<div class="register-decoration">
						<div class="crystal-1"></div>
						<div class="crystal-2"></div>
						<div class="crystal-3"></div>
					</div>
				</div>

				<div class="register-right">
					<div class="register-form-container">
						<h2 class="register-title">用户注册</h2>
						<p class="register-subtitle">创建您的账号，加入我们的会员</p>

						<div v-if="currentStep == 1">
							<van-form @submit="submitRegister">
								<!-- <div class="form-item">
									<label for="username">用户名</label>
									<van-field v-model="registerForm.username" name="username" placeholder="请输入用户名"
										:rules="[
											{ required: true, message: '请输入用户名' },
											{ pattern: /^[a-zA-Z0-9_]{4,16}$/, message: '用户名为4-16位字母、数字或下划线' }
										]" class="register-input" />
								</div> -->

								<div class="form-item">
									<label for="phone">手机号</label>
									<van-field v-model="registerForm.phone" name="phone" placeholder="请输入手机号" :rules="[
										{ required: true, message: '请输入手机号' },
										{ validator: validatePhone, message: '请输入正确的手机号' }
									]" class="register-input" />
								</div>

								<div class="form-item">
									<label for="verificationCode">验证码</label>
									<div class="verification-code-container">
										<van-field v-model="registerForm.verificationCode" name="verificationCode"
											placeholder="请输入验证码" :rules="[{ required: true, message: '请输入验证码' }]"
											class="register-input verification-input" />
										<van-button size="small" type="primary" class="verification-btn"
											:disabled="counting > 0" @click="sendVerificationCode">
											{{ counting > 0 ? `${counting}秒后重试` : '获取验证码' }}
										</van-button>
									</div>
								</div>

								<div class="form-item">
									<label for="password">设置密码</label>
									<van-field v-model="registerForm.password" type="password" name="password"
										placeholder="请设置密码" :rules="[
											{ required: true, message: '请设置密码' },
											{ pattern: /^.{8,20}$/, message: '密码长度为8-20个字符' }
										]" class="register-input" />
								</div>

								<div class="form-item">
									<label for="confirmPassword">确认密码</label>
									<van-field v-model="registerForm.confirmPassword" type="password"
										name="confirmPassword" placeholder="请再次输入密码" :rules="[
											{ required: true, message: '请确认密码' },
											{ validator: validateConfirmPassword, message: '两次输入密码不一致' }
										]" class="register-input" />
								</div>

								<div class="form-item">
									<label for="nickName">昵称</label>
									<van-field v-model="registerForm.nickName" name="nickName" placeholder="请输入昵称"
										:rules="[{ required: true, message: '请输入昵称' }]" class="register-input" />
								</div>

								<div class="form-item">
									<label>会员协议</label>
									<div class="agreement-container">
										<van-checkbox v-model="registerForm.agreement" shape="square">
											我已阅读并同意<a @click.stop="showAgreement" class="agreement-link">《会员服务协议》</a>
										</van-checkbox>
									</div>
								</div>

								<div class="form-submit">
									<van-button round block type="primary" native-type="submit" :loading="loading"
										:disabled="!registerForm.agreement">
										立即注册
									</van-button>
								</div>
							</van-form>
						</div>

						<!-- 注册成功 -->
						<div v-if="currentStep == 2" class="register-success">
							<van-icon name="success" size="64" color="#5e258f" />
							<h3>注册成功</h3>
							<p>恭喜您已成功注册成为会员</p>
							<div class="form-submit">
								<van-button round block type="primary" @click="goToLogin">
									立即登录
								</van-button>
							</div>
						</div>

						<div class="login-link" v-if="currentStep !== 2">
							<span>已有账号？</span>
							<router-link to="/login">立即登录</router-link>
						</div>
					</div>
				</div>
			</div>
		</div>
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone, isMobile } from "@/utils/index";
import Message from "@/utils/message";

export default {
	name: "RegisterView",
	data() {
		return {
			isMobilePhone: isMobilePhone(),
			loading: false,
			currentStep: 1,
			counting: 0,
			timer: null,
			registerForm: {
				username: '',
				phone: '',
				verificationCode: '',
				password: '',
				confirmPassword: '',
				nickName: '',
				agreement: false
			}
		}
	},
	components: {
		Layout
	},
	mounted() {
		this.$wxShare();
	},
	beforeDestroy() {
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		// 验证手机号格式
		validatePhone() {
			return isMobile(this.registerForm.phone);
		},

		// 验证两次密码是否一致
		validateConfirmPassword() {
			return this.registerForm.password == this.registerForm.confirmPassword;
		},

		// 发送验证码
		sendVerificationCode() {
			if (this.counting > 0) return;

			if (!this.validatePhone()) {
				Message.error("请输入正确的手机号");
				return;
			}

			// 用户名可用，发送验证码
			this.postRequestParams("/sendCode", {
				// username: this.registerForm.username,
				phone: this.registerForm.phone
			})
				.then(resp => {
					console.log(resp)
					if (resp && resp.code == 200) {
						Message.success("验证码已发送");
						this.startCounting();
					} else {
						Message.error(resp.message || "验证码发送失败");
					}
				});
		},

		// 开始倒计时
		startCounting() {
			this.counting = 60;
			this.timer = setInterval(() => {
				if (this.counting > 0) {
					this.counting--;
				} else {
					clearInterval(this.timer);
				}
			}, 1000);
		},

		// 一步完成注册
		submitRegister() {
			if (!this.registerForm.agreement) {
				Message.error("请阅读并同意会员服务协议");
				return;
			}

			if (this.registerForm.password !== this.registerForm.confirmPassword) {
				Message.error("两次输入密码不一致");
				return;
			}

			this.loading = true;
			
			// 验证码正确，提交注册信息
			this.completeRegistration();
		},

		// 完成注册
		completeRegistration() {
			this.postRequest("/registerCms", {
				// username: this.registerForm.username,
				phone: this.registerForm.phone,
				password: this.registerForm.password,
				nickName: this.registerForm.nickName,
				code: this.registerForm.verificationCode
			})
				.then(resp => {
					this.loading = false;
					if (resp && resp.code == 200) {
						this.currentStep = 2;
						localStorage.setItem("token", resp.data.token);
						localStorage.setItem("userInfo", JSON.stringify(resp.data.userInfo));
						Message.success("注册成功");
					} else {
						Message.error(resp.message || "注册失败");
					}
				});
		},

		// 显示会员协议
		showAgreement() {
			this.$dialog.alert({
				title: '会员服务协议',
				message: '国际水晶疗愈协会会员服务协议内容...',
				confirmButtonText: '我已阅读并同意'
			}).then(() => {
				this.registerForm.agreement = true;
			});
		},

		// 返回登录页
		goToLogin() {
			this.$router.push("/login");
		}
	}
}
</script>

<style lang="less" scoped>
.register-page {
	min-height: 100vh;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f9fa;
	background-image: url('@/assets/images/pattern-dark.png');
	background-repeat: repeat;
	padding: 40px 20px;
}

.register-container {
	display: flex;
	width: 1200px;
	min-height: 680px;
	border-radius: 20px;
	overflow: hidden;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
	background-color: #fff;
}

.register-left {
	flex: 1;
	background: linear-gradient(135deg, #5e258f, #8647ad);
	background-image: linear-gradient(135deg, rgba(94, 37, 143, 0.95), rgba(134, 71, 173, 0.9)), url('https://images.unsplash.com/photo-1521320226546-87b106cd055d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
	background-size: cover;
	background-position: center;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	color: #fff;
	padding: 50px;
	overflow: hidden;
}

.register-logo {
	margin-bottom: 40px;
	text-align: center;
	position: relative;
	z-index: 2;
}

.logo-image {
	width: 180px;
	height: auto;
}

.register-welcome {
	text-align: center;
	margin-bottom: 40px;
	position: relative;
	z-index: 2;
}

.register-welcome h2 {
	font-size: 24px;
	font-weight: 400;
	margin-bottom: 20px;
	color: rgba(255, 255, 255, 0.9);
}

.register-welcome h1 {
	font-size: 38px;
	font-weight: 700;
	margin-bottom: 20px;
	text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.register-welcome p {
	font-size: 16px;
	line-height: 1.6;
	max-width: 400px;
	color: rgba(255, 255, 255, 0.8);
}

.register-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.crystal-1,
.crystal-2,
.crystal-3 {
	position: absolute;
	background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
	backdrop-filter: blur(5px);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.crystal-1 {
	width: 300px;
	height: 300px;
	top: -100px;
	left: -150px;
}

.crystal-2 {
	width: 200px;
	height: 200px;
	bottom: 50px;
	right: -50px;
}

.crystal-3 {
	width: 150px;
	height: 150px;
	bottom: -50px;
	left: 100px;
}

.register-right {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px;
	background-color: #fff;
}

.register-form-container {
	width: 100%;
	max-width: 400px;
}

.register-title {
	font-size: 32px;
	font-weight: 700;
	color: #333;
	margin-bottom: 10px;
	text-align: center;
}

.register-subtitle {
	font-size: 16px;
	color: #666;
	margin-bottom: 40px;
	text-align: center;
}

.form-item {
	margin-bottom: 25px;
}

.form-item label {
	display: block;
	margin-bottom: 8px;
	font-size: 14px;
	color: #555;
	font-weight: 500;
}

.register-input {
	/deep/ .van-field__control {
		height: 48px;
		padding: 0 15px;
		font-size: 15px;
		background-color: #f5f7fa;
		border: 1px solid #e8eaee;
		border-radius: 8px;
	}

	/deep/ .van-field__control:focus {
		background-color: #fff;
		border-color: #5e258f;
		box-shadow: 0 0 0 3px rgba(94, 37, 143, 0.1);
	}
}

.verification-code-container {
	display: flex;
	align-items: center;
}

.verification-input {
	flex: 1;
	margin-right: 10px;
}

.verification-btn {
	height: 48px;
	background: linear-gradient(45deg, #5e258f, #8647ad);
	border-color: #5e258f;
	white-space: nowrap;
}

.agreement-container {
	display: flex;
	align-items: center;

	/deep/ .van-checkbox__label {
		color: #666;
		font-size: 14px;
	}

	/deep/ .van-checkbox__icon--checked {
		background-color: #5e258f;
		border-color: #5e258f;
	}
}

.agreement-link {
	color: #5e258f;
	text-decoration: none;

	&:hover {
		text-decoration: underline;
	}
}

.form-submit {
	margin-bottom: 25px;

	.van-button {
		height: 50px;
		font-size: 16px;
		font-weight: 500;
		background: linear-gradient(45deg, #5e258f, #8647ad);
		border-color: #5e258f;
		transition: all 0.3s ease;

		&:hover {
			background: linear-gradient(45deg, #4b1e73, #733c94);
			border-color: #4b1e73;
			transform: translateY(-2px);
			box-shadow: 0 6px 15px rgba(94, 37, 143, 0.25);
		}

		&--disabled {
			opacity: 0.6;
			background: linear-gradient(45deg, #5e258f, #8647ad);
		}
	}
}

.login-link {
	text-align: center;
	font-size: 14px;
	color: #666;

	a {
		color: #5e258f;
		font-weight: 500;
		text-decoration: none;
		margin-left: 5px;

		&:hover {
			text-decoration: underline;
		}
	}
}

.register-success {
	text-align: center;
	padding: 20px 0 40px;

	h3 {
		font-size: 24px;
		font-weight: 600;
		margin: 20px 0 10px;
		color: #333;
	}

	p {
		font-size: 16px;
		color: #666;
		margin-bottom: 30px;
	}
}

@media (max-width: 1200px) {
	.register-container {
		width: 95%;
		flex-direction: column;
		min-height: auto;
	}

	.register-left {
		padding: 40px 20px;
	}

	.register-right {
		padding: 40px 20px;
	}

	.register-welcome h1 {
		font-size: 32px;
	}
}

@media (max-width: 767px) {
	.register-page {
		padding: 20px 10px;
	}

	.register-container {
		width: 100%;
		border-radius: 10px;
	}

	.register-left {
		padding: 30px 15px;
	}

	.register-right {
		padding: 30px 15px;
	}

	.register-logo .logo-image {
		width: 150px;
	}

	.register-welcome h1 {
		font-size: 28px;
	}

	.register-welcome p {
		font-size: 14px;
	}

	.register-title {
		font-size: 28px;
	}

	.register-subtitle {
		font-size: 14px;
		margin-bottom: 30px;
	}
}
</style>