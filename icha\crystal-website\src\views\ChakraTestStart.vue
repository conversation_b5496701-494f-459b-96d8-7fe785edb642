<template>
  <Layout>
    <div class="chakra-test-start">
    <!-- 标题栏 -->
    <div class="nav-title">
      <div class="nav-left">
        <van-button 
          class="back-button"
          @click="goBack"
          plain
        >
          <van-icon name="arrow-left" size="18" />
        </van-button>
        <div class="color-bar"></div>
        <div class="title-text">开始您的脉轮测试</div>
      </div>
      <div class="nav-right">
        <van-button 
          class="save-button-small"
          @click="saveExam"
          :loading="saveLoading"
          size="small"
        >
          <van-icon name="bookmark-o" size="14" />
          <span>暂存</span>
        </van-button>
      </div>
    </div>
    
    <!-- 问题列表 -->
    <div class="question-list" v-if="questionEntities.length > 0">
      <div 
        v-for="(item, index) in questionEntities" 
        :key="item.id"
        :id="'question-' + item.id"
        class="question-card"
        :class="{'question-error': item.hasError}"
      >
        <!-- 问题标题 -->
        <div class="question-title" v-html="item.name"></div>
        
        <!-- 选项区域 -->
        <div class="options-container">
          <div class="option-label left-label">{{item.questionOptionEntities[0].name}}</div>
          
          <!-- 单选按钮组 -->
          <van-radio-group 
            v-model="questionEntities[index].selectId" 
            direction="horizontal"
            class="radio-group"
            @change="onOptionChange(index)"
          >
            <van-radio 
              v-for="(option, optionIndex) in item.questionOptionEntities" 
              :key="option.id"
              :name="option.id"
              class="radio-option"
              checked-color="#c9ab79"
            >
            </van-radio>
          </van-radio-group>
          
          <div class="option-label right-label">{{item.questionOptionEntities[4].name}}</div>
        </div>
        
        <!-- 错误提示 -->
        <div v-if="item.hasError" class="error-tip">请选择一个选项</div>
      </div>
    </div>
    
    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <van-button 
        type="primary"
        class="submit-button"
        @click="submitExam"
        :loading="submitLoading"
        block
      >
        <van-icon name="success" size="16" />
        <span>提交测试</span>
      </van-button>
    </div>
    
    <!-- 加载状态 -->
    <van-loading v-if="pageLoading" class="page-loading" />
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/common/Layout.vue'
import Message from '@/utils/message'

export default {
  components: {
    Layout
  },
  name: 'ChakraTestStart',
  data() {
    return {
      token: '',
      questionUserId: '',
      questionEntities: [],
      questionUserEntity: {},
      pageLoading: true,
      saveLoading: false,
      submitLoading: false
    }
  },
  mounted() {
    this.token = this.$route.query.token
    this.questionUserId = this.$route.query.questionUserId
    
    if (!this.token || !this.questionUserId) {
      Message.error('参数错误')
      this.$router.back()
      return
    }
    
    this.getQuestionDetail()
  },
  methods: {
    // 获取问题详情
    getQuestionDetail() {
      this.pageLoading = true
      this.getRequest('/question/detail', { questionUserId: this.questionUserId }).then(res => {
        this.pageLoading = false
        if (res.code == 200) {
          this.questionEntities = res.data.questionEntities || []
          this.questionUserEntity = res.data.questionUserEntity || {}
        } else {
          if (res.message === '您已经参与过答题') {
            Message.warning('您已经参与过答题')
            this.$router.replace({
              path: '/chakra-test/detail',
              query: { questionUserId: this.questionUserId }
            })
          } else {
            Message.error(res.message || '获取问题失败')
            this.$router.back()
          }
        }
      }).catch(err => {
        this.pageLoading = false
        Message.error('获取问题失败')
        console.error('获取问题失败:', err)
      })
    },
    
    // 选项改变
    onOptionChange(index) {
      if (this.questionEntities[index].hasError) {
        this.$set(this.questionEntities[index], 'hasError', false)
      }
    },
    
    // 重置错误提示
    resetErrors() {
      this.questionEntities.forEach(item => {
        this.$set(item, 'hasError', false)
      })
    },
    
    // 验证表单
    validateForm() {
      this.resetErrors()
      let isValid = true
      let firstErrorId = null
      
      for (let i = 0; i < this.questionEntities.length; i++) {
        const question = this.questionEntities[i]
        if (!question.selectId) {
          this.$set(question, 'hasError', true)
          if (!firstErrorId) {
            firstErrorId = question.id
          }
          isValid = false
        }
      }
      
      if (!isValid && firstErrorId) {
        // 滚动到第一个错误问题
        setTimeout(() => {
          const element = document.getElementById('question-' + firstErrorId)
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' })
          }
        }, 100)
      }
      
      return isValid
    },
    
    // 暂存答案
    saveExam() {
      if (!this.validateForm()) {
        Message.warning('请完成所有问题')
        return
      }
      
      this.saveLoading = true
      const data = {
        token: this.token,
        questionUserId: this.questionUserId,
        questionEntities: this.questionEntities
      }
      
      this.postRequest('/question/saveExam', data).then(res => {
        this.saveLoading = false
        if (res.code == 200) {
          Message.success('暂存成功')
          this.$router.back()
        } else {
          Message.error(res.message || '暂存失败')
        }
      }).catch(err => {
        this.saveLoading = false
        Message.error('暂存失败')
        console.error('暂存失败:', err)
      })
    },
    
    // 提交答案
    submitExam() {
      if (!this.validateForm()) {
        Message.warning('请完成所有问题')
        return
      }
      
      this.submitLoading = true
      const data = {
        token: this.token,
        questionUserId: this.questionUserId,
        questionEntities: this.questionEntities
      }
      
      this.postRequest('/question/submitExam', data).then(res => {
        this.submitLoading = false
        if (res.code == 200) {
          Message.success('提交成功')
          this.$router.replace({
            path: '/chakra-test/detail',
            query: { questionUserId: this.questionUserId }
          })
        } else {
          Message.error(res.message || '提交失败')
        }
      }).catch(err => {
        this.submitLoading = false
        Message.error('提交失败')
        console.error('提交失败:', err)
      })
    },
    
    // 返回上一页
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.chakra-test-start {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding-bottom: 80px;
}

.nav-title {
  padding: 15px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(86, 70, 128, 0.1);
  border-radius: 0 0 20px 20px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-right {
  display: flex;
  align-items: center;
}

.back-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(86, 70, 128, 0.08);
  border: 1px solid rgba(86, 70, 128, 0.2);
  color: #564680;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(86, 70, 128, 0.15);
  border-color: rgba(86, 70, 128, 0.4);
  transform: translateX(-2px);
}

.color-bar {
  width: 6px;
  height: 25px;
  background: linear-gradient(135deg, #564680, #516790, #c9ab79);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(86, 70, 128, 0.3);
}

.title-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  letter-spacing: 0.5px;
}

.save-button-small {
  background: linear-gradient(135deg, #564680, #516790);
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  color: white;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);
  transition: all 0.3s ease;
}

.save-button-small:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);
}

.save-button-small span {
  margin-left: 4px;
}

.question-list {
  padding: 0 20px;
}

.question-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  margin-bottom: 20px;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(86, 70, 128, 0.08);
  overflow: hidden;
  transition: all 0.4s ease;
  border: 2px solid transparent;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #564680, #516790, #c9ab79);
  }
  
  &.question-error {
    border-color: #ff6b6b;
    animation: shake 0.6s ease-in-out;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.2);
  }
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(86, 70, 128, 0.12);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-3px); }
  40%, 80% { transform: translateX(3px); }
}

.question-title {
  padding: 15px;
  font-weight: 600;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.options-container {
  display: flex;
  align-items: center;
  padding: 15px;
  justify-content: space-between;
}

.option-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  min-width: 50px;
  
  &.left-label {
    text-align: left;
  }
  
  &.right-label {
    text-align: right;
  }
}

.radio-group {
  flex: 1;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  
  .radio-option {
    margin: 0 4px;
  }
}

.error-tip {
  color: #ff6b6b;
  font-size: 12px;
  padding: 0 15px 10px;
  text-align: center;
}

.bottom-buttons {
  padding: 25px;
  display: flex;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 -4px 15px rgba(86, 70, 128, 0.1);
  border-radius: 25px 25px 0 0;
  margin-top: 30px;
}

.submit-button {
  height: 50px;
  background: linear-gradient(135deg, #c9ab79, #b8996a);
  border: none;
  border-radius: 30px;
  font-size: 16px;
  font-weight: 700;
  box-shadow: 0 6px 20px rgba(201, 171, 121, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button span {
  margin-left: 6px;
}

.page-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .question-card {
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .question-title {
    padding: 16px;
    font-size: 16px;
  }
  
  .options-container {
    padding: 20px 16px;
  }
  
  .radio-group {
    padding: 0 15px;
    
    .radio-option {
      margin: 0 4px;
      transform: scale(1.1);
    }
  }
  
  .bottom-buttons {
    padding: 20px;
    gap: 10px;
    margin-top: 20px;
  }
  
  .back-button {
    font-size: 13px;
  }
  
  .save-button,
  .submit-button {
    height: 46px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .question-list {
    padding: 0 15px;
  }
  
  .nav-title {
    padding: 12px 20px;
    
    .title-text {
      font-size: 18px;
    }
  }
  
  .question-title {
    padding: 14px;
    font-size: 15px;
  }
  
  .options-container {
    padding: 18px 14px;
  }
  
  .option-label {
    font-size: 12px;
    min-width: 50px;
    padding: 6px 10px;
  }
  
  .radio-group {
    padding: 0 12px;
    
    .radio-option {
      margin: 0 3px;
      transform: scale(1.0);
    }
  }
}
</style>
