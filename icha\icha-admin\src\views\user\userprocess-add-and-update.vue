<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="记录名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="记录名称"></el-input>
      </el-form-item>
      <el-form-item label="记录状态" prop="userStatus">
        <el-select v-model="dataForm.userStatus" placeholder="记录状态" filterable>
          <el-option v-for="item in userStatus" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="记录时间" prop="doTime">
        <el-date-picker style="width: 100%" v-model="dataForm.doTime" type="datetime" :placeholder="'记录时间'"
          value-format="yyyy/MM/dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <Tinymce v-model="dataForm.remarks"></Tinymce>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { userprocessCreateApi, userprocessUpdateApi, userprocessDetailApi, userprocessDeleteApi, userprocessListApi, } from '@/api/userprocess'
  import * as systemConfigApi from '@/api/systemConfig.js'
import Tinymce from '@/components/Tinymce/index'
export default {
  data() {
    return {
      visible: false,
      userStatus: [],
      dataForm: {
        id: 0,
        userId: '',
        doTime: '',
        name: '',
        userStatus: '',
        remarks: '',
      },
      dataRule: {
        doTime: [
          { required: true, message: '记录时间不能为空', trigger: 'blur' }
        ],
        userStatus: [
          { required: true, message: '记录状态不能为空', trigger: 'blur' }
        ],
      }
    }
  },
  components: {
    Tinymce
  },
  methods: {
    init(id,userId) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      this.dataForm.remarks = ''
        if (this.dataForm.id) {

          userprocessDetailApi(this.dataForm.id).then((data) => {
            this.dataForm.name = data.name
            this.dataForm.type = data.type
            this.dataForm.userStatus = data.userStatus
            this.dataForm.userId = data.userId
            this.dataForm.remarks = data.remarks
          }).catch((res) => {
            this.$message.error(res.message)
          });
        } else {
          this.dataForm.userId = userId
        }
        this.getUserStatus();
      })
    },
      getUserStatus() {
        systemConfigApi.configGetUniq({key: "userstatus"}).then(data => {
          this.userStatus = data ? data.split(',') : []
        })
      },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.dataForm.id) {

            userprocessCreateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          } else {

            userprocessUpdateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          }
        }
      })
    }
  }
}
</script>
