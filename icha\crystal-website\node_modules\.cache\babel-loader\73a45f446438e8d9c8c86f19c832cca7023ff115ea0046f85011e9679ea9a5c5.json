{"ast": null, "code": "import { getWxRequest } from '../api/api';\n\n// 朋友圈\nconst defaultUrl = window.location.href;\nconst defaultTitle = '国际水晶疗愈协会';\nconst defaultImgUrl = 'https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/content/2025/04/23/1db257d554f7438a9b6b5a74608ded04wo7a7ghi19.png';\nconst defaultDesc = '致力于推动水晶疗愈在教育研究与实践领域的全面发展';\nexport default function wxShare(title = defaultTitle, imgUrl = defaultImgUrl, desc = defaultDesc, shareUrl = defaultUrl) {\n  if (!isWeixinBrowser()) {\n    return;\n  }\n  // let shareUrl = window.location.origin + '/client/' + window.location.hash;\n  // let shareUrl = window.location.href;\n\n  loadShareSignature();\n  wx.ready(function () {\n    desc = desc.replace('\\\\n\\\\r', \"\\n\\r\");\n    desc = desc.replace('\\\\n', \"\\n\");\n    desc = desc.replace('\\\\r', \"\\r\");\n    console.log('desc :' + desc);\n    // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。\n    wx.onMenuShareTimeline({\n      title: title,\n      // 分享标题\n      desc: desc,\n      // 分享描述\n      link: shareUrl,\n      // 分享链接\n      imgUrl: imgUrl // 分享图标\n    });\n    // 朋友\n    wx.onMenuShareAppMessage({\n      title: title,\n      // 分享标题\n      desc: desc,\n      // 分享描述\n      link: shareUrl,\n      // 分享链接\n      imgUrl: imgUrl // 分享图标\n    });\n  });\n\n  wx.error(p => {\n    console.log(p);\n  });\n}\nfunction loadShareSignature() {\n  // if (localStorage.shareSignature && localStorage.shareSignature != 'undefined') {\n  //     let shareSignature = JSON.parse(localStorage.shareSignature);\n  //     setShareConfig(shareSignature);\n  //     return;\n  // }\n  // getWxRequest(\"/wxAuth/getShareSignature\").then(resp => {\n  //     console.log(resp)\n  //     if (resp.data && resp.code !== 200) {\n  //         return\n  //     }\n  //     localStorage.shareSignature = JSON.stringify(resp.data.data);\n  //     setShareConfig(resp.data.data);\n  // })\n}\nfunction setShareConfig(shareSignature) {\n  wx.config({\n    debug: false,\n    appId: shareSignature.appId,\n    timestamp: shareSignature.wxTimestamp,\n    nonceStr: shareSignature.wxNoncestr,\n    signature: shareSignature.wxSignature,\n    jsApiList: ['checkJsApi', 'onMenuShareTimeline', 'onMenuShareAppMessage', 'chooseWXPay', 'scanQRCode', 'hideOptionMenu']\n  });\n}\nfunction isWeixinBrowser() {\n  var ua = window.navigator.userAgent.toLowerCase();\n  if (ua.match(/MicroMessenger/i) == 'micromessenger') {\n    return true;\n  } else {\n    console.info('非微信浏览器');\n    return false;\n  }\n}", "map": {"version": 3, "names": ["getWxRequest", "defaultUrl", "window", "location", "href", "defaultTitle", "defaultImgUrl", "defaultDesc", "wxShare", "title", "imgUrl", "desc", "shareUrl", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadShareSignature", "wx", "ready", "replace", "console", "log", "onMenuShareTimeline", "link", "onMenuShareAppMessage", "error", "p", "setShareConfig", "shareSignature", "config", "debug", "appId", "timestamp", "wxTimestamp", "nonceStr", "wxNoncestr", "signature", "wxSignature", "jsApiList", "ua", "navigator", "userAgent", "toLowerCase", "match", "info"], "sources": ["C:/Users/<USER>/Desktop/code/crystal-mall/icha/crystal-website/src/utils/wxShare.js"], "sourcesContent": ["import {getWxRequest} from '../api/api';\r\n\r\n// 朋友圈\r\nconst defaultUrl = window.location.href;\r\nconst defaultTitle = '国际水晶疗愈协会';\r\nconst defaultImgUrl = 'https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/content/2025/04/23/1db257d554f7438a9b6b5a74608ded04wo7a7ghi19.png';\r\nconst defaultDesc = '致力于推动水晶疗愈在教育研究与实践领域的全面发展';\r\nexport default function wxShare(title = defaultTitle, imgUrl = defaultImgUrl, desc = defaultDesc, shareUrl = defaultUrl) {\r\n    if (!isWeixinBrowser()) {\r\n        return;\r\n    }\r\n    // let shareUrl = window.location.origin + '/client/' + window.location.hash;\r\n    // let shareUrl = window.location.href;\r\n    \r\n    loadShareSignature();\r\n    wx.ready(function () {\r\n        desc = desc.replace('\\\\n\\\\r',\"\\n\\r\");\r\n        desc = desc.replace('\\\\n',\"\\n\");\r\n        desc = desc.replace('\\\\r',\"\\r\");\r\n        \r\n        console.log('desc :' + desc);\r\n        // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。\r\n        wx.onMenuShareTimeline({\r\n            title: title, // 分享标题\r\n            desc: desc, // 分享描述\r\n            link: shareUrl, // 分享链接\r\n            imgUrl: imgUrl, // 分享图标\r\n        });\r\n        // 朋友\r\n        wx.onMenuShareAppMessage({\r\n            title: title, // 分享标题\r\n            desc: desc, // 分享描述\r\n            link: shareUrl, // 分享链接\r\n            imgUrl: imgUrl, // 分享图标\r\n        });\r\n    });\r\n    wx.error(p => {\r\n        console.log(p)\r\n    });\r\n}\r\n\r\nfunction loadShareSignature() {\r\n    // if (localStorage.shareSignature && localStorage.shareSignature != 'undefined') {\r\n    //     let shareSignature = JSON.parse(localStorage.shareSignature);\r\n    //     setShareConfig(shareSignature);\r\n    //     return;\r\n    // }\r\n    // getWxRequest(\"/wxAuth/getShareSignature\").then(resp => {\r\n    //     console.log(resp)\r\n    //     if (resp.data && resp.code !== 200) {\r\n    //         return\r\n    //     }\r\n    //     localStorage.shareSignature = JSON.stringify(resp.data.data);\r\n    //     setShareConfig(resp.data.data);\r\n    // })\r\n}\r\n\r\nfunction setShareConfig(shareSignature) {\r\n    wx.config({\r\n        debug: false,\r\n        appId: shareSignature.appId,\r\n        timestamp: shareSignature.wxTimestamp,\r\n        nonceStr: shareSignature.wxNoncestr,\r\n        signature: shareSignature.wxSignature,\r\n        jsApiList: [\r\n            'checkJsApi',\r\n            'onMenuShareTimeline',\r\n            'onMenuShareAppMessage',\r\n            'chooseWXPay',\r\n            'scanQRCode',\r\n            'hideOptionMenu']\r\n    });\r\n}\r\n\r\nfunction isWeixinBrowser() {\r\n    var ua = window.navigator.userAgent.toLowerCase();\r\n    if (ua.match(/MicroMessenger/i) == 'micromessenger') {\r\n        return true;\r\n    } else {\r\n        console.info('非微信浏览器');\r\n        return false;\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,YAAY;;AAEvC;AACA,MAAMC,UAAU,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;AACvC,MAAMC,YAAY,GAAG,UAAU;AAC/B,MAAMC,aAAa,GAAG,+HAA+H;AACrJ,MAAMC,WAAW,GAAG,0BAA0B;AAC9C,eAAe,SAASC,OAAOA,CAACC,KAAK,GAAGJ,YAAY,EAAEK,MAAM,GAAGJ,aAAa,EAAEK,IAAI,GAAGJ,WAAW,EAAEK,QAAQ,GAAGX,UAAU,EAAE;EACrH,IAAI,CAACY,eAAe,CAAC,CAAC,EAAE;IACpB;EACJ;EACA;EACA;;EAEAC,kBAAkB,CAAC,CAAC;EACpBC,EAAE,CAACC,KAAK,CAAC,YAAY;IACjBL,IAAI,GAAGA,IAAI,CAACM,OAAO,CAAC,QAAQ,EAAC,MAAM,CAAC;IACpCN,IAAI,GAAGA,IAAI,CAACM,OAAO,CAAC,KAAK,EAAC,IAAI,CAAC;IAC/BN,IAAI,GAAGA,IAAI,CAACM,OAAO,CAAC,KAAK,EAAC,IAAI,CAAC;IAE/BC,OAAO,CAACC,GAAG,CAAC,QAAQ,GAAGR,IAAI,CAAC;IAC5B;IACAI,EAAE,CAACK,mBAAmB,CAAC;MACnBX,KAAK,EAAEA,KAAK;MAAE;MACdE,IAAI,EAAEA,IAAI;MAAE;MACZU,IAAI,EAAET,QAAQ;MAAE;MAChBF,MAAM,EAAEA,MAAM,CAAE;IACpB,CAAC,CAAC;IACF;IACAK,EAAE,CAACO,qBAAqB,CAAC;MACrBb,KAAK,EAAEA,KAAK;MAAE;MACdE,IAAI,EAAEA,IAAI;MAAE;MACZU,IAAI,EAAET,QAAQ;MAAE;MAChBF,MAAM,EAAEA,MAAM,CAAE;IACpB,CAAC,CAAC;EACN,CAAC,CAAC;;EACFK,EAAE,CAACQ,KAAK,CAACC,CAAC,IAAI;IACVN,OAAO,CAACC,GAAG,CAACK,CAAC,CAAC;EAClB,CAAC,CAAC;AACN;AAEA,SAASV,kBAAkBA,CAAA,EAAG;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAAA;AAGJ,SAASW,cAAcA,CAACC,cAAc,EAAE;EACpCX,EAAE,CAACY,MAAM,CAAC;IACNC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAEH,cAAc,CAACG,KAAK;IAC3BC,SAAS,EAAEJ,cAAc,CAACK,WAAW;IACrCC,QAAQ,EAAEN,cAAc,CAACO,UAAU;IACnCC,SAAS,EAAER,cAAc,CAACS,WAAW;IACrCC,SAAS,EAAE,CACP,YAAY,EACZ,qBAAqB,EACrB,uBAAuB,EACvB,aAAa,EACb,YAAY,EACZ,gBAAgB;EACxB,CAAC,CAAC;AACN;AAEA,SAASvB,eAAeA,CAAA,EAAG;EACvB,IAAIwB,EAAE,GAAGnC,MAAM,CAACoC,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC;EACjD,IAAIH,EAAE,CAACI,KAAK,CAAC,iBAAiB,CAAC,IAAI,gBAAgB,EAAE;IACjD,OAAO,IAAI;EACf,CAAC,MAAM;IACHvB,OAAO,CAACwB,IAAI,CAAC,QAAQ,CAAC;IACtB,OAAO,KAAK;EAChB;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}