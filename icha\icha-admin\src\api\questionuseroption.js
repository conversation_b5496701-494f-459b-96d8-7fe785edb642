
import request from '@/utils/request'

/**
 * 新增QuestionUserOption
 * @param pram
 */
export function QuestionUserOptionCreateApi(data) {
    return request({
        url: 'questionuseroption/save',
        method: 'POST',
        data
    })
}

/**
 * questionuseroption更新
 * @param pram
 */
export function questionuseroptionUpdateApi(data) {
    return request({
        url: 'questionuseroption/update',
        method: 'POST',
        data
    })
}

/**
 * questionuseroption详情
 * @param pram
 */
export function questionuseroptionDetailApi(id) {
    return request({
        url: `questionuseroption/info/${id}`,
        method: 'GET'
    })
}

/**
 * questionuseroption删除
 * @param pram
 */
export function questionuseroptionDeleteApi(id) {
    return request({
        url: `questionuseroption/delete/${id}`,
        method: 'get'
    })
}


/**
 * questionuseroption列表
 * @param pram
 */
export function questionuseroptionListApi(params) {
    return request({
        url: 'questionuseroption/list',
        method: 'GET',
        params
    })
}

