/**
 * 消息提示工具类，封装Vant UI消息组件
 */
// 使用全局vant对象，不需要导入
// import { Toast, Dialog } from 'vant';

// 定义消息类型
const MessageType = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error'
}

/**
 * Message类封装了消息提示的常用方法
 */
class Message {
  /**
   * 显示普通消息
   * @param {string} message 消息内容
   * @param {number} duration 显示时长(ms)，默认3000ms
   */
  static info(message, duration = 3000) {
    vant.Toast({
      message: message,
      duration: duration,
      type: MessageType.INFO
    });
  }

  /**
   * 显示成功消息
   * @param {string} message 消息内容
   * @param {number} duration 显示时长(ms)，默认3000ms
   */
  static success(message, duration = 3000) {
    vant.Toast.success({
      message: message,
      duration: duration
    });
  }

  /**
   * 显示警告消息
   * @param {string} message 消息内容
   * @param {number} duration 显示时长(ms)，默认3000ms
   */
  static warning(message, duration = 3000) {
    vant.Toast({
      message: message,
      duration: duration,
      type: MessageType.WARNING
    });
  }

  /**
   * 显示错误消息
   * @param {string} message 消息内容
   * @param {number} duration 显示时长(ms)，默认3000ms
   */
  static error(message, duration = 3000) {
    vant.Toast.fail({
      message: message,
      duration: duration
    });
  }

  /**
   * 显示加载中消息
   * @param {string} message 消息内容，默认为"加载中..."
   * @param {boolean} forbidClick 是否禁止背景点击，默认为true
   */
  static loading(message = '加载中...', forbidClick = true) {
    return vant.Toast.loading({
      message: message,
      forbidClick: forbidClick,
      duration: 0
    });
  }

  /**
   * 关闭所有消息提示
   */
  static clear() {
    vant.Toast.clear();
  }

  /**
   * 显示确认对话框
   * @param {string} title 标题
   * @param {string} message 内容
   * @param {Function} callback 确认回调函数
   * @param {Function} cancelCallback 取消回调函数
   */
  static confirm(title, message, callback, cancelCallback) {
    vant.Dialog.confirm({
      title: title,
      message: message
    }).then(() => {
      callback && callback();
    }).catch(() => {
      cancelCallback && cancelCallback();
    });
  }

  /**
   * 显示提示对话框
   * @param {string} title 标题
   * @param {string} message 内容
   * @param {Function} callback 确认回调函数
   */
  static alert(title, message, callback) {
    vant.Dialog.alert({
      title: title,
      message: message
    }).then(() => {
      callback && callback();
    });
  }
}

export default Message; 