{"ast": null, "code": "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'CourseApplyDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      required: true\n    },\n    course: {\n      type: Object,\n      default: null\n    }\n  },\n  data() {\n    return {\n      inviteCode: '',\n      loading: false\n    };\n  },\n  watch: {\n    visible(val) {\n      if (val) this.inviteCode = '';\n    },\n    course(val) {\n      this.inviteCode = '';\n    }\n  },\n  methods: {\n    closeDialog() {\n      this.$emit('close');\n    },\n    confirmApply() {\n      if (!this.course) return;\n      if (this.course.type == 1 && !this.inviteCode) {\n        this.$message && this.$message.warning('请输入邀请码');\n        return;\n      }\n      this.loading = true;\n      const params = {\n        cmsCourseId: this.course.id\n      };\n      if (this.course.type == 1 || this.course.type == 2) {\n        params.inviteCode = this.inviteCode;\n      }\n      // 支持postRequest/getRequest两种调用\n      const req = this.postRequest || this.getRequest;\n      if (!req) {\n        this.$message && this.$message.error('未注入请求方法');\n        this.loading = false;\n        return;\n      }\n      req('/cmsAttend/course', params).then(resp => {\n        this.loading = false;\n        if (resp && resp.code == 200) {\n          this.$message && this.$message.success('报名成功');\n          this.$emit('success', this.course);\n          this.closeDialog();\n        } else {\n          this.$message && this.$message.error(resp.message || '报名失败');\n        }\n      }).catch(() => {\n        this.loading = false;\n      });\n    }\n  },\n  inject: ['postRequest', 'getRequest', '$message']\n};", "map": {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "required", "course", "Object", "default", "data", "inviteCode", "loading", "watch", "val", "methods", "closeDialog", "$emit", "confirmApply", "$message", "warning", "params", "cmsCourseId", "id", "req", "postRequest", "getRequest", "error", "then", "resp", "code", "success", "message", "catch", "inject"], "sources": ["src/components/CourseApplyDialog.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"visible\" class=\"apply-dialog-mask\">\r\n    <div class=\"apply-dialog\">\r\n      <div class=\"apply-dialog-header\">\r\n        <span>课程报名</span>\r\n        <i class=\"fa fa-close apply-dialog-close\" @click=\"closeDialog\"></i>\r\n      </div>\r\n      <div class=\"apply-dialog-body\">\r\n        <p>课程名称：<b>{{course && course.title}}</b></p>\r\n        <p v-if=\"course && (course.type == 1 || course.type == 2)\">\r\n          邀请码：<input v-model=\"inviteCode\" placeholder=\"请输入邀请码\" class=\"invite-input\" />\r\n        </p>\r\n      </div>\r\n      <div class=\"apply-dialog-footer\">\r\n        <button class=\"crystal-btn\" :disabled=\"loading\" @click=\"confirmApply\">{{ loading ? '提交中...' : '确认报名' }}</button>\r\n        <button class=\" crystal-btn-secondary\" style=\"margin-left:10px;\" :disabled=\"loading\" @click=\"closeDialog\">取消</button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CourseApplyDialog',\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      required: true\r\n    },\r\n    course: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      inviteCode: '',\r\n      loading: false\r\n    }\r\n  },\r\n  watch: {\r\n    visible(val) {\r\n      if (val) this.inviteCode = '';\r\n    },\r\n    course(val) {\r\n      this.inviteCode = '';\r\n    }\r\n  },\r\n  methods: {\r\n    closeDialog() {\r\n      this.$emit('close');\r\n    },\r\n    confirmApply() {\r\n      if (!this.course) return;\r\n      if (this.course.type == 1 && !this.inviteCode) {\r\n        this.$message && this.$message.warning('请输入邀请码');\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      const params = {\r\n        cmsCourseId: this.course.id\r\n      };\r\n      if (this.course.type == 1 || this.course.type == 2) {\r\n        params.inviteCode = this.inviteCode;\r\n      }\r\n      // 支持postRequest/getRequest两种调用\r\n      const req = this.postRequest || this.getRequest;\r\n      if (!req) {\r\n        this.$message && this.$message.error('未注入请求方法');\r\n        this.loading = false;\r\n        return;\r\n      }\r\n      req('/cmsAttend/course', params).then(resp => {\r\n        this.loading = false;\r\n        if (resp && resp.code == 200) {\r\n          this.$message && this.$message.success('报名成功');\r\n          this.$emit('success', this.course);\r\n          this.closeDialog();\r\n        } else {\r\n          this.$message && this.$message.error(resp.message || '报名失败');\r\n        }\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    }\r\n  },\r\n  inject: ['postRequest', 'getRequest', '$message']\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.apply-dialog-mask {\r\n  position: fixed;\r\n  z-index: 9999;\r\n  left: 0; top: 0; right: 0; bottom: 0;\r\n  background: rgba(0,0,0,0.35);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.apply-dialog {\r\n  background: #fff;\r\n  border-radius: 12px;\r\n  padding: 30px 30px 20px 30px;\r\n  min-width: 320px;\r\n  max-width: 90vw;\r\n  box-shadow: 0 8px 32px rgba(0,0,0,0.18);\r\n  position: relative;\r\n}\r\n.apply-dialog-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  margin-bottom: 18px;\r\n}\r\n.apply-dialog-close {\r\n  cursor: pointer;\r\n  font-size: 20px;\r\n  color: #888;\r\n  transition: color 0.2s;\r\n}\r\n.apply-dialog-close:hover {\r\n  color: #dc2430;\r\n}\r\n.apply-dialog-body {\r\n  margin-bottom: 18px;\r\n  font-size: 15px;\r\n}\r\n.invite-input {\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  padding: 6px 12px;\r\n  font-size: 15px;\r\n  margin-left: 8px;\r\n}\r\n.apply-dialog-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n}\r\n.crystal-btn-secondary {\r\n  background: linear-gradient(135deg, #2bff00, #1900ff);\r\n  color: white;\r\n  border: none;\r\n  padding: 10px 20px;\r\n  border-radius: 25px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 4px 15px rgba(180, 180, 180, 0.18);\r\n  margin-left: 10px;\r\n  animation: none;\r\n}\r\n.crystal-btn-secondary:hover {\r\n  background: linear-gradient(135deg, #2bff00, #1900ff);\r\n  color: white;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(180, 180, 180, 0.28);\r\n}\r\n\r\n/* 按钮样式 */\r\n.crystal-btn {\r\n  background: linear-gradient(135deg, #7b4397, #dc2430);\r\n  color: white;\r\n  border: none;\r\n  padding: 10px 20px;\r\n  border-radius: 25px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 4px 15px rgba(123, 67, 151, 0.3);\r\n}\r\n\r\n.crystal-btn:hover {\r\n  background: linear-gradient(135deg, #dc2430, #7b4397);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(123, 67, 151, 0.4);\r\n}\r\n\r\n.crystal-btn i {\r\n  margin-right: 8px;\r\n}\r\n\r\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,QAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;IACA;EACA;EACAC,KAAA;IACAV,QAAAW,GAAA;MACA,IAAAA,GAAA,OAAAH,UAAA;IACA;IACAJ,OAAAO,GAAA;MACA,KAAAH,UAAA;IACA;EACA;EACAI,OAAA;IACAC,YAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA;MACA,UAAAX,MAAA;MACA,SAAAA,MAAA,CAAAH,IAAA,eAAAO,UAAA;QACA,KAAAQ,QAAA,SAAAA,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAR,OAAA;MACA,MAAAS,MAAA;QACAC,WAAA,OAAAf,MAAA,CAAAgB;MACA;MACA,SAAAhB,MAAA,CAAAH,IAAA,cAAAG,MAAA,CAAAH,IAAA;QACAiB,MAAA,CAAAV,UAAA,QAAAA,UAAA;MACA;MACA;MACA,MAAAa,GAAA,QAAAC,WAAA,SAAAC,UAAA;MACA,KAAAF,GAAA;QACA,KAAAL,QAAA,SAAAA,QAAA,CAAAQ,KAAA;QACA,KAAAf,OAAA;QACA;MACA;MACAY,GAAA,sBAAAH,MAAA,EAAAO,IAAA,CAAAC,IAAA;QACA,KAAAjB,OAAA;QACA,IAAAiB,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAX,QAAA,SAAAA,QAAA,CAAAY,OAAA;UACA,KAAAd,KAAA,iBAAAV,MAAA;UACA,KAAAS,WAAA;QACA;UACA,KAAAG,QAAA,SAAAA,QAAA,CAAAQ,KAAA,CAAAE,IAAA,CAAAG,OAAA;QACA;MACA,GAAAC,KAAA;QACA,KAAArB,OAAA;MACA;IACA;EACA;EACAsB,MAAA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}