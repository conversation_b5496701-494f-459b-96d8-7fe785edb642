{"version": 3, "file": "js/918.1c0e2362.js", "mappings": "uJAAA,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,OAAO,CAACE,YAAY,gBAAgB,CAACF,EAAG,UAAUA,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACN,EAAIO,GAAG,YAAY,GAAGH,EAAG,WAAW,EAAE,EAC9NI,EAAkB,GCDlB,EAAS,WAAa,IAAIR,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACN,EAAIS,GAAG,GAAGL,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,SAAS,CAACJ,EAAIU,GAAG,cAAgBV,EAAIW,cAAoMP,EAAG,OAAO,CAACJ,EAAIU,GAAG,6CAA6CN,EAAG,MAAMJ,EAAIU,GAAG,UAApQN,EAAG,OAAO,CAACQ,MAAM,sBAAwB,CAACZ,EAAIU,GAAG,+CAA+CN,EAAG,OAAO,CAACS,YAAY,CAAC,YAAY,OAAO,cAAc,QAAQ,CAACb,EAAIU,GAAG,gBAAyGN,EAAG,MAAM,CAACU,MAAMd,EAAIW,cAAgB,sBAAwB,gBAAgB,CAACP,EAAG,SAAS,CAACE,YAAY,YAAYS,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQhB,EAAIiB,UAAU,CAACjB,EAAIU,GAAGV,EAAIkB,GAAGlB,EAAImB,UAAYnB,EAAIoB,WAAcpB,EAAImB,SAASE,UAAYrB,EAAImB,SAASG,SAAY,SAASlB,EAAG,MAAM,CAACU,MAAM,CAAC,cAAe,CAAC,OAAUd,EAAIuB,mBAAmBP,GAAG,CAAC,MAAQhB,EAAIwB,aAAa,CAACpB,EAAG,QAAQA,EAAG,QAAQA,EAAG,gBAAgBA,EAAG,MAAM,CAACU,MAAM,CAAC,cAAe,iBAAkB,CAAC,gBAAiBd,EAAIuB,oBAAoB,CAACnB,EAAG,MAAM,CAACE,YAAY,OAAO,CAACF,EAAG,KAAK,CAACE,YAAY,YAAYN,EAAIyB,GAAIzB,EAAY,UAAE,SAAS0B,EAAKC,GAAO,OAAOvB,EAAG,KAAK,CAACwB,IAAID,EAAMf,MAAOZ,EAAI6B,mBAAmBF,IAAS,CAACvB,EAAG,cAAc,CAACE,YAAY,SAASS,MAAM,CAAC,GAAKW,EAAKI,OAAO,CAAC9B,EAAIU,GAAGV,EAAIkB,GAAGQ,EAAKK,UAAU,EAAE,IAAG,OAAQ/B,EAAiB,cAAEI,EAAG,MAAM,CAACE,YAAY,sBAAsBQ,MAAM,CAAC,OAAUd,EAAIuB,kBAAkBP,GAAG,CAAC,MAAQhB,EAAIwB,cAAcxB,EAAIgC,MAAM,EACvgD,EAAkB,CAAC,WAAa,IAAIhC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACW,MAAM,CAAC,IAAM,EAAQ,MAA4B,IAAM,WAAW,G,QCD7M,MAAMkB,EACjB,eAAOC,CAASC,EAASC,GACrBC,SAASC,cAAcH,GAASI,UAAUC,IAAIJ,EAClD,CAEA,kBAAOK,CAAYN,EAASC,GACxBC,SAASC,cAAcH,GAASI,UAAUG,OAAON,EACrD,CAEA,kBAAOO,CAAYR,EAASC,GACxBC,SAASC,cAAcH,GAASI,UAAUK,OAAOR,EACrD,CAEA,eAAOS,CAASC,EAAKC,EAAI,GACrB,OAAOA,EAAI,EAAID,EAAIE,QAAO,CAACC,EAAKC,IAAQD,EAAIE,OAAOC,MAAMC,QAAQH,GAAOjD,KAAK4C,SAASK,EAAKH,EAAI,GAAKG,IAAM,IAAMJ,EAAIQ,OACxH,CAEA,cAAOC,CAAQC,GACX,OAAOA,EACFC,WACAC,cACAC,QAAQ,OAAQ,KAChBA,QAAQ,WAAY,IACpBA,QAAQ,OAAQ,KAChBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,GACxB,CAEA,qBAAOC,CAAeC,EAAKC,GACvB,IAAIC,EACJ,IAAKA,EAAI,EAAGA,EAAID,EAAKE,OAAQD,IACzB,GAAID,EAAKC,GAAGE,OAASJ,EAAII,KACrB,OAAOF,EAIf,OAAQ,CACZ,E,cCeJ,GACAhC,KAAA,SACAmC,WAAA,CACA,EAEAC,IAAAA,GACA,OACA/C,YAAA,EACAD,SAAA,GACAR,eAAAA,EAAAA,EAAAA,KACAsB,aAAA,EACAV,kBAAA,EACA6C,SAAA,CACA,CAAArC,KAAA,KAAAD,KAAA,UACA,CAAAC,KAAA,OAAAD,KAAA,UACA,CAAAC,KAAA,OAAAD,KAAA,WACA,CAAAC,KAAA,MAAAD,KAAA,aAEA,CAAAC,KAAA,OAAAD,KAAA,gBACA,CAAAC,KAAA,OAAAD,KAAA,aACA,CAAAC,KAAA,OAAAD,KAAA,UAGA,EACAuC,QAAA,CACAC,gBAAAA,GACA,MAAAC,EAAAC,aAAAC,QAAA,SACA,GAAAF,EACA,IACA,MAAAG,EAAAF,aAAAC,QAAA,YACAC,GACA,KAAAvD,SAAAwD,KAAAC,MAAAF,GACA,KAAAtD,YAAA,GAGA,KAAAyD,aAEA,OAAAC,GACAC,QAAAD,MAAA,WAAAA,GACA,KAAAE,gBACA,CAEA,EACA/D,OAAAA,GACA,KAAAgE,QAAAC,KAAA,CACApD,KAAA,UAEA,EACAqD,SAAAA,GACA,KAAAF,QAAAC,KAAA,CACAnD,KAAA,OAMA,EACAP,UAAAA,GACA,KAAAD,kBAAA,KAAAA,iBAEA,KAAAA,iBACAc,SAAA+C,KAAAxE,MAAAyE,SAAA,SAEAhD,SAAA+C,KAAAxE,MAAAyE,SAAA,EAEA,EACAxD,kBAAAA,CAAAF,GAEA,YAAAhB,cACA,CACA2E,gBAAA,IAAA3D,EAAA,KAGA,EACA,EACA4D,kBAAAA,GACA,IAAA5E,EAAAA,EAAAA,KACA,OAEA,MAAA6E,EAAAnD,SAAAoD,gBAAAC,UACAF,EAAA,IACAvD,EAAAC,SAAA,4BACAsD,GAAA,KACAvD,EAAAQ,YAAA,2BAEA,EACAuC,cAAAA,GAEAW,eAAAC,WAAA,SACAD,eAAAC,WAAA,YACApB,aAAAoB,WAAA,SACApB,aAAAoB,WAAA,YACA,KAAAxE,YAAA,EACA,KAAAD,SAAA,EACA,GAEA0E,OAAAA,GACAC,OAAAC,iBAAA,cAAAR,oBACA,KAAAjB,kBACA,EACA0B,OAAAA,GACA,KAAAT,oBACA,EACAU,aAAAA,GACAH,OAAAI,oBAAA,cAAAX,mBACA,GC7JwQ,I,UCQpQY,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIF,EAAeA,EAAiB,QCnB5B,EAAS,WAAa,IAAInG,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,aAAaO,YAAY,CAAC,mBAAmB,aAAaT,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,KAAK,CAACE,YAAY,qBAAqB,CAACF,EAAG,KAAK,CAACE,YAAY,2BAA2B,CAACF,EAAG,cAAc,CAACE,YAAY,0BAA0BS,MAAM,CAAC,GAAK,WAAW,CAACf,EAAIU,GAAG,eAAe,SAASN,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,SAAS,CAACE,YAAY,6BAA6B,CAACN,EAAIU,GAAG,UAAUN,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,IAAI,CAACE,YAAY,sBAAsB,CAACN,EAAIU,GAAG,IAAIV,EAAIkB,GAAGlB,EAAIoG,aAAa,aAAahG,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,SAAS,CAACE,YAAY,6BAA6B,CAACN,EAAIU,GAAG,UAAUN,EAAG,KAAK,CAACE,YAAY,uBAAuB,CAACF,EAAG,KAAK,CAACE,YAAY,6BAA6B,CAACF,EAAG,IAAI,CAACE,YAAY,kBAAkBF,EAAG,OAAO,CAACJ,EAAIU,GAAG,QAAQV,EAAIkB,GAAGlB,EAAIqG,kBAAkBjG,EAAG,KAAK,CAACE,YAAY,6BAA6B,CAACF,EAAG,IAAI,CAACE,YAAY,uBAAuBF,EAAG,OAAO,CAACJ,EAAIU,GAAGV,EAAIkB,GAAGlB,EAAIsG,mBAAmBlG,EAAG,KAAK,CAACE,YAAY,6BAA6B,CAACF,EAAG,IAAI,CAACE,YAAY,oBAAoBF,EAAG,OAAO,CAACJ,EAAIU,GAAGV,EAAIkB,GAAGlB,EAAIuG,+BAA+B,EACz8C,EAAkB,GCsCtB,GACAxE,KAAA,SACAoC,IAAAA,GACA,OACAmC,aAAA,GACAE,WAAA,GACAH,YAAA,GACAD,YAAA,GACAG,cAAA,GAEA,EACAP,OAAAA,GACA,KAAAS,iBACA,EACApC,QAAA,CACAoC,eAAAA,GACA,KAAAC,WAAA,sBAAAC,MAAAC,IACAA,GAAA,KAAAA,EAAAC,OACA,KAAAP,aAAAM,EAAAzC,KAAAmC,aACA,KAAAE,WAAAI,EAAAzC,KAAAqC,WACA,KAAAH,YAAAO,EAAAzC,KAAAkC,YACA,KAAAD,YAAAQ,EAAAzC,KAAAiC,YACA,KAAAG,cAAAK,EAAAzC,KAAAoC,cACA,GAEA,IChEwQ,ICOpQ,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIF,EAAe,EAAiB,QCHhC,GACAxE,KAAA,SACAmC,WAAA,CAAA4C,OAAA,EAAAC,OAAAA,ICjB+P,ICQ3P,GAAY,OACd,EACAhH,EACAS,GACA,EACA,KACA,WACA,MAIF,EAAe,EAAiB,O,oECnBhC,IAAIT,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,SAAS,CAACA,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,aAAaS,MAAM,CAAC,IAAM,EAAQ,MAA4B,IAAM,gBAAgBX,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,KAAK,CAACJ,EAAIU,GAAG,UAAUN,EAAG,KAAK,CAACJ,EAAIU,GAAG,cAAcN,EAAG,IAAI,CAACJ,EAAIU,GAAG,8BAA8BN,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcF,EAAG,MAAM,CAACE,YAAY,cAAcF,EAAG,MAAM,CAACE,YAAY,kBAAkBF,EAAG,MAAM,CAACE,YAAY,eAAe,CAAGN,EAAIoB,WAG8ehB,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,cAAcS,MAAM,CAAC,IAAMf,EAAImB,SAAS6F,QAAU,EAAQ,MAAoC,IAAM,QAAQ5G,EAAG,KAAK,CAACE,YAAY,aAAa,CAACN,EAAIU,GAAGV,EAAIkB,GAAGlB,EAAImB,SAASE,UAAYrB,EAAImB,SAASG,UAAYtB,EAAImB,SAAS8F,aAAa7G,EAAG,IAAI,CAACE,YAAY,aAAa,CAACN,EAAIU,GAAGV,EAAIkB,GAAGlB,EAAImB,SAAS+F,OAAS,cAAc9G,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,aAAa,CAACE,YAAY,aAAaS,MAAM,CAAC,MAAQ,GAAG,MAAQ,GAAG,MAAQ,IAAIC,GAAG,CAAC,MAAQhB,EAAImH,SAAS,CAACnH,EAAIU,GAAG,aAAa,KAHjjCN,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,KAAK,CAACE,YAAY,eAAe,CAACN,EAAIU,GAAG,UAAUN,EAAG,IAAI,CAACE,YAAY,kBAAkB,CAACN,EAAIU,GAAG,kBAAkBN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACU,MAAM,CAAC,WAAY,CAAEsG,OAA0B,SAAlBpH,EAAIqH,YAAwBrG,GAAG,CAAC,MAAQ,SAASsG,GAAQ,OAAOtH,EAAIuH,gBAAgB,OAAO,IAAI,CAACvH,EAAIU,GAAG,aAAaN,EAAG,MAAM,CAACU,MAAM,CAAC,WAAY,CAAEsG,OAA0B,aAAlBpH,EAAIqH,YAA4BrG,GAAG,CAAC,MAAQ,SAASsG,GAAQ,OAAOtH,EAAIuH,gBAAgB,WAAW,IAAI,CAACvH,EAAIU,GAAG,gBAAmC,aAAlBV,EAAIqH,UAA0BjH,EAAG,WAAW,CAACY,GAAG,CAAC,OAAShB,EAAIwH,WAAW,CAACpH,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACW,MAAM,CAAC,IAAM,aAAa,CAACf,EAAIU,GAAG,aAAaN,EAAG,YAAY,CAACE,YAAY,cAAcS,MAAM,CAAC,KAAO,WAAW,YAAc,aAAa,MAAQ,CAAC,CAAE0G,UAAU,EAAMC,QAAS,gBAAiBC,MAAM,CAACC,MAAO5H,EAAI6H,UAAkB,SAAEC,SAAS,SAAUC,GAAM/H,EAAIgI,KAAKhI,EAAI6H,UAAW,WAAYE,EAAI,EAAEE,WAAW,yBAAyB,GAAG7H,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACW,MAAM,CAAC,IAAM,aAAa,CAACf,EAAIU,GAAG,QAAQN,EAAG,YAAY,CAACE,YAAY,cAAcS,MAAM,CAAC,KAAO,WAAW,KAAO,WAAW,YAAc,QAAQ,MAAQ,CAAC,CAAE0G,UAAU,EAAMC,QAAS,WAAYC,MAAM,CAACC,MAAO5H,EAAI6H,UAAkB,SAAEC,SAAS,SAAUC,GAAM/H,EAAIgI,KAAKhI,EAAI6H,UAAW,WAAYE,EAAI,EAAEE,WAAW,yBAAyB,GAAG7H,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,eAAe,CAACuH,MAAM,CAACC,MAAO5H,EAAc,WAAE8H,SAAS,SAAUC,GAAM/H,EAAIkI,WAAWH,CAAG,EAAEE,WAAW,eAAe,CAACjI,EAAIU,GAAG,UAAU,KAAKN,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,aAAa,CAACW,MAAM,CAAC,MAAQ,GAAG,MAAQ,GAAG,KAAO,UAAU,cAAc,SAAS,QAAUf,EAAImI,UAAU,CAACnI,EAAIU,GAAG,WAAW,KAAKN,EAAG,WAAW,CAACY,GAAG,CAAC,OAAShB,EAAIoI,eAAe,CAAChI,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACW,MAAM,CAAC,IAAM,UAAU,CAACf,EAAIU,GAAG,SAASN,EAAG,YAAY,CAACE,YAAY,cAAcS,MAAM,CAAC,KAAO,QAAQ,YAAc,SAAS,MAAQ,CACznF,CAAE0G,UAAU,EAAMC,QAAS,UAC3B,CAAEW,QAAS,gBAAiBX,QAAS,gBACtCC,MAAM,CAACC,MAAO5H,EAAIsI,SAAc,MAAER,SAAS,SAAUC,GAAM/H,EAAIgI,KAAKhI,EAAIsI,SAAU,QAASP,EAAI,EAAEE,WAAW,qBAAqB,GAAG7H,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,QAAQ,CAACW,MAAM,CAAC,IAAM,SAAS,CAACf,EAAIU,GAAG,SAASN,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,YAAY,CAACE,YAAY,yBAAyBS,MAAM,CAAC,KAAO,OAAO,YAAc,SAAS,MAAQ,CAAC,CAAE0G,UAAU,EAAMC,QAAS,YAAaC,MAAM,CAACC,MAAO5H,EAAIsI,SAAa,KAAER,SAAS,SAAUC,GAAM/H,EAAIgI,KAAKhI,EAAIsI,SAAU,OAAQP,EAAI,EAAEE,WAAW,mBAAmB7H,EAAG,aAAa,CAACE,YAAY,cAAcS,MAAM,CAAC,KAAO,QAAQ,KAAO,UAAU,SAAWf,EAAIuI,aAAevI,EAAIwI,SAAW,GAAGxH,GAAG,CAAC,MAAQ,SAASsG,GAAgC,OAAxBA,EAAOmB,iBAAwBzI,EAAI0I,oBAAoBC,MAAM,KAAMC,UAAU,IAAI,CAAC5I,EAAIU,GAAG,IAAIV,EAAIkB,GAAGlB,EAAIwI,SAAW,EAAKxI,EAAIwI,SAAW,SAAY,SAAS,QAAQ,KAAKpI,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,aAAa,CAACW,MAAM,CAAC,MAAQ,GAAG,MAAQ,GAAG,KAAO,UAAU,cAAc,SAAS,QAAUf,EAAImI,UAAU,CAACnI,EAAIU,GAAG,WAAW,KAAKN,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACJ,EAAIU,GAAG,YAAYN,EAAG,cAAc,CAACW,MAAM,CAAC,GAAK,cAAc,CAACf,EAAIU,GAAG,WAAW,IAAI,UAA8lB,EACzwDF,EAAkB,G,uCCsJtB,GACAuB,KAAA,YACAoC,IAAAA,GACA,OACA0E,SAAA,GACAlI,eAAAA,EAAAA,EAAAA,KACAwH,SAAA,EACA/G,YAAA,EACAD,SAAA,GACA+G,YAAA,EACAb,UAAA,OACAQ,UAAA,CACAZ,SAAA,GACA6B,SAAA,IAEAR,SAAA,CACApB,MAAA,GACAL,KAAA,IAEA0B,aAAA,EACAC,SAAA,EACAO,MAAA,KAEA,EACA7E,WAAA,CACA8E,OAAAA,EAAAA,GAEAhD,OAAAA,GACA,KAAA6C,SAAAI,mBAAA,KAAAC,OAAAC,MAAAN,UAAA,IACA,KAAAO,WACA,KAAA9E,kBACA,EACA2B,aAAAA,GACA,KAAA8C,OACAM,cAAA,KAAAN,MAEA,EACA1E,QAAA,CACAkD,eAAAA,CAAA+B,GACA,KAAAjC,UAAAiC,CACA,EACAhF,gBAAAA,GACA,MAAAC,EAAAC,aAAAC,QAAA,SACA,GAAAF,EACA,IACA,MAAAG,EAAAF,aAAAC,QAAA,YACAC,GACA,KAAAvD,SAAAwD,KAAAC,MAAAF,GACA,KAAAtD,YAAA,GAGA,KAAAyD,aAEA,OAAAC,GACAC,QAAAD,MAAA,WAAAA,GACA,KAAAE,gBACA,CAEA,EACAH,WAAAA,GACA,KAAA6B,WAAA,SAAAC,MAAAC,IACAA,GAAA,KAAAA,EAAAC,MACA,KAAA1F,SAAAyF,EAAAzC,KACA,KAAA/C,YAAA,EAEAuE,eAAA4D,QAAA,WAAA5E,KAAA6E,UAAA5C,EAAAzC,OACAK,aAAA+E,QAAA,WAAA5E,KAAA6E,UAAA5C,EAAAzC,QAEA,KAAAa,gBACA,GAEA,EACAwC,QAAAA,GACA,KAAAW,SAAA,EAEA,KAAAsB,YAAA,aACAC,QAAA,KAAA7B,UAAAZ,SACA6B,SAAA,KAAAjB,UAAAiB,WAEAnC,MAAAC,IACA,KAAAuB,SAAA,EACAvB,GAAA,KAAAA,EAAAC,MAIArC,aAAA+E,QAAA,QAAA3C,EAAAzC,KAAAI,OAEAC,aAAA+E,QAAA,WAAA5E,KAAA6E,UAAA5C,EAAAzC,KAAAhD,WAEA,KAAAA,SAAAyF,EAAAzC,KAAAhD,SACA,KAAAC,YAAA,EAEAuI,EAAAA,EAAAC,QAAA,QAGA,KAAAf,SACAgB,SAAAC,KAAA,KAAAjB,SAGA,KAAA5D,QAAAC,KAAA,MAGAyE,EAAAA,EAAA7E,MAAA8B,EAAAc,SAAA,iBACA,GAEA,EACAU,YAAAA,GACA,KAAAE,SAAApB,OAAA,KAAAoB,SAAAzB,MAKA,KAAAsB,SAAA,EAEA,KAAAsB,YAAA,iBACAvC,MAAA,KAAAoB,SAAApB,MACA6C,QAAA,KAAAzB,SAAAzB,OAEAF,MAAAC,IACA,KAAAuB,SAAA,EACAvB,GAAA,KAAAA,EAAAC,MAEArC,aAAA+E,QAAA,QAAA3C,EAAAzC,KAAAI,OAEAC,aAAA+E,QAAA,WAAA5E,KAAA6E,UAAA5C,EAAAzC,KAAAhD,WAEA,KAAAA,SAAAyF,EAAAzC,KAAAhD,SACA,KAAAC,YAAA,EAEAuI,EAAAA,EAAAC,QAAA,QAGA,KAAAf,SACAgB,SAAAC,KAAA,KAAAjB,SAGA,KAAA5D,QAAAC,KAAA,MAGAyE,EAAAA,EAAA7E,MAAA8B,EAAAc,SAAA,kBACA,KAhCAiC,EAAAA,EAAA7E,MAAA,aAkCA,EACA4D,mBAAAA,GACA,QAAAH,aAAA,KAAAC,SAAA,SAEA,MAAAtB,EAAA,KAAAoB,SAAApB,MACAA,EAKA,gBAAA8C,KAAA9C,IAKA,KAAAqB,aAAA,EAGA,KAAA0B,kBAAA,aACA/C,MAAAA,IAEAP,MAAAC,IACA,KAAA2B,aAAA,EACA3B,GAAA,KAAAA,EAAAC,MACA8C,EAAAA,EAAAC,QAAA,gBAEA,KAAApB,SAAA,GACA,KAAAO,MAAAmB,aAAA,KACA,KAAA1B,WACA,KAAAA,UAAA,GACAa,cAAA,KAAAN,MACA,GACA,MAEAY,EAAAA,EAAA7E,MAAA8B,EAAAc,SAAA,gBACA,IAEAyC,OAAA,KACA,KAAA5B,aAAA,EACAoB,EAAAA,EAAA7E,MAAA,qBA5BA6E,EAAAA,EAAA7E,MAAA,cALA6E,EAAAA,EAAA7E,MAAA,SAmCA,EACAsF,cAAAA,GACA,KAAAnF,QAAAC,KAAA,QACA,EACAiC,MAAAA,GAEA,KAAAsC,YAAA,WAAAY,SAAA,KACA,KAAArF,iBACA2E,EAAAA,EAAAC,QAAA,SAEA,KAAAxI,YAAA,EACA,KAAAD,SAAA,GACA0I,SAAAS,QAAA,GAEA,EACAtF,cAAAA,GAEAW,eAAAC,WAAA,SACAD,eAAAC,WAAA,YACApB,aAAAoB,WAAA,SACApB,aAAAoB,WAAA,YACA,KAAAxE,YAAA,EACA,KAAAD,SAAA,EACA,ICxWqP,I,UCQjPgF,GAAY,OACd,EACApG,EACAS,GACA,EACA,KACA,WACA,MAIF,EAAe2F,EAAiB,O,uBCdzB,SAASxF,IAEZ,IAAImJ,EAAOhE,OAAO+D,SAASC,KAC3B,GAAGA,EAAKS,SAAS,WACf,OAAO,EAET,GAAGT,EAAKS,SAAS,YACf,OAAO,EAGT,IAAIC,EAAK1E,OAAO2E,UAAUC,UAAUhH,cAMpC,MAAO,iKAAiKsG,KAAKQ,EAEjL,CAMO,SAASG,EAASC,GACrB,MAAO,eAAeZ,KAAKY,EAC/B,C", "sources": ["webpack://city-font-a0/./src/components/common/Layout.vue?ce4e", "webpack://city-font-a0/./src/components/common/header/Header.vue?186c", "webpack://city-font-a0/./src/utils/AppFunctions.js", "webpack://city-font-a0/src/components/common/header/Header.vue", "webpack://city-font-a0/./src/components/common/header/Header.vue?38db", "webpack://city-font-a0/./src/components/common/header/Header.vue", "webpack://city-font-a0/./src/components/common/footer/Footer.vue?b880", "webpack://city-font-a0/src/components/common/footer/Footer.vue", "webpack://city-font-a0/./src/components/common/footer/Footer.vue?6062", "webpack://city-font-a0/./src/components/common/footer/Footer.vue", "webpack://city-font-a0/src/components/common/Layout.vue", "webpack://city-font-a0/./src/components/common/Layout.vue?a648", "webpack://city-font-a0/./src/components/common/Layout.vue", "webpack://city-font-a0/./src/views/login.vue?e65c", "webpack://city-font-a0/src/views/login.vue", "webpack://city-font-a0/./src/views/login.vue?568d", "webpack://city-font-a0/./src/views/login.vue", "webpack://city-font-a0/./src/utils/index.js"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('main',{staticClass:\"page-wrapper\"},[_c('Header'),_c('div',{staticClass:\"main-content\"},[_vm._t(\"default\")],2),_c('Footer')],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"header-wrapper\"},[_c('div',{staticClass:\"header\"},[_c('div',{staticClass:\"header-content\"},[_vm._m(0),_c('div',{staticClass:\"header-mid\"},[_c('div',{staticClass:\"header-item\"},[_c('strong',[_vm._v(\"国际水晶疗愈协会\")]),(!_vm.isMobilePhone)?_c('span',{style:('margin-left: 20px;')},[_vm._v(\"International Crystal Healing Association, \"),_c('span',{staticStyle:{\"font-size\":\"12px\",\"font-weight\":\"600\"}},[_vm._v(\"ICHA\")])]):_c('span',[_vm._v(\"International Crystal Healing Association\"),_c('br'),_vm._v(\"ICHA\")])])]),_c('div',{class:_vm.isMobilePhone ? 'header-right-mobile' : 'header-right'},[_c('button',{staticClass:\"login-btn\",attrs:{\"type\":\"button\"},on:{\"click\":_vm.goLogin}},[_vm._v(_vm._s(_vm.userInfo && _vm.isLoggedIn ? (_vm.userInfo.nickname || _vm.userInfo.nickName) : '登录'))]),_c('div',{class:['menu-toggle', {'active': _vm.mobileMenuActive}],on:{\"click\":_vm.toggleMenu}},[_c('span'),_c('span'),_c('span')])])])]),_c('div',{class:['nav-wrapper', 'header-default', {'mobile-active': _vm.mobileMenuActive}]},[_c('div',{staticClass:\"nav\"},[_c('ul',{staticClass:\"nav-list\"},_vm._l((_vm.navItems),function(item,index){return _c('li',{key:index,style:(_vm.getMobileItemStyle(index))},[_c('router-link',{staticClass:\"router\",attrs:{\"to\":item.path}},[_vm._v(_vm._s(item.name))])],1)}),0)])]),(_vm.isMobilePhone)?_c('div',{staticClass:\"mobile-menu-overlay\",class:{'active': _vm.mobileMenuActive},on:{\"click\":_vm.toggleMenu}}):_vm._e()])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"header-left\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/logo.png\"),\"alt\":\"Logo\"}})])}]\n\nexport { render, staticRenderFns }", "export default class AppFunctions {\r\n    static addClass(element, className) {\r\n        document.querySelector(element).classList.add(className)\r\n    }\r\n\r\n    static removeClass(element, className) {\r\n        document.querySelector(element).classList.remove(className)\r\n    }\r\n\r\n    static toggleClass(element, className) {\r\n        document.querySelector(element).classList.toggle(className)\r\n    }\r\n\r\n    static flatDeep(arr, d = 1) {\r\n        return d > 0 ? arr.reduce((acc, val) => acc.concat(Array.isArray(val) ? this.flatDeep(val, d - 1) : val), []) : arr.slice();\r\n    }\r\n\r\n    static slugify(text) {\r\n        return text\r\n            .toString()\r\n            .toLowerCase()\r\n            .replace(/\\s+/g, '-') // Replace spaces with -\r\n            .replace(/[^\\w-]+/g, '') // Remove all non-word chars\r\n            .replace(/--+/g, '-') // Replace multiple - with single -\r\n            .replace(/^-+/, '') // Trim - from start of text\r\n            .replace(/-+$/, '') // Trim - from end of text\r\n    }\r\n\r\n    static containsObject(obj, list) {\r\n        let i;\r\n        for (i = 0; i < list.length; i++) {\r\n            if (list[i].slug === obj.slug) {\r\n                return i;\r\n            }\r\n        }\r\n\r\n        return -1;\r\n    }\r\n}", "<template>\r\n\t<div class=\"header-wrapper\">\r\n\t\t<!-- 顶部栏 -->\r\n\t\t<div class=\"header\">\r\n\t\t\t<div class=\"header-content\">\r\n\t\t\t\t<div class=\"header-left\">\r\n\t\t\t\t\t<img src=\"@/assets/images/logo.png\" alt=\"Logo\" />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"header-mid\">\r\n\t\t\t\t\t<div class=\"header-item\">\r\n\t\t\t\t\t\t<strong>国际水晶疗愈协会</strong>\r\n\t\t\t\t\t\t<span v-if=\"!isMobilePhone\" :style=\"'margin-left: 20px;'\">International Crystal Healing Association, <span style=\"font-size: 12px;font-weight: 600;\">ICHA</span></span>\r\n\t\t\t\t\t\t<span v-else>International Crystal Healing Association<br />ICHA</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div :class=\"isMobilePhone ? 'header-right-mobile' : 'header-right'\">\r\n\t\t\t\t\t<button type=\"button\" class=\"login-btn\" @click=\"goLogin\">{{userInfo && isLoggedIn ? (userInfo.nickname || userInfo.nickName) : '登录'}}</button>\r\n\t\t\t\t\t<!-- 移动端汉堡菜单按钮 -->\r\n\t\t\t\t\t<div :class=\"['menu-toggle', {'active': mobileMenuActive}]\" @click=\"toggleMenu\">\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 导航菜单 -->\r\n\t\t<div :class=\"['nav-wrapper', 'header-default', {'mobile-active': mobileMenuActive}]\">\r\n\t\t\t<div class=\"nav\">\r\n\t\t\t\t<ul class=\"nav-list\">\r\n\t\t\t\t\t<li v-for=\"(item, index) in navItems\" :key=\"index\" :style=\"getMobileItemStyle(index)\">\r\n\t\t\t\t\t\t<router-link class=\"router\" :to=\"item.path\">{{ item.name }}</router-link>\r\n\t\t\t\t\t</li>\r\n\t\t\t\t</ul>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 移动菜单背景遮罩 -->\r\n\t\t<div \r\n\t\t\tclass=\"mobile-menu-overlay\" \r\n\t\t\tv-if=\"isMobilePhone\" \r\n\t\t\t:class=\"{'active': mobileMenuActive}\"\r\n\t\t\t@click=\"toggleMenu\"\r\n\t\t></div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n// import Try from \"./components/try\";\r\nimport AppFunctions from \"@/utils/AppFunctions\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nexport default {\r\n\tname: \"Header\",\r\n\tcomponents: {\r\n\t\t// Try,\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisLoggedIn: false,\r\n\t\t\tuserInfo: {},\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tAppFunctions,\r\n\t\t\tmobileMenuActive: false,\r\n\t\t\tnavItems: [\r\n\t\t\t\t{ name: '主页', path: '/index' },\r\n\t\t\t\t{ name: '关于我们', path: '/about' },\r\n\t\t\t\t{ name: '认证课程', path: '/course' },\r\n\t\t\t\t{ name: '工作坊', path: '/workshop' },\r\n\t\t\t\t// { name: '脉轮测试', path: '/chakra-test' },\r\n\t\t\t\t{ name: '证书查询', path: '/certificate' },\r\n\t\t\t\t{ name: '资料下载', path: '/download' },\r\n\t\t\t\t{ name: '加入我们', path: '/join' },\r\n\t\t\t]\r\n\t\t};\r\n\t},\r\n\tmethods: {\r\n\t\tcheckLoginStatus() {\r\n\t\t\tconst token = localStorage.getItem(\"token\");\r\n\t\t\tif (token) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst userInfoStr = localStorage.getItem(\"userInfo\") ;\r\n\t\t\t\t\tif (userInfoStr) {\r\n\t\t\t\t\t\tthis.userInfo = JSON.parse(userInfoStr);\r\n\t\t\t\t\t\tthis.isLoggedIn = true;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 有token但没有用户信息，尝试获取用户信息\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"解析用户信息失败\", error);\r\n\t\t\t\t\tthis.clearLoginInfo();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tgoLogin() {\r\n\t\t\tthis.$router.push({\r\n\t\t\t\tpath: '/login'\r\n\t\t\t})\r\n\t\t},\r\n\t\ttryHandle() {\r\n\t\t\tthis.$router.push({\r\n\t\t\t\tname: 'try'\r\n\t\t\t})\r\n\t\t// \tthis.tryVisible = true;\r\n\t\t// \tthis.$nextTick(() => {\r\n\t\t// \t\tthis.$refs.try.init();\r\n\t\t// \t});\r\n\t\t},\r\n\t\ttoggleMenu() {\r\n\t\t\tthis.mobileMenuActive = !this.mobileMenuActive;\r\n\t\t\t// 控制body滚动\r\n\t\t\tif (this.mobileMenuActive) {\r\n\t\t\t\tdocument.body.style.overflow = 'hidden';\r\n\t\t\t} else {\r\n\t\t\t\tdocument.body.style.overflow = '';\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetMobileItemStyle(index) {\r\n\t\t\t// 仅在移动端添加动画延迟\r\n\t\t\tif (this.isMobilePhone) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttransitionDelay: `${index * 0.05}s`\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\treturn {};\r\n\t\t},\r\n\t\ttoggleStickyHeader() {\r\n\t\t\tif(isMobilePhone()){\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tconst scrolled = document.documentElement.scrollTop;\r\n\t\t\tif (scrolled > 100) {\r\n\t\t\t\tAppFunctions.addClass(\".header-default\", \"sticky\");\r\n\t\t\t} else if (scrolled <= 100) {\r\n\t\t\t\tAppFunctions.removeClass(\".header-default\", \"sticky\");\r\n\t\t\t}\r\n\t\t},\r\n\t\tclearLoginInfo() {\r\n\t\t\t// 清除所有存储的登录信息\r\n\t\t\tsessionStorage.removeItem(\"token\");\r\n\t\t\tsessionStorage.removeItem(\"userInfo\");\r\n\t\t\tlocalStorage.removeItem(\"token\");\r\n\t\t\tlocalStorage.removeItem(\"userInfo\");\r\n\t\t\tthis.isLoggedIn = false;\r\n\t\t\tthis.userInfo = {};\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\twindow.addEventListener(\"scroll\", this.toggleStickyHeader);\r\n\t\tthis.checkLoginStatus();\r\n\t},\r\n\tmounted() {\r\n\t\tthis.toggleStickyHeader();\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\twindow.removeEventListener(\"scroll\", this.toggleStickyHeader);\r\n\t},\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 基础样式 */\r\n.header-wrapper {\r\n\twidth: 100%;\r\n\tposition: relative;\r\n\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.header {\r\n\tpadding: 15px 20px;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.header-content {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmax-width: 1200px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n/* Logo区域 */\r\n.header-left img {\r\n\theight: 60px;\r\n\twidth: auto;\r\n}\r\n\r\n/* 中间标题区域 */\r\n.header-mid {\r\n\tflex: 1;\r\n\tmargin: 0 20px;\r\n}\r\n\r\n.header-mid .item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.header-mid strong {\r\n\tfont-size: 22px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 2px;\r\n}\r\n\r\n.header-mid span {\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 右侧登录按钮 */\r\n.header-right {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-left:100px;\r\n}\r\n.header-right-mobile {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.login-btn {\r\n\tbackground-color: #516790;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tpadding: 8px 22px;\r\n\tborder-radius: 4px;\r\n\tfont-size: 14px;\r\n\tcursor: pointer;\r\n\ttransition: background-color 0.3s;\r\n}\r\n\r\n.login-btn:hover {\r\n\tbackground-color: #3f5273;\r\n}\r\n\r\n/* 导航菜单 */\r\n.nav-wrapper {\r\n\tbackground-color: #f8f8f8;\r\n\tborder-top: 1px solid #eee;\r\n\ttransition: all 0.3s ease;\r\n\twidth: 100%;\r\n}\r\n\r\n.nav {\r\n\tmax-width: 1200px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n.nav-list {\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n\tlist-style: none;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n}\r\n\r\n.nav-list li {\r\n\tposition: relative;\r\n}\r\n\r\n.router {\r\n\tdisplay: block;\r\n\tpadding: 15px 10px;\r\n\tcolor: #333;\r\n\ttext-decoration: none;\r\n\tfont-size: 16px;\r\n\ttransition: color 0.3s;\r\n\ttext-align: center;\r\n}\r\n\r\n.router:hover {\r\n\tcolor: #516790;\r\n}\r\n\r\n.router.router-link-active {\r\n\tcursor: default;\r\n\tfont-weight: 600;\r\n\tcolor: #516790;\r\n}\r\n\r\n/* 粘性导航 */\r\n.sticky {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tz-index: 900;\r\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n\tanimation: slideDown 0.3s ease;\r\n}\r\n\r\n@keyframes slideDown {\r\n\tfrom {\r\n\t\ttransform: translateY(-100%);\r\n\t}\r\n\tto {\r\n\t\ttransform: translateY(0);\r\n\t}\r\n}\r\n\r\n/* 汉堡菜单 */\r\n.menu-toggle {\r\n\tdisplay: none;\r\n\tflex-direction: column;\r\n\tjustify-content: space-between;\r\n\twidth: 30px;\r\n\theight: 20px;\r\n\tcursor: pointer;\r\n\tmargin-left: 20px;\r\n\tposition: relative;\r\n\tz-index: 1001;\r\n}\r\n\r\n.menu-toggle span {\r\n\tdisplay: block;\r\n\theight: 2px;\r\n\twidth: 90%;\r\n\tbackground-color: #333;\r\n\tborder-radius: 3px;\r\n\ttransition: transform 0.3s ease, opacity 0.2s ease;\r\n\ttransform-origin: center;\r\n}\r\n\r\n/* 汉堡菜单变X动画 */\r\n.menu-toggle.active span:nth-child(1) {\r\n\ttransform: translateY(9px) rotate(45deg);\r\n}\r\n\r\n.menu-toggle.active span:nth-child(2) {\r\n\topacity: 0;\r\n}\r\n\r\n.menu-toggle.active span:nth-child(3) {\r\n\ttransform: translateY(-9px) rotate(-45deg);\r\n}\r\n\r\n/* 移动菜单背景遮罩 */\r\n.mobile-menu-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\tz-index: 999;\r\n\tvisibility: hidden;\r\n\topacity: 0;\r\n\ttransition: opacity 0.3s ease, visibility 0.3s ease;\r\n}\r\n\r\n.mobile-menu-overlay.active {\r\n\tvisibility: visible;\r\n\topacity: 1;\r\n}\r\n\r\n/* 媒体查询 - 移动端适配 */\r\n@media (max-width: 768px) {\r\n\t/* 移动端基础样式 */\r\n\t.header-wrapper {\r\n\t\toverflow-x: hidden;\r\n\t}\r\n\r\n\t.header-mid strong {\r\n\t\tfont-size: 18px;\r\n\t}\r\n\t\r\n\t.header-mid span {\r\n\t\tfont-size: 12px;\r\n\t}\r\n\t\r\n\t.menu-toggle {\r\n\t\tdisplay: flex;\r\n\t}\r\n\t\r\n\t.login-btn {\r\n\t\tpadding: 6px 15px;\r\n\t\tfont-size: 13px;\r\n\t}\r\n\t\r\n\t/* 移动导航样式增强 */\r\n\t.nav-wrapper {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tright: -280px;\r\n\t\twidth: 280px;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tz-index: 1000;\r\n\t\ttransition: transform 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);\r\n\t\tbox-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);\r\n\t\toverflow-y: auto;\r\n\t\tpadding-top: 60px;\r\n\t}\r\n\t\r\n\t.nav-wrapper.sticky {\r\n\t\tright: -280px;\r\n\t\tposition: fixed;\r\n\t\tanimation: none;\r\n\t}\r\n\t\r\n\t.nav-wrapper.mobile-active {\r\n\t\ttransform: translateX(-280px);\r\n\t}\r\n\t\r\n\t.nav-wrapper.sticky.mobile-active {\r\n\t\ttransform: translateX(-280px);\r\n\t}\r\n\t\r\n\t.nav {\r\n\t\tmax-width: 100%;\r\n\t}\r\n\t\r\n\t.nav-list {\r\n\t\tflex-direction: column;\r\n\t\theight: auto;\r\n\t\tpadding: 20px 0;\r\n\t}\r\n\t\r\n\t.nav-list li {\r\n\t\topacity: 0;\r\n\t\ttransform: translateX(20px);\r\n\t\ttransition: opacity 0.4s ease, transform 0.4s ease;\r\n\t}\r\n\t\r\n\t.mobile-active .nav-list li {\r\n\t\topacity: 1;\r\n\t\ttransform: translateX(0);\r\n\t}\r\n\t\r\n\t.router {\r\n\t\tpadding: 15px 25px;\r\n\t\ttext-align: left;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t}\r\n\t\r\n\t.router:active {\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n}\r\n\r\n/* 小屏幕手机适配 */\r\n@media (max-width: 480px) {\r\n\t.header-left img {\r\n\t\theight: 40px;\r\n\t}\r\n\t\r\n\t.header-mid {\r\n\t\tmargin: 0 10px;\r\n\t}\r\n\t\r\n\t.header-mid strong {\r\n\t\tfont-size: 16px;\r\n\t}\r\n\t\r\n\t.header-mid span {\r\n\t\tfont-size: 10px;\r\n\t}\r\n\t\r\n\t.header {\r\n\t\tpadding: 10px 15px;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Header.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Header.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Header.vue?vue&type=template&id=7ec16e68&scoped=true&\"\nimport script from \"./Header.vue?vue&type=script&lang=js&\"\nexport * from \"./Header.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Header.vue?vue&type=style&index=0&id=7ec16e68&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7ec16e68\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"footer\"},[_c('div',{staticClass:\"footer--bg\",staticStyle:{\"background-color\":\"#383d61\"}}),_c('div',{staticClass:\"footer--inner\"},[_c('div',{staticClass:\"am-g\"},[_c('div',{staticClass:\"am-u-md-3\"},[_c('div',{staticClass:\"footer_main--column\"},[_c('ul',{staticClass:\"footer_navigation\"},[_c('li',{staticClass:\"footer_navigation--item\"},[_c('router-link',{staticClass:\"footer_navigation--link\",attrs:{\"to\":\"/about\"}},[_vm._v(\"国际水晶疗愈协会\")])],1)])])]),_c('div',{staticClass:\"am-u-md-6\"},[_c('div',{staticClass:\"footer_main--column\"},[_c('strong',{staticClass:\"footer_main--column_title\"},[_vm._v(\"关于我们\")]),_c('div',{staticClass:\"footer_about\"},[_c('p',{staticClass:\"footer_about--text\"},[_vm._v(\" \"+_vm._s(_vm.footerabout)+\" \")])])])]),_c('div',{staticClass:\"am-u-md-3\"},[_c('div',{staticClass:\"footer_main--column\"},[_c('strong',{staticClass:\"footer_main--column_title\"},[_vm._v(\"联系详情\")]),_c('ul',{staticClass:\"footer_contact_info\"},[_c('li',{staticClass:\"footer_contact_info--item\"},[_c('i',{staticClass:\"am-icon-phone\"}),_c('span',[_vm._v(\"服务专线：\"+_vm._s(_vm.aboutmobile))])]),_c('li',{staticClass:\"footer_contact_info--item\"},[_c('i',{staticClass:\"am-icon-map-marker\"}),_c('span',[_vm._v(_vm._s(_vm.aboutaddress))])]),_c('li',{staticClass:\"footer_contact_info--item\"},[_c('i',{staticClass:\"am-icon-clock-o\"}),_c('span',[_vm._v(_vm._s(_vm.aboutkefutime))])])])])])])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<div class=\"footer\">\r\n\t\t<div style=\"background-color:#383d61\" class=\"footer--bg\"></div>\r\n\t\t<div class=\"footer--inner\">\r\n\t\t\t<div class=\"am-g\">\r\n\t\t\t\t<div class=\"am-u-md-3 \">\r\n\t\t\t\t\t<div class=\"footer_main--column\">\r\n\t\t\t\t\t\t<!-- <strong class=\"footer_main--column_title\">产品中心</strong> -->\r\n\t\t\t\t\t\t<ul class=\"footer_navigation\">\r\n\t\t\t\t\t\t\t<li class=\"footer_navigation--item\"><router-link to=\"/about\" class=\"footer_navigation--link\">国际水晶疗愈协会</router-link></li>\r\n\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"am-u-md-6 \">\r\n\t\t\t\t\t<div class=\"footer_main--column\">\r\n\t\t\t\t\t\t<strong class=\"footer_main--column_title\">关于我们</strong>\r\n\t\t\t\t\t\t<div class=\"footer_about\">\r\n\t\t\t\t\t\t\t<p class=\"footer_about--text\">\r\n\t\t\t\t\t\t\t\t{{ footerabout }}\r\n\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"am-u-md-3 \">\r\n\t\t\t\t\t<div class=\"footer_main--column\">\r\n\t\t\t\t\t\t<strong class=\"footer_main--column_title\">联系详情</strong>\r\n\t\t\t\t\t\t<ul class=\"footer_contact_info\">\r\n\t\t\t\t\t\t\t<li class=\"footer_contact_info--item\"><i class=\"am-icon-phone\"></i><span>服务专线：{{ aboutmobile }}</span></li>\r\n\t\t\t\t\t\t\t<li class=\"footer_contact_info--item\"><i class=\"am-icon-map-marker\"></i><span>{{ aboutaddress }}</span></li>\r\n\t\t\t\t\t\t\t<li class=\"footer_contact_info--item\"><i class=\"am-icon-clock-o\"></i><span>{{ aboutkefutime }}</span></li>\r\n\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tname: \"Footer\",\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\taboutaddress: '',\r\n\t\t\taboutemail: '',\r\n\t\t\taboutmobile: '',\r\n\t\t\tfooterabout: '',\r\n\t\t\taboutkefutime: '',\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.getConfigFooter()\r\n\t},\r\n\tmethods: {\r\n\t\tgetConfigFooter() {\r\n\t\t\tthis.getRequest(\"/cms/config/footer\").then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.aboutaddress = resp.data.aboutaddress;\r\n\t\t\t\t\tthis.aboutemail = resp.data.aboutemail;\r\n\t\t\t\t\tthis.aboutmobile = resp.data.aboutmobile;\r\n\t\t\t\t\tthis.footerabout = resp.data.footerabout;\r\n\t\t\t\t\tthis.aboutkefutime = resp.data.aboutkefutime;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Footer.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Footer.vue?vue&type=template&id=163a3df6&scoped=true&\"\nimport script from \"./Footer.vue?vue&type=script&lang=js&\"\nexport * from \"./Footer.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"163a3df6\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n\t<main class=\"page-wrapper\">\r\n\t\t<Header/>\r\n\t\t\r\n\t\t<div class=\"main-content\">\r\n\t\t\t<slot></slot>\r\n\t\t</div>\r\n\t\r\n\t\t<Footer/>\r\n\t</main>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"@/components/common/header/Header\";\r\nimport Footer from \"@/components/common/footer/Footer\";\r\nexport default {\r\n\tname: \"Layout\",\r\n\tcomponents:{Header, Footer}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.main-content{\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Layout.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Layout.vue?vue&type=template&id=26a44a32&scoped=true&\"\nimport script from \"./Layout.vue?vue&type=script&lang=js&\"\nexport * from \"./Layout.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Layout.vue?vue&type=style&index=0&id=26a44a32&prod&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"26a44a32\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('Layout',[_c('div',{staticClass:\"login-page\"},[_c('div',{staticClass:\"login-container\"},[_c('div',{staticClass:\"login-left\"},[_c('div',{staticClass:\"login-logo\"},[_c('img',{staticClass:\"logo-image\",attrs:{\"src\":require(\"@/assets/images/logo.png\"),\"alt\":\"国际水晶疗愈协会\"}})]),_c('div',{staticClass:\"login-welcome\"},[_c('h2',[_vm._v(\"欢迎来到\")]),_c('h1',[_vm._v(\"国际水晶疗愈协会\")]),_c('p',[_vm._v(\"登录后享受专业的水晶疗愈服务和丰富的学习资源\")])]),_c('div',{staticClass:\"login-decoration\"},[_c('div',{staticClass:\"crystal-1\"}),_c('div',{staticClass:\"crystal-2\"}),_c('div',{staticClass:\"crystal-3\"})])]),_c('div',{staticClass:\"login-right\"},[(!_vm.isLoggedIn)?_c('div',{staticClass:\"login-form-container\"},[_c('h2',{staticClass:\"login-title\"},[_vm._v(\"用户登录\")]),_c('p',{staticClass:\"login-subtitle\"},[_vm._v(\"欢迎回来，请登录您的账号\")]),_c('div',{staticClass:\"login-tabs\"},[_c('div',{class:['tab-item', { active: _vm.loginType === 'code' }],on:{\"click\":function($event){return _vm.switchLoginType('code')}}},[_vm._v(\" 验证码登录 \")]),_c('div',{class:['tab-item', { active: _vm.loginType === 'password' }],on:{\"click\":function($event){return _vm.switchLoginType('password')}}},[_vm._v(\" 账号密码登录 \")])]),(_vm.loginType === 'password')?_c('van-form',{on:{\"submit\":_vm.onSubmit}},[_c('div',{staticClass:\"form-item\"},[_c('label',{attrs:{\"for\":\"username\"}},[_vm._v(\"用户名/手机号\")]),_c('van-field',{staticClass:\"login-input\",attrs:{\"name\":\"username\",\"placeholder\":\"请输入用户名或手机号\",\"rules\":[{ required: true, message: '请输入用户名/手机号' }]},model:{value:(_vm.loginForm.username),callback:function ($$v) {_vm.$set(_vm.loginForm, \"username\", $$v)},expression:\"loginForm.username\"}})],1),_c('div',{staticClass:\"form-item\"},[_c('label',{attrs:{\"for\":\"password\"}},[_vm._v(\"密码\")]),_c('van-field',{staticClass:\"login-input\",attrs:{\"type\":\"password\",\"name\":\"password\",\"placeholder\":\"请输入密码\",\"rules\":[{ required: true, message: '请输入密码' }]},model:{value:(_vm.loginForm.password),callback:function ($$v) {_vm.$set(_vm.loginForm, \"password\", $$v)},expression:\"loginForm.password\"}})],1),_c('div',{staticClass:\"login-options\"},[_c('div',{staticClass:\"remember-me\"},[_c('van-checkbox',{model:{value:(_vm.rememberMe),callback:function ($$v) {_vm.rememberMe=$$v},expression:\"rememberMe\"}},[_vm._v(\"记住我\")])],1)]),_c('div',{staticClass:\"form-submit\"},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"primary\",\"native-type\":\"submit\",\"loading\":_vm.loading}},[_vm._v(\" 登录 \")])],1)]):_c('van-form',{on:{\"submit\":_vm.onCodeSubmit}},[_c('div',{staticClass:\"form-item\"},[_c('label',{attrs:{\"for\":\"phone\"}},[_vm._v(\"手机号\")]),_c('van-field',{staticClass:\"login-input\",attrs:{\"name\":\"phone\",\"placeholder\":\"请输入手机号\",\"rules\":[\n                                    { required: true, message: '请输入手机号' },\n                                    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码' }\n                                ]},model:{value:(_vm.codeForm.phone),callback:function ($$v) {_vm.$set(_vm.codeForm, \"phone\", $$v)},expression:\"codeForm.phone\"}})],1),_c('div',{staticClass:\"form-item\"},[_c('label',{attrs:{\"for\":\"code\"}},[_vm._v(\"验证码\")]),_c('div',{staticClass:\"code-field\"},[_c('van-field',{staticClass:\"login-input code-input\",attrs:{\"name\":\"code\",\"placeholder\":\"请输入验证码\",\"rules\":[{ required: true, message: '请输入验证码' }]},model:{value:(_vm.codeForm.code),callback:function ($$v) {_vm.$set(_vm.codeForm, \"code\", $$v)},expression:\"codeForm.code\"}}),_c('van-button',{staticClass:\"code-button\",attrs:{\"size\":\"small\",\"type\":\"primary\",\"disabled\":_vm.codeSending || _vm.cooldown > 0},on:{\"click\":function($event){$event.preventDefault();return _vm.getVerificationCode.apply(null, arguments)}}},[_vm._v(\" \"+_vm._s(_vm.cooldown > 0 ? (_vm.cooldown + \"秒后重新获取\") : '获取验证码')+\" \")])],1)]),_c('div',{staticClass:\"form-submit\"},[_c('van-button',{attrs:{\"round\":\"\",\"block\":\"\",\"type\":\"primary\",\"native-type\":\"submit\",\"loading\":_vm.loading}},[_vm._v(\" 登录 \")])],1)]),_c('div',{staticClass:\"register-link\"},[_c('span',[_vm._v(\"还没有账号？\")]),_c('router-link',{attrs:{\"to\":\"/register\"}},[_vm._v(\"立即注册\")])],1)],1):_c('div',{staticClass:\"logged-in-container\"},[_c('div',{staticClass:\"user-info\"},[_c('img',{staticClass:\"user-avatar\",attrs:{\"src\":_vm.userInfo.avatar || require('@/assets/images/pattern-dark.png'),\"alt\":\"头像\"}}),_c('h3',{staticClass:\"user-name\"},[_vm._v(_vm._s(_vm.userInfo.nickname || _vm.userInfo.nickName || _vm.userInfo.username))]),_c('p',{staticClass:\"user-role\"},[_vm._v(_vm._s(_vm.userInfo.phone || '暂无手机号'))])]),_c('div',{staticClass:\"action-buttons\"},[_c('van-button',{staticClass:\"logout-btn\",attrs:{\"round\":\"\",\"block\":\"\",\"plain\":\"\"},on:{\"click\":_vm.logout}},[_vm._v(\" 退出登录 \")])],1)])])])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n\t<Layout>\r\n\t<div class=\"login-page\">\r\n\t\t<div class=\"login-container\">\r\n\t\t\t<div class=\"login-left\">\r\n\t\t\t\t<div class=\"login-logo\">\r\n\t\t\t\t\t<img src=\"@/assets/images/logo.png\" alt=\"国际水晶疗愈协会\" class=\"logo-image\" />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"login-welcome\">\r\n\t\t\t\t\t<h2>欢迎来到</h2>\r\n\t\t\t\t\t<h1>国际水晶疗愈协会</h1>\r\n\t\t\t\t\t<p>登录后享受专业的水晶疗愈服务和丰富的学习资源</p>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"login-decoration\">\r\n\t\t\t\t\t<div class=\"crystal-1\"></div>\r\n\t\t\t\t\t<div class=\"crystal-2\"></div>\r\n\t\t\t\t\t<div class=\"crystal-3\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<div class=\"login-right\">\r\n\t\t\t\t<div class=\"login-form-container\" v-if=\"!isLoggedIn\">\r\n\t\t\t\t\t<h2 class=\"login-title\">用户登录</h2>\r\n\t\t\t\t\t<p class=\"login-subtitle\">欢迎回来，请登录您的账号</p>\r\n                    \r\n                    <div class=\"login-tabs\">\r\n                        <div \r\n                            :class=\"['tab-item', { active: loginType === 'code' }]\" \r\n                            @click=\"switchLoginType('code')\"\r\n                        >\r\n                            验证码登录\r\n                        </div>\r\n                        <div \r\n                            :class=\"['tab-item', { active: loginType === 'password' }]\" \r\n                            @click=\"switchLoginType('password')\"\r\n                        >\r\n                            账号密码登录\r\n                        </div>\r\n                    </div>\r\n\r\n\t\t\t\t\t<van-form @submit=\"onSubmit\" v-if=\"loginType === 'password'\">\r\n\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t<label for=\"username\">用户名/手机号</label>\r\n\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\tv-model=\"loginForm.username\"\r\n\t\t\t\t\t\t\t\tname=\"username\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入用户名或手机号\"\r\n\t\t\t\t\t\t\t\t:rules=\"[{ required: true, message: '请输入用户名/手机号' }]\"\r\n\t\t\t\t\t\t\t\tclass=\"login-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t<label for=\"password\">密码</label>\r\n\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\tv-model=\"loginForm.password\"\r\n\t\t\t\t\t\t\t\ttype=\"password\"\r\n\t\t\t\t\t\t\t\tname=\"password\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入密码\"\r\n\t\t\t\t\t\t\t\t:rules=\"[{ required: true, message: '请输入密码' }]\"\r\n\t\t\t\t\t\t\t\tclass=\"login-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"login-options\">\r\n\t\t\t\t\t\t\t<div class=\"remember-me\">\r\n\t\t\t\t\t\t\t\t<van-checkbox v-model=\"rememberMe\">记住我</van-checkbox>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<!-- <router-link to=\"/forgotPassword\" class=\"forgot-password\">忘记密码？</router-link> -->\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t<van-button round block type=\"primary\" native-type=\"submit\" :loading=\"loading\">\r\n\t\t\t\t\t\t\t\t登录\r\n\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</van-form>\r\n\r\n                    <van-form @submit=\"onCodeSubmit\" v-else>\r\n\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t<label for=\"phone\">手机号</label>\r\n\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\tv-model=\"codeForm.phone\"\r\n\t\t\t\t\t\t\t\tname=\"phone\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入手机号\"\r\n\t\t\t\t\t\t\t\t:rules=\"[\r\n                                    { required: true, message: '请输入手机号' },\r\n                                    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码' }\r\n                                ]\"\r\n\t\t\t\t\t\t\t\tclass=\"login-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t<label for=\"code\">验证码</label>\r\n                            <div class=\"code-field\">\r\n                                <van-field\r\n                                    v-model=\"codeForm.code\"\r\n                                    name=\"code\"\r\n                                    placeholder=\"请输入验证码\"\r\n                                    :rules=\"[{ required: true, message: '请输入验证码' }]\"\r\n                                    class=\"login-input code-input\"\r\n                                />\r\n                                <van-button \r\n                                    size=\"small\" \r\n                                    type=\"primary\" \r\n                                    class=\"code-button\" \r\n                                    :disabled=\"codeSending || cooldown > 0\"\r\n                                    @click.prevent=\"getVerificationCode\"\r\n                                >\r\n                                    {{ cooldown > 0 ? `${cooldown}秒后重新获取` : '获取验证码' }}\r\n                                </van-button>\r\n                            </div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t<van-button round block type=\"primary\" native-type=\"submit\" :loading=\"loading\">\r\n\t\t\t\t\t\t\t\t登录\r\n\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</van-form>\r\n\r\n\t\t\t\t\t<div class=\"register-link\">\r\n\t\t\t\t\t\t<span>还没有账号？</span>\r\n\t\t\t\t\t\t<router-link to=\"/register\">立即注册</router-link>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"logged-in-container\" v-else>\r\n\t\t\t\t\t<div class=\"user-info\">\r\n\t\t\t\t\t\t<img :src=\"userInfo.avatar || require('@/assets/images/pattern-dark.png')\" alt=\"头像\" class=\"user-avatar\">\r\n\t\t\t\t\t\t<h3 class=\"user-name\">{{ userInfo.nickname || userInfo.nickName || userInfo.username }}</h3>\r\n\t\t\t\t\t\t<p class=\"user-role\">{{ userInfo.phone || '暂无手机号' }}</p>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"action-buttons\">\r\n\t\t\t\t\t\t<!-- <van-button round block type=\"primary\" @click=\"goToUserCenter\">\r\n\t\t\t\t\t\t\t进入个人中心\r\n\t\t\t\t\t\t</van-button> -->\r\n\t\t\t\t\t\t<van-button round block plain @click=\"logout\" class=\"logout-btn\">\r\n\t\t\t\t\t\t\t退出登录\r\n\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport Message from \"@/utils/message\";\r\n\r\nexport default {\r\n\tname: \"LoginView\",\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tredirect: '',\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tloading: false,\r\n\t\t\tisLoggedIn: false,\r\n\t\t\tuserInfo: {},\r\n\t\t\trememberMe: false,\r\n            loginType: 'code', // 'password' 或 'code'\r\n\t\t\tloginForm: {\r\n\t\t\t\tusername: '',\r\n\t\t\t\tpassword: ''\r\n\t\t\t},\r\n            codeForm: {\r\n                phone: '',\r\n                code: ''\r\n            },\r\n            codeSending: false,\r\n            cooldown: 0,\r\n            timer: null\r\n\t\t}\r\n\t},\r\n\tcomponents: {\r\n\t\tLayout\r\n\t},\r\n\tmounted() {\r\n\t\tthis.redirect = decodeURIComponent(this.$route.query.redirect || '');\r\n\t\tthis.$wxShare();\r\n\t\tthis.checkLoginStatus();\r\n\t},\r\n    beforeDestroy() {\r\n        if (this.timer) {\r\n            clearInterval(this.timer);\r\n        }\r\n    },\r\n\tmethods: {\r\n        switchLoginType(type) {\r\n            this.loginType = type;\r\n        },\r\n\t\tcheckLoginStatus() {\r\n\t\t\tconst token = localStorage.getItem(\"token\");\r\n\t\t\tif (token) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst userInfoStr = localStorage.getItem(\"userInfo\") ;\r\n\t\t\t\t\tif (userInfoStr) {\r\n\t\t\t\t\t\tthis.userInfo = JSON.parse(userInfoStr);\r\n\t\t\t\t\t\tthis.isLoggedIn = true;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 有token但没有用户信息，尝试获取用户信息\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"解析用户信息失败\", error);\r\n\t\t\t\t\tthis.clearLoginInfo();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetUserInfo() {\r\n\t\t\tthis.getRequest(\"/user\").then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.userInfo = resp.data;\r\n\t\t\t\t\tthis.isLoggedIn = true;\r\n\t\t\t\t\t// 更新存储的用户信息\r\n\t\t\t\t\tsessionStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\r\n\t\t\t\t\tlocalStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.clearLoginInfo();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tonSubmit() {\r\n\t\t\tthis.loading = true;\r\n\t\t\t// 登录请求\r\n\t\t\tthis.postRequest(\"/loginCms\", {\r\n\t\t\t\taccount: this.loginForm.username,\r\n\t\t\t\tpassword: this.loginForm.password\r\n\t\t\t})\r\n\t\t\t\t.then(resp => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\t\t// 根据记住我选项决定存储位置\r\n\t\t\t\t\t\t// const storage = this.rememberMe ? localStorage : sessionStorage;\r\n\t\t\t\t\t\t// 保存token\r\n\t\t\t\t\t\tlocalStorage.setItem(\"token\", resp.data.token);\r\n\t\t\t\t\t\t// 保存用户信息\r\n\t\t\t\t\t\tlocalStorage.setItem(\"userInfo\", JSON.stringify(resp.data.userInfo));\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.userInfo = resp.data.userInfo;\r\n\t\t\t\t\t\tthis.isLoggedIn = true;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tMessage.success(\"登录成功\");\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 检查是否有重定向URL\r\n\t\t\t\t\t\tif (this.redirect) {\r\n\t\t\t\t\t\t\tlocation.href = this.redirect;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 跳转到首页\r\n\t\t\t\t\t\t\tthis.$router.push(\"/\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tMessage.error(resp.message || \"登录失败，请检查用户名和密码\");\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n        onCodeSubmit() {\r\n            if (!this.codeForm.phone || !this.codeForm.code) {\r\n                Message.error(\"请输入手机号和验证码\");\r\n                return;\r\n            }\r\n\r\n            this.loading = true;\r\n            // 验证码登录请求\r\n            this.postRequest(\"/login/mobile\", {\r\n                phone: this.codeForm.phone,\r\n                captcha: this.codeForm.code\r\n            })\r\n            .then(resp => {\r\n                this.loading = false;\r\n                if (resp && resp.code == 200) {\r\n                    // 保存token\r\n                    localStorage.setItem(\"token\", resp.data.token);\r\n                    // 保存用户信息\r\n                    localStorage.setItem(\"userInfo\", JSON.stringify(resp.data.userInfo));\r\n                    \r\n                    this.userInfo = resp.data.userInfo;\r\n                    this.isLoggedIn = true;\r\n                    \r\n                    Message.success(\"登录成功\");\r\n                    \r\n                    // 检查是否有重定向URL\r\n                    if (this.redirect) {\r\n\t\t\t\t\t\tlocation.href = this.redirect;\r\n                    } else {\r\n                        // 跳转到首页\r\n                        this.$router.push(\"/\");\r\n                    }\r\n                } else {\r\n                    Message.error(resp.message || \"登录失败，请检查手机号和验证码\");\r\n                }\r\n            });\r\n        },\r\n        getVerificationCode() {\r\n            if (this.codeSending || this.cooldown > 0) return;\r\n            \r\n            const phone = this.codeForm.phone;\r\n            if (!phone) {\r\n                Message.error(\"请输入手机号\");\r\n                return;\r\n            }\r\n            \r\n            if (!/^1[3-9]\\d{9}$/.test(phone)) {\r\n                Message.error(\"请输入正确的手机号码\");\r\n                return;\r\n            }\r\n            \r\n            this.codeSending = true;\r\n            \r\n            // 发送验证码请求\r\n            this.postRequestParams(\"/sendCode\", {\r\n                phone: phone,\r\n            })\r\n            .then(resp => {\r\n                this.codeSending = false;\r\n                if (resp && resp.code == 200) {\r\n                    Message.success(\"验证码已发送，请注意查收\");\r\n                    // 开始倒计时\r\n                    this.cooldown = 60;\r\n                    this.timer = setInterval(() => {\r\n                        this.cooldown--;\r\n                        if (this.cooldown <= 0) {\r\n                            clearInterval(this.timer);\r\n                        }\r\n                    }, 1000);\r\n                } else {\r\n                    Message.error(resp.message || \"验证码发送失败，请稍后重试\");\r\n                }\r\n            })\r\n            .catch(() => {\r\n                this.codeSending = false;\r\n                Message.error(\"验证码发送失败，请稍后重试\");\r\n            });\r\n        },\r\n\t\tgoToUserCenter() {\r\n\t\t\tthis.$router.push(\"/user\");\r\n\t\t},\r\n\t\tlogout() {\r\n\t\t\t// 退出登录请求\r\n\t\t\tthis.postRequest(\"/logout\").finally(() => {\r\n\t\t\t\tthis.clearLoginInfo();\r\n\t\t\t\tMessage.success(\"已退出登录\");\r\n\t\t\t\t// 刷新页面\r\n\t\t\t\tthis.isLoggedIn = false;\r\n\t\t\t\tthis.userInfo = {};\r\n\t\t\t\tlocation.reload();\r\n\t\t\t});\r\n\t\t},\r\n\t\tclearLoginInfo() {\r\n\t\t\t// 清除所有存储的登录信息\r\n\t\t\tsessionStorage.removeItem(\"token\");\r\n\t\t\tsessionStorage.removeItem(\"userInfo\");\r\n\t\t\tlocalStorage.removeItem(\"token\");\r\n\t\t\tlocalStorage.removeItem(\"userInfo\");\r\n\t\t\tthis.isLoggedIn = false;\r\n\t\t\tthis.userInfo = {};\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.login-page {\r\n\tmin-height: 100vh;\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground-color: #f8f9fa;\r\n\tbackground-image: url('@/assets/images/pattern-dark.png');\r\n\tbackground-repeat: repeat;\r\n\tpadding: 40px 20px;\r\n}\r\n\r\n.login-container {\r\n\tdisplay: flex;\r\n\twidth: 1200px;\r\n\tmin-height: 680px;\r\n\tborder-radius: 20px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.login-left {\r\n\tflex: 1;\r\n\tbackground: linear-gradient(135deg, #5e258f, #8647ad);\r\n\tbackground-image: linear-gradient(135deg, rgba(94, 37, 143, 0.95), rgba(134, 71, 173, 0.9)), url('https://images.unsplash.com/photo-1521320226546-87b106cd055d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');\r\n\tbackground-size: cover;\r\n\tbackground-position: center;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tposition: relative;\r\n\tcolor: #fff;\r\n\tpadding: 50px;\r\n\toverflow: hidden;\r\n}\r\n\r\n.login-logo {\r\n\tmargin-bottom: 40px;\r\n\ttext-align: center;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.logo-image {\r\n\twidth: 180px;\r\n\theight: auto;\r\n}\r\n\r\n.login-welcome {\r\n\ttext-align: center;\r\n\tmargin-bottom: 40px;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.login-welcome h2 {\r\n\tfont-size: 24px;\r\n\tfont-weight: 400;\r\n\tmargin-bottom: 20px;\r\n\tcolor: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.login-welcome h1 {\r\n\tfont-size: 38px;\r\n\tfont-weight: 700;\r\n\tmargin-bottom: 20px;\r\n\ttext-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.login-welcome p {\r\n\tfont-size: 16px;\r\n\tline-height: 1.6;\r\n\tmax-width: 400px;\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.login-decoration {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.crystal-1, .crystal-2, .crystal-3 {\r\n\tposition: absolute;\r\n\tbackground: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));\r\n\tbackdrop-filter: blur(5px);\r\n\tborder: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.crystal-1 {\r\n\twidth: 300px;\r\n\theight: 300px;\r\n\ttop: -100px;\r\n\tleft: -150px;\r\n}\r\n\r\n.crystal-2 {\r\n\twidth: 200px;\r\n\theight: 200px;\r\n\tbottom: 50px;\r\n\tright: -50px;\r\n}\r\n\r\n.crystal-3 {\r\n\twidth: 150px;\r\n\theight: 150px;\r\n\tbottom: -50px;\r\n\tleft: 100px;\r\n}\r\n\r\n.login-right {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 60px;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.login-form-container, .logged-in-container {\r\n\twidth: 100%;\r\n\tmax-width: 400px;\r\n}\r\n\r\n.login-title {\r\n\tfont-size: 32px;\r\n\tfont-weight: 700;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10px;\r\n\ttext-align: center;\r\n}\r\n\r\n.login-subtitle {\r\n\tfont-size: 16px;\r\n\tcolor: #666;\r\n\tmargin-bottom: 40px;\r\n\ttext-align: center;\r\n}\r\n\r\n.login-tabs {\r\n    display: flex;\r\n    margin-bottom: 30px;\r\n    border-bottom: 1px solid #e8eaee;\r\n}\r\n\r\n.tab-item {\r\n    flex: 1;\r\n    text-align: center;\r\n    padding: 15px 0;\r\n    font-size: 16px;\r\n    color: #666;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    position: relative;\r\n}\r\n\r\n.tab-item.active {\r\n    color: #5e258f;\r\n    font-weight: 500;\r\n}\r\n\r\n.tab-item.active:after {\r\n    content: \"\";\r\n    position: absolute;\r\n    bottom: -1px;\r\n    left: 20%;\r\n    width: 60%;\r\n    height: 3px;\r\n    background: #5e258f;\r\n    border-radius: 3px 3px 0 0;\r\n}\r\n\r\n.form-item {\r\n\tmargin-bottom: 25px;\r\n}\r\n\r\n.form-item label {\r\n\tdisplay: block;\r\n\tmargin-bottom: 8px;\r\n\tfont-size: 14px;\r\n\tcolor: #555;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.login-input {\r\n\t/deep/ .van-field__control {\r\n\t\theight: 48px;\r\n\t\tpadding: 0 15px;\r\n\t\tfont-size: 15px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder: 1px solid #e8eaee;\r\n\t\tborder-radius: 8px;\r\n\t}\r\n\r\n\t/deep/ .van-field__control:focus {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-color: #5e258f;\r\n\t\tbox-shadow: 0 0 0 3px rgba(94, 37, 143, 0.1);\r\n\t}\r\n}\r\n\r\n.code-field {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.code-input {\r\n    flex: 1;\r\n    margin-right: 10px;\r\n}\r\n\r\n.code-button {\r\n    white-space: nowrap;\r\n    height: 48px;\r\n    background: linear-gradient(45deg, #5e258f, #8647ad);\r\n    border-color: #5e258f;\r\n    \r\n    &:disabled {\r\n        background: #cccccc;\r\n        border-color: #bbbbbb;\r\n        color: #ffffff;\r\n    }\r\n}\r\n\r\n.login-options {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 30px;\r\n}\r\n\r\n.remember-me {\r\n\t/deep/ .van-checkbox__label {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t/deep/ .van-checkbox__icon--checked {\r\n\t\tbackground-color: #5e258f;\r\n\t\tborder-color: #5e258f;\r\n\t}\r\n}\r\n\r\n.forgot-password {\r\n\tcolor: #5e258f;\r\n\tfont-size: 14px;\r\n\ttext-decoration: none;\r\n\ttransition: color 0.2s;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #8647ad;\r\n\t\ttext-decoration: underline;\r\n\t}\r\n}\r\n\r\n.form-submit {\r\n\tmargin-bottom: 30px;\r\n\t\r\n\t.van-button {\r\n\t\theight: 50px;\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 500;\r\n\t\tbackground: linear-gradient(45deg, #5e258f, #8647ad);\r\n\t\tborder-color: #5e258f;\r\n\t\ttransition: all 0.3s ease;\r\n\t\t\r\n\t\t&:hover {\r\n\t\t\tbackground: linear-gradient(45deg, #4b1e73, #733c94);\r\n\t\t\tborder-color: #4b1e73;\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 6px 15px rgba(94, 37, 143, 0.25);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.register-link {\r\n\ttext-align: center;\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n\t\r\n\ta {\r\n\t\tcolor: #5e258f;\r\n\t\tfont-weight: 500;\r\n\t\ttext-decoration: none;\r\n\t\tmargin-left: 5px;\r\n\t\t\r\n\t\t&:hover {\r\n\t\t\ttext-decoration: underline;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.logged-in-container {\r\n\ttext-align: center;\r\n\tpadding: 20px 0;\r\n\t\r\n\t.user-info {\r\n\t\tmargin-bottom: 40px;\r\n\t\t\r\n\t\t.user-avatar {\r\n\t\t\twidth: 110px;\r\n\t\t\theight: 110px;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tobject-fit: cover;\r\n\t\t\tborder: 4px solid rgba(94, 37, 143, 0.2);\r\n\t\t\tmargin-bottom: 20px;\r\n\t\t}\r\n\t\t\r\n\t\t.user-name {\r\n\t\t\tfont-size: 24px;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tmargin-bottom: 8px;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t\t\r\n\t\t.user-role {\r\n\t\t\tfont-size: 16px;\r\n\t\t\tcolor: #666;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.action-buttons {\r\n\t\t.van-button {\r\n\t\t\tmargin-bottom: 16px;\r\n\t\t\theight: 50px;\r\n\t\t\tfont-size: 16px;\r\n\t\t\t\r\n\t\t\t&--primary {\r\n\t\t\t\tbackground: linear-gradient(45deg, #5e258f, #8647ad);\r\n\t\t\t\tborder-color: #5e258f;\r\n\t\t\t\t\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground: linear-gradient(45deg, #4b1e73, #733c94);\r\n\t\t\t\t\tborder-color: #4b1e73;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.logout-btn {\r\n\t\t\tborder-color: #f56c6c;\r\n\t\t\tcolor: #f56c6c;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground-color: rgba(245, 108, 108, 0.05);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n\t.login-container {\r\n\t\twidth: 95%;\r\n\t\tflex-direction: column;\r\n\t\tmin-height: auto;\r\n\t}\r\n\t\r\n\t.login-left {\r\n\t\tpadding: 40px 20px;\r\n\t}\r\n\t\r\n\t.login-right {\r\n\t\tpadding: 40px 20px;\r\n\t}\r\n\t\r\n\t.login-welcome h1 {\r\n\t\tfont-size: 32px;\r\n\t}\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\t.login-page {\r\n\t\tpadding: 20px 10px;\r\n\t}\r\n\t\r\n\t.login-container {\r\n\t\twidth: 100%;\r\n\t\tborder-radius: 10px;\r\n\t}\r\n\t\r\n\t.login-left {\r\n\t\tpadding: 30px 15px;\r\n\t}\r\n\t\r\n\t.login-right {\r\n\t\tpadding: 30px 15px;\r\n\t}\r\n\t\r\n\t.login-logo .logo-image {\r\n\t\twidth: 150px;\r\n\t}\r\n\t\r\n\t.login-welcome h1 {\r\n\t\tfont-size: 28px;\r\n\t}\r\n\t\r\n\t.login-welcome p {\r\n\t\tfont-size: 14px;\r\n\t}\r\n\t\r\n\t.login-title {\r\n\t\tfont-size: 28px;\r\n\t}\r\n\t\r\n\t.login-subtitle {\r\n\t\tfont-size: 14px;\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./login.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./login.vue?vue&type=template&id=7da3b730&scoped=true&\"\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=7da3b730&prod&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7da3b730\",\n  null\n  \n)\n\nexport default component.exports", "\r\n/**\r\n * 判断是否为移动设备\r\n * @returns {*|boolean}\r\n */\r\nexport function isMobilePhone() {\r\n    // 获取访问链接\r\n    let href = window.location.href;\r\n    if(href.includes(\"pc=true\")) {\r\n      return false;\r\n    }\r\n    if(href.includes(\"pc=false\")) {\r\n      return true;\r\n    }\r\n    //获取访问的user-agent\r\n    let ua = window.navigator.userAgent.toLowerCase();\r\n    //判断user-agent\r\n    // isWX = /MicroMessenger/i.test(ua); //微信端\r\n    // isIOS = /(iPhone|iPad|iPod|iOS)/i.test(ua); //苹果家族\r\n    // isAndroid = /(android|nexus)/i.test(ua); //安卓家族\r\n    // isWindows = /(Windows Phone|windows[\\s+]phone)/i.test(ua); //微软家族\r\n    return /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|MicroMessenger)/i.test(ua)\r\n\r\n}\r\n\r\n/**\r\n * 手机号码\r\n * @param {*} s\r\n */\r\nexport function isMobile(s) {\r\n    return /^1[0-9]{10}$/.test(s)\r\n}\r\n"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "_t", "staticRenderFns", "_m", "_v", "isMobilePhone", "style", "staticStyle", "class", "attrs", "on", "goLogin", "_s", "userInfo", "isLoggedIn", "nickname", "nick<PERSON><PERSON>", "mobileMenuActive", "toggleMenu", "_l", "item", "index", "key", "getMobileItemStyle", "path", "name", "_e", "AppFunctions", "addClass", "element", "className", "document", "querySelector", "classList", "add", "removeClass", "remove", "toggleClass", "toggle", "flatDeep", "arr", "d", "reduce", "acc", "val", "concat", "Array", "isArray", "slice", "slugify", "text", "toString", "toLowerCase", "replace", "containsObject", "obj", "list", "i", "length", "slug", "components", "data", "navItems", "methods", "checkLoginStatus", "token", "localStorage", "getItem", "userInfoStr", "JSON", "parse", "getUserInfo", "error", "console", "clearLoginInfo", "$router", "push", "<PERSON><PERSON><PERSON><PERSON>", "body", "overflow", "transitionDelay", "toggle<PERSON><PERSON>yHeader", "scrolled", "documentElement", "scrollTop", "sessionStorage", "removeItem", "created", "window", "addEventListener", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "component", "footerabout", "aboutmobile", "aboutaddress", "aboutkefutime", "aboutemail", "getConfigFooter", "getRequest", "then", "resp", "code", "Header", "Footer", "avatar", "username", "phone", "logout", "active", "loginType", "$event", "switchLoginType", "onSubmit", "required", "message", "model", "value", "loginForm", "callback", "$$v", "$set", "expression", "rememberMe", "loading", "onCodeSubmit", "pattern", "codeForm", "codeSending", "cooldown", "preventDefault", "getVerificationCode", "apply", "arguments", "redirect", "password", "timer", "Layout", "decodeURIComponent", "$route", "query", "$wxShare", "clearInterval", "type", "setItem", "stringify", "postRequest", "account", "Message", "success", "location", "href", "<PERSON><PERSON>a", "test", "postRequestParams", "setInterval", "catch", "goToUserCenter", "finally", "reload", "includes", "ua", "navigator", "userAgent", "isMobile", "s"], "sourceRoot": ""}