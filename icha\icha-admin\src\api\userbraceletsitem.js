
import request from '@/utils/request'

/**
 * 新增UserBraceletsItem
 * @param pram
 */
export function UserBraceletsItemCreateApi(data) {
    return request({
        url: 'userbraceletsitem/save',
        method: 'POST',
        data
    })
}

/**
 * userbraceletsitem更新
 * @param pram
 */
export function userbraceletsitemUpdateApi(data) {
    return request({
        url: 'userbraceletsitem/update',
        method: 'POST',
        data
    })
}

/**
 * userbraceletsitem详情
 * @param pram
 */
export function userbraceletsitemDetailApi(id) {
    return request({
        url: `userbraceletsitem/info/${id}`,
        method: 'GET'
    })
}

/**
 * userbraceletsitem删除
 * @param pram
 */
export function userbraceletsitemDeleteApi(id) {
    return request({
        url: `userbraceletsitem/delete/${id}`,
        method: 'get'
    })
}


/**
 * userbraceletsitem列表
 * @param pram
 */
export function userbraceletsitemListApi(params) {
    return request({
        url: 'userbraceletsitem/list',
        method: 'GET',
        params
    })
}

