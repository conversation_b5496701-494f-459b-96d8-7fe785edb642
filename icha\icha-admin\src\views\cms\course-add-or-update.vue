<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="名称" prop="title">
        <el-input v-model="dataForm.title" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item label="简介" prop="brief">
        <el-input v-model="dataForm.brief" placeholder="简介"></el-input>
      </el-form-item>
      <el-form-item label="图片" prop="cover">
        <div class="upLoadPicBox" @click="modalPicTap('1')">
          <div v-if="dataForm.cover" class="pictrue"><img :src="dataForm.cover"></div>
          <div v-else class="upLoad">
            <i class="el-icon-camera cameraIconfont" />
          </div>
        </div>
        <!-- 尺寸提示 -->
        <div class="sizeTip">
          <span>尺寸提示：</span>
          <span>宽度：1920px</span>
          <span>高度：1080px</span>
        </div>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-input v-model="dataForm.tags" placeholder="标签，多个用逗号分隔"></el-input>
      </el-form-item>
      <!-- <el-form-item label="地址" prop="location">
        <el-input v-model="dataForm.location" placeholder="地址"></el-input>
      </el-form-item> -->
      <el-form-item label="考卷地址" prop="url">
        <el-input v-model="dataForm.url" placeholder="考卷地址"></el-input>
      </el-form-item>
      <el-form-item label="是否展示" prop="isShow">
        <el-select v-model="dataForm.isShow" placeholder="是否展示" filterable>
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否首页" prop="isIndex">
        <el-select v-model="dataForm.isIndex" placeholder="是否首页" filterable>
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="paixu">
        <el-input-number v-model="dataForm.paixu" :min="0" placeholder="排序"></el-input-number>
      </el-form-item>
      <!-- <el-form-item label="课程时间" prop="dateRange">
        <el-date-picker v-model="dateRange" type="daterange" value-format="yyyy/MM/dd" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="价格" prop="price">
        <el-input-number v-model="dataForm.price" :precision="2" :step="0.1" :min="0"></el-input-number>
      </el-form-item>
      <el-form-item label="视频类型" prop="type">
        <el-select v-model="dataForm.type" placeholder="视频类型">
          <el-option v-for="item in videoTypeOptions" :key="item.key" :label="item.value" :value="item.key">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="邀请码数量" prop="number" v-if="dataForm.type === 2 && !dataForm.id">
        <el-input v-model="dataForm.number" placeholder="邀请码数量"></el-input>
      </el-form-item>
      <el-form-item label="邀请码" prop="inviteCode" v-if="dataForm.type === 1">
        <el-input v-model="dataForm.inviteCode" placeholder="邀请码"></el-input>
      </el-form-item>
      <el-form-item label="课程内容" prop="content">
        <Tinymce ref="tinymceEditor" v-model="dataForm.content"></Tinymce>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import Tinymce from '@/components/Tinymce/index'
import { videoType } from '@/data/common'
import { cmsCourseCreateApi, cmsCourseUpdateApi, cmsCourseDetailApi } from '@/api/cmsCourse'
import { yesOrNo } from '@/data/common'
export default {
  components: { Tinymce },
  data() {
    return {
      validateNumber: (rule, value, callback) => {
        if (value < 0) {
          callback(new Error('邀请码数量不能小于0'))
        } else {
          callback()
        }
      },
      videoTypeOptions: videoType,
      yesOrNo,
      visible: false,
      dateRange: [],
      dataForm: {
        id: 0,
        title: '',
        brief: '',
        cover: '',
        tags: '',
        location: '',
        isShow: 1,
        isIndex: 0,
        paixu: 0,
        startTime: '',
        endTime: '',
        price: 0,
        type: 0,
        number: 0,
        inviteCode: '',
        url: '',
        content: ''
      },
      dataRule: {
        title: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        isShow: [
          { required: true, message: '是否展示不能为空', trigger: 'blur' }
        ],
        isIndex: [
          { required: true, message: '是否首页不能为空', trigger: 'blur' }
        ],
        paixu: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ],
        // dateRange: [
        //   { required: true, message: '课程时间不能为空', trigger: 'change' }
        // ],
        price: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ],
        number: [
          { required: true, message: '邀请码数量不能为空', trigger: 'blur' },
          { validator: this.validateNumber, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        // 先重置富文本内容为空
        this.dataForm.content = ''
        // 使用Tinymce组件的resetContent方法重置编辑器
        this.$nextTick(() => {
          if (this.$refs.tinymceEditor) {
            this.$refs.tinymceEditor.resetContent('')
          }
        })
        if (this.dataForm.id) {
          cmsCourseDetailApi(this.dataForm.id).then((data) => {
            this.dataForm.title = data.title
            this.dataForm.brief = data.brief
            this.dataForm.cover = data.cover
            this.dataForm.tags = data.tags
            this.dataForm.location = data.location
            this.dataForm.isShow = data.isShow
            this.dataForm.isIndex = data.isIndex
            this.dataForm.paixu = data.paixu
            this.dataForm.startTime = data.startTime
            this.dataForm.endTime = data.endTime
            this.dateRange = data.startTime && data.endTime ? [data.startTime, data.endTime] : []
            this.dataForm.price = data.price
            this.dataForm.type = data.type
            this.dataForm.inviteCode = data.inviteCode
            this.dataForm.content = data.content
            this.dataForm.url = data.url
            
            // 编辑模式下，设置获取的content到编辑器
            this.$nextTick(() => {
              if (this.$refs.tinymceEditor) {
                this.$refs.tinymceEditor.resetContent(data.content || '')
              }
            })
          }).catch((res) => {
            this.$message.error(res.message)
          });
        }
      })
    },
    // 点击图片上传
    modalPicTap(field) {
      const _this = this;
      this.$modalUpload(function (img) {
        _this.dataForm.cover = img[0].sattDir
      }, field, 'content')
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dateRange && this.dateRange.length === 2) {
            this.dataForm.startTime = this.dateRange[0]
            this.dataForm.endTime = this.dateRange[1]
          }
          if (!this.dataForm.id) {
            cmsCourseCreateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          } else {
            cmsCourseUpdateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.upLoadPicBox {
  width: 100px;
  height: 100px;
  position: relative;
  cursor: pointer;
}

.upLoadPicBox .pictrue {
  width: 100%;
  height: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.upLoadPicBox .pictrue img {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.upLoadPicBox .upLoad {
  width: 100%;
  height: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upLoadPicBox .upLoad:hover {
  border-color: #409EFF;
}

.upLoadPicBox .upLoad .cameraIconfont {
  font-size: 28px;
  color: #8c939d;
}
</style>