{"ast": null, "code": "// import Vue from 'vue'\n// import VueRouter from 'vue-router'\n\nVue.use(VueRouter);\nconst routes = [{\n  path: '/',\n  redirect: '/index'\n}, {\n  path: '/index',\n  name: 'index',\n  component: () => import('../views/IndexView.vue')\n}, {\n  path: '/about',\n  name: 'about',\n  component: () => import('../views/AboutView.vue')\n}, {\n  path: '/workshop',\n  name: 'workshop',\n  component: () => import('../views/workshop.vue')\n}, {\n  path: '/course',\n  name: 'course',\n  component: () => import('../views/course.vue')\n}, {\n  path: '/certificate',\n  name: 'certificate',\n  component: () => import('../views/certificate.vue')\n}, {\n  path: '/download',\n  name: 'download',\n  component: () => import('../views/download.vue')\n}, {\n  path: '/join',\n  name: 'join',\n  component: () => import('../views/join.vue')\n}, {\n  path: '/login',\n  name: 'login',\n  component: () => import('../views/login.vue')\n}, {\n  path: '/register',\n  name: 'register',\n  component: () => import('../views/register.vue')\n}, {\n  path: '/forgotPassword',\n  name: 'forgotPassword',\n  component: () => import('../views/ForgotPassword.vue')\n}, {\n  path: '/healers',\n  name: 'healers',\n  component: () => import('../views/HealersView.vue')\n}, {\n  path: '/healer-detail/:id',\n  name: 'healerDetail',\n  component: () => import('../views/HealerDetailView.vue')\n}, {\n  path: '/course-detail',\n  name: 'courseDetail',\n  component: () => import('../views/courseDetail.vue')\n}, {\n  path: '/chakra-test',\n  name: 'chakraTest',\n  component: () => import('../views/ChakraTest.vue')\n}, {\n  path: '/chakra-test/start',\n  name: 'chakraTestStart',\n  component: () => import('../views/ChakraTestStart.vue')\n}, {\n  path: '/chakra-test/detail',\n  name: 'chakraTestDetail',\n  component: () => import('../views/ChakraTestDetail.vue')\n}, {\n  path: '/chakra-test/list',\n  name: 'chakraTestList',\n  component: () => import('../views/ChakraTestList.vue')\n}, {\n  path: '/chakra-test/intro',\n  name: 'chakraTestIntro',\n  component: () => import('../views/ChakraTestIntro.vue')\n}, {\n  path: '/chakra-test/balance',\n  name: 'chakraTestBalance',\n  component: () => import('../views/ChakraTestBalance.vue')\n}];\nconst router = new VueRouter({\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "use", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirect", "name", "component", "router"], "sources": ["C:/Users/<USER>/Desktop/code/crystal-mall/icha/crystal-website/src/router/index.js"], "sourcesContent": ["// import Vue from 'vue'\r\n// import VueRouter from 'vue-router'\r\n\r\nVue.use(VueRouter)\r\n\r\nconst routes = [\r\n    {\r\n        path:'/',\r\n        redirect:'/index'\r\n    },\r\n    {\r\n        path: '/index',\r\n        name: 'index',\r\n        component: () => import('../views/IndexView.vue')\r\n    },\r\n    {\r\n        path: '/about',\r\n        name: 'about',\r\n        component: () => import('../views/AboutView.vue')\r\n    },\r\n    {\r\n        path: '/workshop',\r\n        name: 'workshop',\r\n        component: () => import('../views/workshop.vue')\r\n    },\r\n    {\r\n        path: '/course',\r\n        name: 'course',\r\n        component: () => import('../views/course.vue')\r\n    },\r\n    {\r\n        path: '/certificate',\r\n        name: 'certificate',\r\n        component: () => import('../views/certificate.vue')\r\n    },\r\n    {\r\n        path: '/download',\r\n        name: 'download',\r\n        component: () => import('../views/download.vue')\r\n    },\r\n    {\r\n        path: '/join',\r\n        name: 'join',\r\n        component: () => import('../views/join.vue')\r\n    },\r\n    {\r\n        path: '/login',\r\n        name: 'login',\r\n        component: () => import('../views/login.vue')\r\n    },\r\n    {\r\n        path: '/register',\r\n        name: 'register',\r\n        component: () => import('../views/register.vue')\r\n    },\r\n    {\r\n        path: '/forgotPassword',\r\n        name: 'forgotPassword',\r\n        component: () => import('../views/ForgotPassword.vue')\r\n    },\r\n    {\r\n        path: '/healers',\r\n        name: 'healers',\r\n        component: () => import('../views/HealersView.vue')\r\n    },\r\n    {\r\n        path: '/healer-detail/:id',\r\n        name: 'healerDetail',\r\n        component: () => import('../views/HealerDetailView.vue')\r\n    },\r\n    {\r\n        path: '/course-detail',\r\n        name: 'courseDetail',\r\n        component: () => import('../views/courseDetail.vue')\r\n    },\r\n    {\r\n        path: '/chakra-test',\r\n        name: 'chakraTest',\r\n        component: () => import('../views/ChakraTest.vue')\r\n    },\r\n    {\r\n        path: '/chakra-test/start',\r\n        name: 'chakraTestStart',\r\n        component: () => import('../views/ChakraTestStart.vue')\r\n    },\r\n    {\r\n        path: '/chakra-test/detail',\r\n        name: 'chakraTestDetail',\r\n        component: () => import('../views/ChakraTestDetail.vue')\r\n    },\r\n    {\r\n        path: '/chakra-test/list',\r\n        name: 'chakraTestList',\r\n        component: () => import('../views/ChakraTestList.vue')\r\n    },\r\n    {\r\n        path: '/chakra-test/intro',\r\n        name: 'chakraTestIntro',\r\n        component: () => import('../views/ChakraTestIntro.vue')\r\n    },\r\n    {\r\n        path: '/chakra-test/balance',\r\n        name: 'chakraTestBalance',\r\n        component: () => import('../views/ChakraTestBalance.vue')\r\n    }\r\n]\r\n\r\nconst router = new VueRouter({\r\n    routes\r\n})\r\n\r\nexport default router;\r\n"], "mappings": "AAAA;AACA;;AAEAA,GAAG,CAACC,GAAG,CAACC,SAAS,CAAC;AAElB,MAAMC,MAAM,GAAG,CACX;EACIC,IAAI,EAAC,GAAG;EACRC,QAAQ,EAAC;AACb,CAAC,EACD;EACID,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB;AACpD,CAAC,EACD;EACIH,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB;AACpD,CAAC,EACD;EACIH,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB;AACnD,CAAC,EACD;EACIH,IAAI,EAAE,SAAS;EACfE,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB;AACjD,CAAC,EACD;EACIH,IAAI,EAAE,cAAc;EACpBE,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B;AACtD,CAAC,EACD;EACIH,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB;AACnD,CAAC,EACD;EACIH,IAAI,EAAE,OAAO;EACbE,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB;AAC/C,CAAC,EACD;EACIH,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB;AAChD,CAAC,EACD;EACIH,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,uBAAuB;AACnD,CAAC,EACD;EACIH,IAAI,EAAE,iBAAiB;EACvBE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B;AACzD,CAAC,EACD;EACIH,IAAI,EAAE,UAAU;EAChBE,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B;AACtD,CAAC,EACD;EACIH,IAAI,EAAE,oBAAoB;EAC1BE,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B;AAC3D,CAAC,EACD;EACIH,IAAI,EAAE,gBAAgB;EACtBE,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B;AACvD,CAAC,EACD;EACIH,IAAI,EAAE,cAAc;EACpBE,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,yBAAyB;AACrD,CAAC,EACD;EACIH,IAAI,EAAE,oBAAoB;EAC1BE,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B;AAC1D,CAAC,EACD;EACIH,IAAI,EAAE,qBAAqB;EAC3BE,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B;AAC3D,CAAC,EACD;EACIH,IAAI,EAAE,mBAAmB;EACzBE,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B;AACzD,CAAC,EACD;EACIH,IAAI,EAAE,oBAAoB;EAC1BE,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B;AAC1D,CAAC,EACD;EACIH,IAAI,EAAE,sBAAsB;EAC5BE,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;AAC5D,CAAC,CACJ;AAED,MAAMC,MAAM,GAAG,IAAIN,SAAS,CAAC;EACzBC;AACJ,CAAC,CAAC;AAEF,eAAeK,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}