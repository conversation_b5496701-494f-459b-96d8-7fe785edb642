<template>
  <Layout>
    <div class="chakra-test-page">
      <!-- 页面头部 -->
      <div class="hero-header-section chakra-header">
        <div class="hero-content">
          <h1 class="hero-title">
            <van-icon name="fire-o" size="40" color="rgba(255,255,255,0.9)" class="rotating-icon" /> 脉轮测试
          </h1>
          <p class="hero-subtitle">探索您的能量中心，开启身心灵平衡之旅</p>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="content-wrapper">
        <!-- 脉轮介绍部分 -->
        <section class="chakra-intro-section">
          <div class="intro-content">
            <div class="intro-text">
              <h2 class="section-title">什么是脉轮测试？</h2>
              <p>脉轮是人体能量系统的重要组成部分，通过专业的脉轮测试，您可以了解自己七个主要脉轮的能量状态。</p>
              <p>在开始测试之前，请找一个安静不受干扰的地方，让自己放松，放下任何情绪，以获得最准确的测试结果。</p>

              <div class="test-features">
                <div class="feature-item">
                  <van-icon name="success" size="20" color="#c9ab79" />
                  <span>专业测试问卷</span>
                </div>
                <div class="feature-item">
                  <van-icon name="bar-chart-o" size="20" color="#c9ab79" />
                  <span>详细结果分析</span>
                </div>
                <div class="feature-item">
                  <van-icon name="like-o" size="20" color="#c9ab79" />
                  <span>个性化建议</span>
                </div>
              </div>
            </div>

            <div class="intro-visual">
              <div class="chakra-circles-container">
                <img
                  v-if="mailunshouye"
                  :src="mailunshouye"
                  alt="脉轮测试"
                  class="chakra-main-image"
                />
                <div v-else class="chakra-circles">
                  <div class="chakra-circle" style="background: #993734;" title="海底轮"></div>
                  <div class="chakra-circle" style="background: #be6f2a;" title="脐轮"></div>
                  <div class="chakra-circle" style="background: #d7c34a;" title="太阳轮"></div>
                  <div class="chakra-circle" style="background: #5f9057;" title="心轮"></div>
                  <div class="chakra-circle" style="background: #5b8aa4;" title="喉轮"></div>
                  <div class="chakra-circle" style="background: #2c3485;" title="眉心轮"></div>
                  <div class="chakra-circle" style="background: #7e4997;" title="顶轮"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- 开始测试部分 -->
        <section class="start-test-section">
          <div class="start-test-content">
            <h3>准备好开始您的脉轮之旅了吗？</h3>
            <p>点击下方按钮开始专业的脉轮能量测试</p>
            <button
              class="crystal-btn-large start-test-btn"
              @click="startTest"
              :disabled="loading"
            >
              <van-icon name="play-circle-o" size="20" color="white" />
              {{ loading ? '正在准备测试...' : '开始我的脉轮测试' }}
            </button>
          </div>
        </section>

        <!-- 快捷导航部分 -->
        <section class="quick-nav-section">
          <h3 class="section-title">探索更多</h3>
          <div class="nav-cards">
            <div class="nav-card" @click="goToList">
              <div class="nav-card-icon">
                <van-icon name="notes-o" size="30" />
              </div>
              <h4>我的测试</h4>
              <p>查看历史测试记录和结果</p>
            </div>

            <div class="nav-card" @click="goToIntro">
              <div class="nav-card-icon">
                <van-icon name="info-o" size="30" />
              </div>
              <h4>脉轮简介</h4>
              <p>了解七个脉轮的详细知识</p>
            </div>

            <div class="nav-card" @click="goToBalance">
              <div class="nav-card-icon">
                <van-icon name="balance-list-o" size="30" />
              </div>
              <h4>平衡脉轮</h4>
              <p>学习脉轮平衡的方法和技巧</p>
            </div>
          </div>
        </section>
      </div>
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/common/Layout.vue'
import { isMobilePhone } from '@/utils/index'
import Message from '@/utils/message'

export default {
  components: {
    Layout
  },
  name: 'ChakraTest',
  data() {
    return {
      mailunshouye: '',
      loading: false,
      isMobilePhone: isMobilePhone()
    }
  },
  mounted() {
    this.getConfig()
  },
  methods: {
    // 获取配置信息
    getConfig() {
      this.getRequest('/mailunConfig').then(res => {
        if (res.code == 200) {
          this.mailunshouye = res.data.mailunshouye
        }
      }).catch(err => {
        console.error('获取配置失败:', err)
      })
    },

    // 开始测试
    startTest() {
      this.loading = true
      this.getRequest('/question/startExam').then(res => {
        this.loading = false
        if (res.code == 200) {
          // 跳转到答题页面
          this.$router.push({
            path: '/chakra-test/start',
            query: {
              questionUserId: res.data.questionUserId,
              token: res.data.token
            }
          })
        } else {
          Message.error(res.message || '开始测试失败')
        }
      }).catch(err => {
        this.loading = false
        Message.error('开始测试失败')
        console.error('开始测试失败:', err)
      })
    },
    
    // 跳转到测试列表
    goToList() {
      this.$router.push('/chakra-test/list')
    },
    
    // 跳转到脉轮简介
    goToIntro() {
      this.$router.push('/chakra-test/intro')
    },
    
    // 跳转到平衡脉轮
    goToBalance() {
      this.$router.push('/chakra-test/balance')
    }
  }
}
</script>

<style scoped>
.chakra-test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 页面头部样式 */
.hero-header-section {
  background: linear-gradient(135deg, #564680 0%, #516790 50%, #c9ab79 100%);
  color: white;
  text-align: center;
  padding: 100px 20px;
  position: relative;
  overflow: hidden;
  border-radius: 0 0 50px 50px;
}

.chakra-header {
  background: linear-gradient(135deg, #564680 0%, #516790 50%, #c9ab79 100%);
  box-shadow: 0 10px 30px rgba(86, 70, 128, 0.3);
}

.hero-header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="3" fill="rgba(255,255,255,0.15)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1.5" fill="rgba(255,255,255,0.12)"/><circle cx="60" cy="30" r="2.5" fill="rgba(255,255,255,0.08)"/></svg>');
  animation: float 30s infinite linear;
}

@keyframes float {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 25px;
  text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
  letter-spacing: 2px;
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-title .van-icon {
  margin-right: 15px;
  animation: rotate 2s linear infinite;
}

.rotating-icon {
  display: inline-block;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.hero-title i {
  margin-right: 15px;
  color: rgba(255,255,255,0.9);
}

.hero-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

/* 内容包装器 */
.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 脉轮介绍部分 */
.chakra-intro-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  position: relative;
}

.chakra-intro-section::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  transform: skewY(-2deg);
  z-index: 1;
}

.intro-content {
  display: flex;
  align-items: center;
  gap: 80px;
  position: relative;
  z-index: 2;
}

.intro-text {
  flex: 1;
  min-width: 300px;
}

.section-title {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 30px;
  font-weight: 600;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 0;
  width: 80px;
  height: 5px;
  background: linear-gradient(90deg, #564680, #516790, #c9ab79);
  border-radius: 3px;
  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);
}

.intro-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin-bottom: 20px;
}

.test-features {
  display: flex;
  gap: 30px;
  margin-top: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  color: #666;
}

.feature-item .van-icon {
  margin-right: 8px;
}

.feature-item i {
  color: #c9ab79;
  font-size: 1.2rem;
}

.intro-visual {
  flex: 0 0 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chakra-circles-container {
  text-align: center;
}

.chakra-main-image {
  max-width: 100%;
  height: auto;
  border-radius: 25px;
  box-shadow: 0 20px 50px rgba(86, 70, 128, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chakra-main-image:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 30px 70px rgba(86, 70, 128, 0.3);
}

.chakra-circles {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.chakra-circle {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  animation: chakra-pulse 4s infinite;
  cursor: pointer;
  transition: all 0.4s ease;
  position: relative;
}

.chakra-circle::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chakra-circle:hover {
  transform: scale(1.3) rotate(15deg);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.chakra-circle:hover::before {
  opacity: 1;
}

@keyframes chakra-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 开始测试部分 */
.start-test-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  text-align: center;
  position: relative;
}

.start-test-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(86, 70, 128, 0.1), transparent);
  border-radius: 50%;
  z-index: 1;
}

.start-test-content {
  position: relative;
  z-index: 2;
}

.start-test-content h3 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 20px;
  font-weight: 600;
}

.start-test-content p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 40px;
}

.start-test-btn {
  background: linear-gradient(135deg, #564680, #516790, #c9ab79);
  border: none;
  color: white;
  padding: 20px 50px;
  font-size: 1.2rem;
  font-weight: 700;
  border-radius: 60px;
  cursor: pointer;
  transition: all 0.4s ease;
  box-shadow: 0 10px 25px rgba(86, 70, 128, 0.4);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.start-test-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s ease;
}

.start-test-btn:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 40px rgba(86, 70, 128, 0.5);
  background: linear-gradient(135deg, #516790, #c9ab79, #564680);
}

.start-test-btn:hover::before {
  left: 100%;
}

.start-test-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.start-test-btn .van-icon {
  margin-right: 10px;
}

.start-test-btn i {
  margin-right: 10px;
}

/* 快捷导航部分 */
.quick-nav-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  text-align: center;
  position: relative;
}

.quick-nav-section::before {
  content: '';
  position: absolute;
  top: -100px;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  transform: skewY(2deg);
  z-index: 1;
}

.quick-nav-section .section-title,
.nav-cards {
  position: relative;
  z-index: 2;
}

.quick-nav-section .section-title {
  text-align: center;
  margin-bottom: 50px;
}

.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.nav-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 50px 35px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(86, 70, 128, 0.1);
  transition: all 0.4s ease;
  cursor: pointer;
  border: 3px solid transparent;
  position: relative;
  overflow: hidden;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(86, 70, 128, 0.05), transparent, rgba(201, 171, 121, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 50px rgba(86, 70, 128, 0.2);
  border-color: #564680;
}

.nav-card:hover::before {
  opacity: 1;
}

.nav-card-icon {
  width: 90px;
  height: 90px;
  background: linear-gradient(135deg, #564680, #516790, #c9ab79);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
  color: white;
  box-shadow: 0 8px 20px rgba(86, 70, 128, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.nav-card-icon .van-icon {
  color: white;
}

.nav-card:hover .nav-card-icon {
  transform: rotate(360deg) scale(1.1);
  box-shadow: 0 12px 30px rgba(86, 70, 128, 0.4);
}

.nav-card h4 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.nav-card p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .hero-header-section {
    padding: 80px 20px;
    border-radius: 0 0 30px 30px;
  }
  
  .hero-title {
    font-size: 2.5rem;
    letter-spacing: 1px;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .intro-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }

  .intro-visual {
    flex: none;
  }

  .test-features {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .nav-cards {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .content-wrapper {
    padding: 0 15px;
  }

  .chakra-intro-section,
  .start-test-section,
  .quick-nav-section {
    padding: 60px 0;
  }
  
  .start-test-btn {
    padding: 18px 40px;
    font-size: 1.1rem;
  }
  
  .nav-card {
    padding: 35px 25px;
  }
  
  .nav-card-icon {
    width: 70px;
    height: 70px;
  }
  
  .nav-card-icon .van-icon {
    font-size: 26px;
  }
}

@media (max-width: 480px) {
  .hero-header-section {
    padding: 60px 15px;
    border-radius: 0 0 20px 20px;
  }

  .hero-title {
    font-size: 2rem;
    letter-spacing: 0.5px;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .nav-card {
    padding: 30px 20px;
    border-radius: 15px;
  }

  .nav-card-icon {
    width: 65px;
    height: 65px;
  }
  
  .nav-card-icon .van-icon {
    font-size: 24px;
  }
  
  .start-test-btn {
    padding: 15px 35px;
    font-size: 1rem;
    border-radius: 40px;
  }
  
  .chakra-circle {
    width: 35px;
    height: 35px;
  }
}
</style>
