package com.crystal.admin.controller;

import java.util.Arrays;
import java.util.Map;

import com.crystal.common.model.user.UserBraceletsEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.request.UserRechargeSearchRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.UserBraceletsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;




/**
 * 用户手串 控制器
 * | Author: 陈佳音
 * ｜ @date Fri Nov 22 16:43:13 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("userbracelets")
public class UserBraceletsController {
    @Autowired
    private UserBraceletsService userBraceletsService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('userbracelets:list')")
    public CommonResult<CommonPage<UserBraceletsEntity>> list(@Validated UserBraceletsEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<UserBraceletsEntity> page = CommonPage.restPage(userBraceletsService.queryPage(request,pageParamRequest));

        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('userbracelets:info')")
    public CommonResult<UserBraceletsEntity> info(@PathVariable("id") Long id){
		UserBraceletsEntity userBracelets = userBraceletsService.getById(id);

        return CommonResult.success(userBracelets);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('userbracelets:save')")
    public CommonResult<String> save(@RequestBody UserBraceletsEntity userBracelets){
        if (userBraceletsService.save(userBracelets)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('userbracelets:update')")
    public CommonResult<String> update(@RequestBody UserBraceletsEntity userBracelets){
        if (userBraceletsService.updateById(userBracelets)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('userbracelets:delete')")
    public CommonResult<String> delete(@RequestBody Integer[] ids){
        if (userBraceletsService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
