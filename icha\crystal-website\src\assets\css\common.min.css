

/*================================ Header ================================*/
.header-wrapper{
    display: flex;
    flex-direction: column;
}

.header-wrapper .header{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-around;
    padding: 15px 0;
    max-width: 1240px;
    margin: 0 auto;
    width: 100%;
    /*background-color: yellow;*/
}

.header-wrapper .header .header-left{
    height: 60px;
}
.header-wrapper .header .header-left img{
    height: 100%;
}
.header-wrapper .header .header-mid{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin: 0px 5px;
}
.header-wrapper .header .header-mid .header-item{
    display: flex;
    flex-direction: row;
    align-items: center;
}
.header-wrapper .header .header-mid .header-item i{
    font-size: 30px;
}
.header-wrapper .header .header-mid .header-item .item{
    display: flex;
    flex-direction: column;
    margin-left: 14px;
}
.header-wrapper .header .header-right{
    padding: 10px 0;
}
.header-wrapper .header .header-right .button1{
    padding: 8px 16px;
    border: 1px solid #564680;
    border-radius: 4px;
    color: #564680;
    background-color: #fff;
}
.header-wrapper .header .header-right .button1:hover {
    background-color: #564680;
    color: #ffffff;
}
.header-wrapper .header .header-right .button2{
    padding: 8px 16px;
    background-color: #516790;
    color: #ffffff;
    border-radius: 4px;
    border: none;
}
.header-wrapper .header .header-right .button2:hover {
    border: 1px solid #516790;
    color: #516790;
    background-color: #fff;
}

/* header固定 */
.sticky{
    background-color: #ffffff;
    position: fixed;
    z-index: 9999;
    top: 0;
    left: 0;
    width: 100%;
    animation: headerSticky .95s ease forwards;
}


/*================================ Nav ================================*/
.nav-wrapper{
    width: 100%;
    border-top: 1px solid #e9e9e9;
    box-shadow: 0 1px 3px #e9e9e9;
}
.nav{
    max-width: 1240px;
    margin: 0 auto;
}

.nav .am-nav li {
    padding: 25px 0px;
    list-style: none;
    position: relative;
}

.nav .am-nav li a {
    color: #333;
    padding: 0;
    margin: 0;
}

.nav .am-nav li a:hover {
    color: #6975a6;
    background: #fff
}

@font-face {
    font-family: fontawesome;
    src: url(../fonts/fontawesome-webfont.ttf)
}



/*================================ Nav ================================*/





.page-header {
    text-align: center;
    padding: 80px 0 92px;
    font-weight: 300;
    background: url(../images/pattern-dark.png) #383d61
}

.page-header .page-header-title {
    font-size: 60px;
    color: #fff;
    font-weight: 400
}

.breadcrumb-box {
    border-bottom: 1px solid #e9e9e9
}

.breadcrumb-box .am-breadcrumb {
    padding: 16px 0 0;
    margin-bottom: 1rem
}

.breadcrumb-box .am-breadcrumb li a {
    font-size: 14px;
    color: #262626
}

.breadcrumb-box .am-breadcrumb .am-active {
    color: #6975a6
}

.section {
    padding: 100px 0
}

.section .section--header .section--title-nocolor {
    font-size: 50px;
    font-weight: 600;
    text-align: center;
}

.section .section--header .section--description {
    font-size: 20px;
    line-height: 30px;
    color: #9b9b9b;
    text-align: center;
    margin: 13px 0 0
}

@media screen and (max-width: 640px) {
    .section {
        padding: 50px 0 !important
    }

    .section .section--header {
        padding: 0 15px
    }

    .section .section--header .section--title {
        font-size: 30px !important
    }

    .section .section--header .section--description {
        font-size: 15px;
        line-height: 32px
    }

    .section--description {
        font-size: 16px
    }
}



/*================================ Footer ================================*/
.footer {
    width: 100%;
    background: url(../images/footer.jpg);
    background-size: cover;
    position: relative
}

.footer .footer--bg {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: .95;
    top: 0;
    left: 0
}

.footer .footer--inner {
    position: relative;
    z-index: 1;
    width: 100%;

}
.footer .footer--inner .am-g{
    max-width: 1140px;
}

.footer .footer_main--column {
    margin: 50px 0 0 10px
}

 .footer .footer_main--column_title {
    color: #fff;
    font-size: 17px;
    line-height: 29px;
    font-weight: 700;
    display: block
}

 .footer .footer_about {
    position: relative;
    padding: 17px 25px 0 0
}

 .footer .footer_about--text {
    font-size: 15px;
    line-height: 25px;
    color: #fff;
    padding: 0 0 16px
}

 .footer .footer_navigation {
    padding: 6px 15px 0 0;
}

 .footer .footer_navigation--item {
    display: block;
    position: relative;
    padding: 12px 25px 12px 0;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, .1)
}

 .footer .footer_navigation--item::before {
    position: absolute;
    content: "\f054";
    display: block;
    right: 10px;
    top: 50%;
    transform: translate(0, -50%);
    margin-top: 3px;
    color: #fff;
    font-size: 12px;
    font-family: fontawesome
}

 .footer .footer_navigation--link {
    color: #fff;
    font-size: 15px;
    line-height: 22px
}

 .footer .footer_navigation--link:hover {
    text-decoration: underline
}

 .footer .footer_contact_info {
    padding: 5px 0 0
}

 .footer .footer_contact_info--item {
    padding: 10px 0;
    position: relative;
    color: #fff;
    font-size: 15px;
    line-height: 25px;
    list-style: none
}

 .footer .footer_contact_info--item i {
    display: block;
    position: absolute;
    left: -2px;
    top: 12px
}

 .footer .footer_contact_info--item span {
    display: inline-block;
    vertical-align: text-top;
    padding: 0 0 0 28px
}



.contact_card {
    background: #fff;
    border: 1px solid #e9e9e9;
    border-radius: 3px;
    position: relative;
    text-align: center;
    padding: 138px 0 48px;
    margin: 30px
}

.contact_card .contact_card--icon {
    position: absolute;
    font-size: 56px;
    display: block;
    top: 37px;
    left: 50%;
    transform: translate(-50%, 0);
    color: #564680
}

.contact_card .contact_card--title {
    font-size: 20px;
    display: inline-block
}

.contact_card .contact_card--text {
    padding: 0 0 10px;
    color: #969696;
    font-size: 16px;
    line-height: 23px
}

.contact_card .contact_card--text a {
    color: #969696
}

.contact_card .am-btn-secondary {
    background-color: transparent;
    color: #564680;
    border-color: #564680;
    border-width: 2px;
    padding: 12px 18px
}

.contact_card .am-btn-secondary:hover {
    background-color: #564680;
    color: #fff
}
