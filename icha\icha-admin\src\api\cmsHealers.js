import request from '@/utils/request'

/**
 * 新增疗愈师
 * @param data
 */
export function cmsHealersCreateApi(data) {
    return request({
        url: '/admin/cms/healers/save',
        method: 'POST',
        data
    })
}

/**
 * 疗愈师更新
 * @param data
 */
export function cmsHealersUpdateApi(data) {
    return request({
        url: '/admin/cms/healers/update',
        method: 'POST',
        data
    })
}

/**
 * 疗愈师详情
 * @param id
 */
export function cmsHealersDetailApi(id) {
    return request({
        url: `/admin/cms/healers/info/${id}`,
        method: 'GET'
    })
}

/**
 * 疗愈师删除
 * @param ids 要删除的id数组
 */
export function cmsHealersDeleteApi(ids) {
    return request({
        url: `/admin/cms/healers/delete`,
        method: 'POST',
        data: ids
    })
}

/**
 * 疗愈师列表
 * @param params
 */
export function cmsHealersListApi(params) {
    return request({
        url: '/admin/cms/healers/list',
        method: 'GET',
        params
    })
} 