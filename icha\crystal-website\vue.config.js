module.exports = {
  pages: {
    index: {
      entry: 'src/main.js',
    }
  },
  publicPath: './',
  lintOnSave: false, // 关闭语法检查
  devServer: {
    port: 1089,
    proxy: {
			'/': {
				// target: 'http://127.0.0.1:8026/',
				target: 'http://icha.work/', // 正式环境
        "ws": false
      },
    },
  },
  configureWebpack: {
    devServer: {
      historyApiFallback: true,
      allowedHosts: "all"
    },
    externals: {
      vue: "Vue",
      "vue-router": "Router",
    }
  },

}

