<template>
  <Layout>
    <div class="chakra-balance-page">
    <!-- 标题栏 -->
    <div class="nav-title">
      <div class="nav-left">
        <van-button 
          class="back-button"
          @click="goBack"
          plain
        >
          <van-icon name="arrow-left" size="18" />
        </van-button>
        <div class="color-bar"></div>
        <div class="title-text">平衡脉轮</div>
      </div>
      <div class="nav-right">
        <van-button 
          class="test-button-small"
          @click="goToTest"
          size="small"
        >
          <van-icon name="play" size="14" />
          <span>开始测试</span>
        </van-button>
        <van-button 
          class="intro-button-small"
          @click="goToIntro"
          size="small"
          type="primary"
        >
          <van-icon name="info-o" size="14" />
          <span>脉轮简介</span>
        </van-button>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-container">
      <div class="intro-section">
        <h3 class="section-title">脉轮平衡的重要性</h3>
        <p class="intro-text">
          脉轮是人体能量系统的重要组成部分，当脉轮平衡时，我们的身心灵都会处于和谐状态。
          通过冥想、瑜伽、水晶疗愈等方法，可以帮助我们平衡和激活脉轮能量。
        </p>
      </div>
      
      <div class="methods-section">
        <h3 class="section-title">平衡脉轮的方法</h3>
        
        <div class="method-card" v-for="(method, index) in balanceMethods" :key="index">
          <div class="method-header">
            <van-icon :name="method.icon" size="24" :color="method.color" />
            <h4 class="method-name">{{ method.name }}</h4>
          </div>
          <div class="method-description">
            <p>{{ method.description }}</p>
          </div>
          <div class="method-steps">
            <h5>具体步骤：</h5>
            <ul>
              <li v-for="(step, stepIndex) in method.steps" :key="stepIndex">
                {{ step }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <div class="tips-section">
        <h3 class="section-title">平衡脉轮的小贴士</h3>
        <div class="tips-list">
          <div class="tip-item" v-for="(tip, index) in tips" :key="index">
            <van-icon name="info-o" size="16" color="#c9ab79" />
            <span>{{ tip }}</span>
          </div>
        </div>
      </div>
    </div>
    
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/common/Layout.vue'

export default {
  components: {
    Layout
  },
  name: 'ChakraTestBalance',
  data() {
    return {
      balanceMethods: [
        {
          name: '冥想练习',
          icon: 'smile-o',
          color: '#c9ab79',
          description: '通过专注的冥想练习，可以帮助我们感知和平衡脉轮能量。',
          steps: [
            '找一个安静舒适的地方坐下',
            '闭上眼睛，深呼吸几次放松身心',
            '将注意力集中在需要平衡的脉轮位置',
            '想象该脉轮发出温暖的光芒',
            '保持专注15-20分钟'
          ]
        },
        {
          name: '瑜伽体式',
          icon: 'user-o',
          color: '#5f9057',
          description: '特定的瑜伽体式可以帮助激活和平衡不同的脉轮。',
          steps: [
            '选择针对特定脉轮的瑜伽体式',
            '在练习前进行热身运动',
            '保持正确的呼吸节奏',
            '在体式中保持专注和觉知',
            '练习后进行放松和整合'
          ]
        },
        {
          name: '水晶疗愈',
          icon: 'diamond-o',
          color: '#2c3485',
          description: '不同颜色的水晶对应不同的脉轮，可以帮助平衡脉轮能量。',
          steps: [
            '选择与脉轮颜色对应的水晶',
            '清洁和净化水晶能量',
            '将水晶放在对应的脉轮位置',
            '静躺15-30分钟感受能量',
            '结束后感谢水晶的帮助'
          ]
        },
        {
          name: '声音疗愈',
          icon: 'music-o',
          color: '#5b8aa4',
          description: '通过特定的音频、咒语或唱诵来平衡脉轮。',
          steps: [
            '选择对应脉轮的音频或咒语',
            '找一个不被打扰的环境',
            '专注聆听或跟随唱诵',
            '感受声音在身体中的振动',
            '让声音的能量流遍全身'
          ]
        }
      ],
      tips: [
        '每天花10-15分钟进行脉轮平衡练习',
        '保持规律的练习比偶尔长时间练习更有效',
        '在练习前后多喝水帮助能量流动',
        '注意身体的感受，不要强迫任何体验',
        '结合健康的生活方式效果更佳',
        '如有不适请停止练习并咨询专业人士'
      ]
    }
  },
  methods: {
    goToTest() {
      this.$router.push('/chakra-test')
    },
    
    goToIntro() {
      this.$router.push('/chakra-test/intro')
    },
    
    // 返回上一页
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.chakra-balance-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.nav-title {
  padding: 15px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 4px 15px rgba(86, 70, 128, 0.1);
  margin-bottom: 20px;
  border-radius: 0 0 20px 20px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(86, 70, 128, 0.08);
  border: 1px solid rgba(86, 70, 128, 0.2);
  color: #564680;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(86, 70, 128, 0.15);
  border-color: rgba(86, 70, 128, 0.4);
  transform: translateX(-2px);
}

.color-bar {
  width: 6px;
  height: 25px;
  background: linear-gradient(135deg, #564680, #516790, #c9ab79);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(86, 70, 128, 0.3);
}

.title-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  letter-spacing: 0.5px;
}

.test-button-small {
  background: linear-gradient(135deg, #564680, #516790);
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  color: white;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);
  transition: all 0.3s ease;
}

.test-button-small:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);
}

.test-button-small span {
  margin-left: 4px;
}

.intro-button-small {
  background: linear-gradient(135deg, #c9ab79, #b8996a);
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  color: white;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(201, 171, 121, 0.3);
  transition: all 0.3s ease;
}

.intro-button-small:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(201, 171, 121, 0.4);
}

.intro-button-small span {
  margin-left: 4px;
}

.content-container {
  padding: 0 20px 20px;
}

.intro-section, .methods-section, .tips-section {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 8px 25px rgba(86, 70, 128, 0.08);
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
}

.intro-section::before, .methods-section::before, .tips-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #564680, #516790, #c9ab79);
  border-radius: 16px 16px 0 0;
}

.intro-section:hover, .methods-section:hover, .tips-section:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(86, 70, 128, 0.12);
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin: 0 0 20px 0;
  padding-bottom: 15px;
  border-bottom: 3px solid transparent;
  background: linear-gradient(90deg, #564680, #516790, #c9ab79) bottom;
  background-size: 80px 3px;
  background-repeat: no-repeat;
  position: relative;
  letter-spacing: 0.5px;
}

.intro-text {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

.method-card {
  border: 2px solid rgba(86, 70, 128, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  transition: all 0.3s ease;
  position: relative;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(86, 70, 128, 0.1);
    border-color: rgba(86, 70, 128, 0.2);
  }
}

.method-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(86, 70, 128, 0.1);
}

.method-name {
  font-size: 16px;
  font-weight: 700;
  color: #333;
  margin: 0 0 0 10px;
  letter-spacing: 0.3px;
}

.method-description {
  margin-bottom: 15px;
  
  p {
    font-size: 14px;
    line-height: 1.5;
    color: #555;
    margin: 0;
  }
}

.method-steps {
  h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      font-size: 13px;
      line-height: 1.5;
      color: #666;
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.tips-list {
  .tip-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;
    color: #555;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    span {
      margin-left: 8px;
    }
  }
}


/* 移动端适配 */
@media (max-width: 768px) {
  .intro-section, .methods-section, .tips-section {
    padding: 20px;
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .method-card {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .section-title {
    font-size: 16px;
  }
  
  .method-name {
    font-size: 15px;
  }
  
  .nav-title {
    padding: 12px 20px;
    height: 55px;
  }
  
  .nav-left {
    gap: 8px;
  }
  
  .nav-right {
    gap: 6px;
  }
  
  .back-button {
    width: 36px;
    height: 36px;
  }
  
  .title-text {
    font-size: 18px;
  }
  
  .test-button-small,
  .intro-button-small {
    padding: 5px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .nav-title {
    padding: 10px 15px;
    height: 50px;
  }
  
  .nav-left {
    gap: 6px;
  }
  
  .nav-right {
    gap: 4px;
  }
  
  .back-button {
    width: 32px;
    height: 32px;
  }
  
  .color-bar {
    width: 4px;
    height: 20px;
  }
  
  .title-text {
    font-size: 16px;
  }
  
  .test-button-small,
  .intro-button-small {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .content-container {
    padding: 0 15px 20px;
  }
  
  .intro-section, .methods-section, .tips-section {
    padding: 16px;
  }
  
  .method-card {
    padding: 14px;
  }
  
  .section-title {
    font-size: 15px;
    background-size: 60px 3px;
  }
  
  .method-name {
    font-size: 14px;
  }
  
  .method-description p {
    font-size: 13px;
  }
  
  .method-steps {
    h5 {
      font-size: 13px;
    }
    
    ul li {
      font-size: 12px;
    }
  }
  
  .tip-item {
    font-size: 13px;
  }
}
</style>
