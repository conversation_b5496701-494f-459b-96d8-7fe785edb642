
import request from '@/utils/request'

/**
 * 新增QuestionUser
 * @param pram
 */
export function QuestionUserCreateApi(data) {
    return request({
        url: 'questionuser/save',
        method: 'POST',
        data
    })
}

/**
 * questionuser更新
 * @param pram
 */
export function questionuserUpdateApi(data) {
    return request({
        url: 'questionuser/update',
        method: 'POST',
        data
    })
}

/**
 * questionuser详情
 * @param pram
 */
export function questionuserDetailApi(id) {
    return request({
        url: `questionuser/info/${id}`,
        method: 'GET'
    })
}

/**
 * questionuser删除
 * @param pram
 */
export function questionuserDeleteApi(id) {
    return request({
        url: `questionuser/delete/${id}`,
        method: 'get'
    })
}


/**
 * questionuser列表
 * @param pram
 */
export function questionuserListApi(params) {
    return request({
        url: 'questionuser/list',
        method: 'GET',
        params
    })
}

