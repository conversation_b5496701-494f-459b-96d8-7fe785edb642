import request from '@/utils/request'

/**
 * 获取证书列表
 * @param {Object} params 查询参数
 */
export function cmsCertificateListApi(params) {
    return request({
        url: '/admin/cms/certificate/list',
        method: 'get',
        params
    })
}

/**
 * 获取证书详情
 * @param {Number} id 证书ID
 */
export function cmsCertificateInfoApi(id) {
    return request({
        url: `/admin/cms/certificate/info/${id}`,
        method: 'get'
    })
}

/**
 * 新增证书
 * @param {Object} data 证书信息
 */
export function cmsCertificateAddApi(data) {
    return request({
        url: '/admin/cms/certificate/save',
        method: 'post',
        data
    })
}

/**
 * 更新证书
 * @param {Object} data 证书信息
 */
export function cmsCertificateUpdateApi(data) {
    return request({
        url: '/admin/cms/certificate/update',
        method: 'post',
        data
    })
}

/**
 * 删除证书
 * @param {Array} ids 证书ID数组
 */
export function cmsCertificateDeleteApi(ids) {
    return request({
        url: '/admin/cms/certificate/delete',
        method: 'post',
        data: ids
    })
}

/**
 * 更新证书状态
 * @param {Object} data 包含id和状态信息
 */
export function cmsCertificateUpdateStatusApi(data) {
    return request({
        url: '/admin/cms/certificate/status',
        method: 'post',
        data
    })
} 