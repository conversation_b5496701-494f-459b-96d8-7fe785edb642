package com.crystal.admin.controller;

import com.crystal.common.model.question.QuestionUserEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.QuestionUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;


/**
 * 用户手串 控制器
 * | Author: 陈佳音
 * ｜ @date Fri Nov 22 16:43:13 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/questionuser")
public class QuestionUserController {
    @Autowired
    private QuestionUserService questionUserService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('userbraceletsitem:list')")
    public CommonResult<CommonPage<QuestionUserEntity>> list(@Validated QuestionUserEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<QuestionUserEntity> page = CommonPage.restPage(questionUserService.queryPage(request,pageParamRequest));

        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('userbraceletsitem:info')")
    public CommonResult<QuestionUserEntity> info(@PathVariable("id") Integer id){
		QuestionUserEntity questionUser = questionUserService.getById(id);

        return CommonResult.success(questionUser);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('userbraceletsitem:save')")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> save(@RequestBody QuestionUserEntity questionUser){
        questionUser.setAddTime(new Date());
        if (questionUserService.save(questionUser)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
    @Transactional(rollbackFor = Exception.class)
//    @PreAuthorize("hasAuthority('userbraceletsitem:update')")
    public CommonResult<String> update(@RequestBody QuestionUserEntity questionUser){
        questionUser.setUpdateTime(new Date());
        if (questionUserService.updateById(questionUser)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('userbraceletsitem:delete')")
    public CommonResult<String> delete(@RequestBody Integer[] ids){
        if (questionUserService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
