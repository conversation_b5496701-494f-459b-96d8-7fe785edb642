
import request from '@/utils/request'

/**
 * 新增UserBracelets
 * @param pram
 */
export function UserBraceletsCreateApi(data) {
    return request({
        url: 'userbracelets/save',
        method: 'POST',
        data
    })
}

/**
 * userbracelets更新
 * @param pram
 */
export function userbraceletsUpdateApi(data) {
    return request({
        url: 'userbracelets/update',
        method: 'POST',
        data
    })
}

/**
 * userbracelets详情
 * @param pram
 */
export function userbraceletsDetailApi(id) {
    return request({
        url: `userbracelets/info/${id}`,
        method: 'GET'
    })
}

/**
 * userbracelets删除
 * @param pram
 */
export function userbraceletsDeleteApi(id) {
    return request({
        url: `userbracelets/delete/${id}`,
        method: 'get'
    })
}


/**
 * userbracelets列表
 * @param pram
 */
export function userbraceletsListApi(params) {
    return request({
        url: 'userbracelets/list',
        method: 'GET',
        params
    })
}

