<template>
	<Layout>
		<div class="layout-container" style="width: 100%">
			<!-- 美化后的页面头部 -->
			<div class="hero-header-section workshop-header">
				<div class="hero-content">
					<h1 class="hero-title"><i class="fa fa-diamond fa-spin-pulse"></i> 工作坊</h1>
					<p class="hero-subtitle">专业体验，深入感知水晶能量的奇妙魅力</p>
					<button class="my-enroll-btn" @click="showMyEnrolls"><i class="fa fa-list-ul"></i> 我的报名</button>
				</div>
			</div>
		</div>
		<!-- 搜索区域 -->
		<div class="section">
			<div class="container" style="max-width: 1160px">
				<div class="workshop-search">
					<div class="search-container">
						<input type="text" v-model="searchKeyword" placeholder="搜索工作坊名称或关键词" class="search-input" />
						<button class="search-btn" @click="searchWorkshops">搜索</button>
					</div>
				</div>

				<!-- 工作坊列表 -->
				<div class="crystal-workshop-container">
					<div class="crystal-grid" :class="{ 'crystal-grid-mobile': isMobilePhone }">
						<div v-for="(workshop, index) in workshopList" :key="index" class="crystal-workshop-card">
							<div class="crystal-workshop-img">
								<img :src="workshop.avatar" alt="">
								<div class="workshop-overlay"></div>
							</div>
							<div class="crystal-workshop-info">
								<h3>{{ workshop.title }}</h3>
								<p>{{ workshop.brief }}</p>
								<div class="crystal-workshop-meta">
									<!-- <span><i class="fa fa-calendar"></i> {{workshop.startTime}}</span> -->
									<span><i class="fa fa-map-marker"></i> {{ workshop.location }}</span>
									<!-- <span><i class="fa fa-users"></i> 剩余名额: {{workshop.number - workshop.readyNumber}}</span> -->
								</div>
								<div class="crystal-workshop-footer">
									<!-- <span class="crystal-workshop-price">¥{{workshop.price}}元</span> -->
									<span class="crystal-workshop-price">剩余名额: {{ workshop.number -
										workshop.readyNumber}}</span>
									<button class="crystal-btn pulse-btn"
										@click="showEnrollForm(workshop)">立即报名</button>
								</div>
							</div>
						</div>
					</div>

					<!-- 无数据提示 -->
					<div v-if="workshopList.length == 0" class="no-data">
						<p>暂无工作坊数据</p>
					</div>

					<!-- 分页 -->
					<ul class="am-pagination" style="text-align: center;" v-if="total > 0">
						<li :class="pageIndex == 1 ? 'am-disabled' : ''" @click="changeIndex(pageIndex - 1)">
							<a href="javascript:void(0);">&laquo;</a>
						</li>

						<li v-for="p in totalPage" :key="p" @click="changeIndex(p)"
							:class="pageIndex == p ? 'am-active' : ''">
							<a href="javascript:void(0);">{{ p }}</a>
						</li>

						<li :class="pageIndex == totalPage ? 'am-disabled' : ''" @click="changeIndex(pageIndex + 1)">
							<a href="javascript:void(0);">&raquo;</a>
						</li>
					</ul>
				</div>
			</div>
		</div>

		<!-- 报名表单弹窗 -->
		<van-popup v-model="showEnrollPopup" round closeable close-icon="close" position="center"
			:style="{ width: isMobilePhone ? '90%' : '500px' }">
			<div class="enroll-form">
				<h2 class="enroll-title">{{ currentWorkshop.title }} - 报名表</h2>
				<van-form @submit="submitEnrollForm">
					<van-field v-model="enrollForm.name" name="name" label="姓名" placeholder="请输入姓名"
						:rules="[{ required: true, message: '请填写姓名' }]" />
					<van-field v-model="enrollForm.phone" name="phone" label="手机号码" placeholder="请输入手机号码" :rules="[
						{ required: true, message: '请填写手机号码' },
						{ validator: validatePhone, message: '手机号码格式不正确' }
					]" />
					<van-field name="count" label="报名人数">
						<template #input>
							<van-stepper v-model="enrollForm.number" min="1"
								:max="currentWorkshop.number - currentWorkshop.readyNumber" />
						</template>
					</van-field>
					<van-field v-model="enrollForm.message" name="message" label="留言" type="textarea"
						placeholder="有什么想告诉我们的？" rows="2" autosize />
					<div style="margin: 16px;">
						<van-button round block type="primary" native-type="submit" :loading="submitting">
							提交报名
						</van-button>
					</div>
				</van-form>
			</div>
		</van-popup>

		<!-- 我的报名记录弹窗 -->
		<van-popup v-model="showMyEnrollPopup" round closeable close-icon="close" position="center"
			:style="{ width: isMobilePhone ? '90%' : '600px' }">
			<div class="my-enroll-list">
				<h2 class="enroll-title">我的工作坊报名记录</h2>
				
				<div v-if="myEnrollList.length > 0" class="enroll-records">
					<div v-for="(item, index) in myEnrollList" :key="index" class="enroll-record-item">
						<div class="record-workshop-info">
							<h3>{{ item.cmsWorkshopName }}</h3>
							<!-- 姓名，联系方式 -->
							<p><i class="fa fa-user"></i> 姓名: {{ item.name }}</p>
							<p><i class="fa fa-phone"></i> 联系方式: {{ item.phone }}</p>
							<p class="record-date"><i class="fa fa-calendar"></i> 报名时间: {{ formatDate(item.addTime) }}</p>
							<!-- <p><i class="fa fa-map-marker"></i> 地点: {{ item.workshop.location }}</p> -->
							<p><i class="fa fa-users"></i> 报名人数: {{ item.number }}人</p>
							<p v-if="item.message"><i class="fa fa-comment"></i> 留言: {{ item.message }}</p>
						</div>
					</div>
				</div>
				
				<div v-else class="no-data">
					<p>暂无报名记录</p>
				</div>
				
				<div class="popup-footer">
					<van-button round block type="primary" @click="showMyEnrollPopup = false">关闭</van-button>
				</div>
			</div>
		</van-popup>
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import '../assets/css/common-headers.css'; // 导入头部共用样式

export default {
	name: "WorkshopView",
	components: { Layout },
	data() {
		return {
			isMobilePhone: isMobilePhone(),
			workshopList: [],
			searchKeyword: '',
			pageIndex: 1,
			pageSize: 6,
			total: 0,
			totalPage: 1,
			// 报名相关
			showEnrollPopup: false,
			currentWorkshop: {},
			enrollForm: {
				name: '',
				phone: '',
				number: 1,
				message: ''
			},
			submitting: false,
			userInfo: {},
			// 我的报名相关
			showMyEnrollPopup: false,
			myEnrollList: []
		}
	},
	mounted() {
		this.$wxShare();
		this.getWorkshops();
	},
	methods: {
		// 获取工作坊列表
		getWorkshops() {
			this.getRequest("/cms/workshop/list", {
				'page': this.pageIndex,
				'limit': this.pageSize,
				'title': this.searchKeyword
			}).then(resp => {
				if (resp && resp.code == 200) {
					this.workshopList = resp.data.list || [];
					this.total = resp.data.total || 0;
					this.totalPage = resp.data.totalPage || 1;
				} else {
					this.workshopList = [];
					this.total = 0;
					this.totalPage = 1;
				}
			})
		},

		// 搜索工作坊
		searchWorkshops() {
			this.pageIndex = 1;
			this.getWorkshops();
		},

		// 切换页码
		changeIndex(index) {
			if (index < 1 || index > this.totalPage) {
				return;
			}
			this.pageIndex = index;
			this.getWorkshops();
		},

		// 显示报名表单
		showEnrollForm(workshop) {
			this.getRequest("/user").then(resp => {
				if (resp && resp.code == 200) {
					this.userInfo = resp.data;
					this.currentWorkshop = workshop;
					this.showEnrollPopup = true;
					// 重置表单
					this.enrollForm = {
						name: this.userInfo.nickname,
						phone: this.userInfo.phone,
						number: 1,
						message: ''
					};
					sessionStorage.setItem("userInfo", JSON.stringify(resp.data));
					localStorage.setItem("userInfo", JSON.stringify(resp.data));
				}
			})
		},

		// 验证手机号
		validatePhone(val) {
			return /^1[3-9]\d{9}$/.test(val);
		},

		// 验证邮箱
		validateEmail(val) {
			return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(val);
		},

		// 提交报名表单
		submitEnrollForm() {
			this.submitting = true;

			// 构造提交数据
			const formData = {
				cmsWorkshopId: this.currentWorkshop.id,
				...this.enrollForm
			};

			// 实际项目中替换为真实接口
			this.postRequest("/cmsAttend/workshop", formData).then(resp => {
			  this.submitting = false;
			  console.log(resp);
			  if (resp && resp.code == 200) {
			    this.$toast.success('报名成功！');
			    this.showEnrollPopup = false;
			    // 更新工作坊剩余名额
			    this.getWorkshops();
			  } else {
			    this.$toast.fail(resp.message || '报名失败，请稍后再试');
			  }
			});
		},

		// 显示我的报名记录
		showMyEnrolls() {
			this.getRequest("/cmsAttend/myList").then(resp => {
				if (resp && resp.code == 200) {
					this.myEnrollList = resp.data || [];
					this.showMyEnrollPopup = true;
				} else {
					this.$toast.fail(resp.message || '获取报名记录失败');
				}
			});
		},

		// 格式化日期
		formatDate(timestamp) {
			if (!timestamp) return '';
			const date = new Date(timestamp);
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
		},

		// 获取状态对应的类名
		getStatusClass(status) {
			switch (status) {
				case 0: return 'status-pending';
				case 1: return 'status-confirmed';
				case 2: return 'status-completed';
				case -1: return 'status-cancelled';
				default: return '';
			}
		},

		// 获取状态对应的文本
		getStatusText(status) {
			switch (status) {
				case 0: return '待确认';
				case 1: return '已确认';
				case 2: return '已完成';
				case -1: return '已取消';
				default: return '未知状态';
			}
		}
	}
}
</script>

<style scoped>
/* 美化后的页面头部样式 */
.workshop-header {
	background-image: url('https://img.freepik.com/free-photo/watercolor-pastel-texture-background_53876-98173.jpg') !important;
}

/* 我的报名按钮样式 */
.my-enroll-btn {
	padding: 10px 20px;
	background-color: #dc2430;
	color: white;
	border: none;
	border-radius: 25px;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	margin-top: 15px;
	transition: all 0.3s ease;
	box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.my-enroll-btn i {
	margin-right: 8px;
}

.my-enroll-btn:hover {
	background-color: rgba(86, 70, 128, 1);
	box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
	transform: translateY(-2px);
}

.my-enroll-btn:active {
	transform: translateY(0);
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 工作坊列表样式 */
.workshop-search {
	margin-bottom: 30px;
}

.search-container {
	display: flex;
	max-width: 600px;
	margin: 0 auto;
}

.search-input {
	flex: 1;
	padding: 12px 15px;
	border: 1px solid #ddd;
	border-radius: 4px 0 0 4px;
	font-size: 16px;
}

.search-btn {
	padding: 0 20px;
	background-color: #564680;
	border: none;
	color: white;
	border-radius: 0 4px 4px 0;
	cursor: pointer;
	font-size: 16px;
	transition: background-color 0.3s;
}

.search-btn:hover {
	background-color: #483c6f;
}

/* 我的报名记录样式 */
.my-enroll-list {
	padding: 20px;
}

.enroll-records {
	max-height: 60vh;
	overflow-y: auto;
	margin-bottom: 20px;
	padding-right: 10px;
}

.enroll-record-item {
	background-color: #f9f9f9;
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 15px;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
}

.enroll-record-item:hover {
	box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
	transform: translateY(-2px);
}

.record-workshop-info h3 {
	margin: 0 0 10px;
	color: #333;
	font-size: 18px;
}

.record-workshop-info p {
	margin: 5px 0;
	color: #555;
	font-size: 14px;
}

.record-workshop-info i {
	color: #564680;
	margin-right: 5px;
	width: 14px;
	text-align: center;
}

.record-date {
	color: #888 !important;
	font-size: 13px !important;
}

.record-status {
	display: inline-block;
	padding: 4px 10px;
	border-radius: 15px;
	font-size: 12px;
	font-weight: 500;
	margin-top: 10px;
	color: white;
}

.status-pending {
	background-color: #f39c12;
}

.status-confirmed {
	background-color: #3498db;
}

.status-completed {
	background-color: #2ecc71;
}

.status-cancelled {
	background-color: #e74c3c;
}

.popup-footer {
	margin-top: 20px;
}

.crystal-workshop-container {
	margin-top: 30px;
}

.crystal-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30px;
	margin-bottom: 40px;
}

.crystal-grid-mobile {
	grid-template-columns: 1fr;
	/* padding: 0 15px; */
}

.crystal-workshop-card {
	width: 350px;
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
	transition: transform 0.3s, box-shadow 0.3s;
	background: white;
}

.crystal-workshop-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.crystal-workshop-img {
	position: relative;
	height: 200px;
	overflow: hidden;
}

.crystal-workshop-img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.5s;
}

.crystal-workshop-card:hover .crystal-workshop-img img {
	transform: scale(1.05);
}

.workshop-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.5));
}

.crystal-workshop-info {
	padding: 20px;
}

.crystal-workshop-info h3 {
	margin: 0 0 10px;
	color: #333;
	font-size: 18px;
}

.crystal-workshop-info p {
	margin: 0 0 15px;
	color: #666;
	font-size: 14px;
	line-height: 1.5;
	height: 63px;
	overflow: hidden;
}

.crystal-workshop-meta {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 15px;
	font-size: 13px;
	color: #777;
}

.crystal-workshop-meta span {
	margin-right: 15px;
	margin-bottom: 5px;
}

.crystal-workshop-meta i {
	margin-right: 5px;
	color: #564680;
}

.crystal-workshop-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.crystal-workshop-price {
	font-size: 18px;
	font-weight: bold;
	color: #e83e8c;
}

.crystal-btn {
	padding: 8px 15px;
	background-color: #564680;
	color: white;
	border: none;
	border-radius: 20px;
	cursor: pointer;
	font-size: 14px;
	transition: background-color 0.3s;
}

.crystal-btn:hover {
	background-color: #483c6f;
}

.pulse-btn {
	position: relative;
	overflow: hidden;
}

.pulse-btn:after {
	content: "";
	position: absolute;
	top: 50%;
	left: 50%;
	width: 5px;
	height: 5px;
	background: rgba(255, 255, 255, 0.5);
	opacity: 0;
	border-radius: 100%;
	transform: scale(1, 1) translate(-50%);
	transform-origin: 50% 50%;
}

.pulse-btn:hover:after {
	animation: ripple 1.2s ease-out;
}

@keyframes ripple {
	0% {
		transform: scale(0, 0);
		opacity: 0.5;
	}

	100% {
		transform: scale(50, 50);
		opacity: 0;
	}
}

.no-data {
	text-align: center;
	padding: 40px 0;
	color: #999;
}

/* 分页样式 */
.am-pagination {
	display: flex;
	justify-content: center;
	list-style: none;
	padding: 0;
	margin: 30px 0;
}

.am-pagination li {
	margin: 0 5px;
}

.am-pagination a {
	display: block;
	padding: 8px 12px;
	color: #666;
	background: #f5f5f5;
	border-radius: 4px;
	text-decoration: none;
}

.am-pagination .am-active a {
	background-color: #564680;
	color: white;
}

.am-pagination .am-disabled a {
	color: #ccc;
	cursor: not-allowed;
}

/* 报名表单样式 */
.enroll-form {
	padding: 20px;
}

.enroll-title {
	text-align: center;
	margin-bottom: 20px;
	color: #564680;
}

/* 响应式调整 */
@media (max-width: 768px) {
	.crystal-grid {
		grid-template-columns: repeat(2, 1fr);
		padding: 0 15px;
	}

	.search-container {
		width: 90%;
		margin: 0 auto;
	}
}

@media (max-width: 576px) {
	.crystal-grid {
		grid-template-columns: 1fr;
		padding: 0 15px;
	}

	.crystal-workshop-meta {
		font-size: 12px;
	}

	.crystal-workshop-meta span {
		margin-right: 10px;
	}

	.crystal-workshop-footer {
		flex-direction: column;
		gap: 10px;
		align-items: flex-start;
	}

	.crystal-btn {
		width: 100%;
		text-align: center;
	}
	
	.my-enroll-btn {
		font-size: 14px;
		padding: 8px 16px;
	}
	
	.record-workshop-info h3 {
		font-size: 16px;
	}
}
</style>
