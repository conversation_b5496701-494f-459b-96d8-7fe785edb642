<template>
	<Layout>
		<div class="layout-container" style="width: 100%">
			<!-- 美化后的页面头部 -->
			<div class="hero-header-section download-header">
				<div class="hero-content">
					<h1 class="hero-title"><i class="am-icon-download fa-spin-pulse"></i> 资料下载</h1>
					<p class="hero-subtitle">探索水晶的神奇力量，获取专业学习资源</p>
				</div>
			</div>
			
			<div class="section">
				<div class="am-container">
					<div class="section--header">
						<h2 class="section--title" :style="isMobilePhone ? 'font-size: 20px' : ''">学习资料下载</h2>
						<p class="section--description">
							这里提供水晶疗愈相关的学习资料，包括教程、手册、图表等，帮助您更好地学习和应用水晶疗愈知识。
						</p>
					</div>
					
					<div class="download-container">
						<div class="download-list">
							<div v-for="(item, index) in downloadList" :key="index" class="download-item">
								<div class="download-info">
									<div class="download-icon">
										<i :class="getFileIcon(item.fileType)"></i>
									</div>
									<div class="download-content">
										<h3 class="download-title">{{ item.title }}</h3>
										<p class="download-desc">{{ item.description }}</p>
										<div class="download-meta">
											<span class="meta-item"><i class="am-icon-calendar"></i> {{ item.uploadDate }}</span>
											<span class="meta-item"><i class="am-icon-file-o"></i> {{ item.fileSize }}</span>
											<span class="meta-item"><i class="am-icon-download"></i> {{ item.downloads }} 次下载</span>
										</div>
									</div>
								</div>
								<div class="download-action">
									<button class="download-btn" @click="downloadFile(item)">
										<i class="am-icon-download"></i> 下载
									</button>
								</div>
							</div>
							
							<!-- 无数据显示 -->
							<div v-if="downloadList.length == 0" class="no-data">
								<i class="am-icon-exclamation-circle"></i>
								<p>暂无可下载资料</p>
							</div>

							<!-- 分页 -->
							<ul class="am-pagination" style="text-align: center;" v-if="total > 0">
								<li :class="pageIndex == 1 ? 'am-disabled':''" @click="changeIndex(pageIndex - 1)">
									<a href="javascript:void(0);">&laquo;</a>
								</li>
								
								<li v-for="p in totalPage" :key="p" @click="changeIndex(p)" :class="pageIndex == p ? 'am-active':''">
									<a href="javascript:void(0);">{{p}}</a>
								</li>
								
								<li :class="pageIndex == totalPage ? 'am-disabled':''" @click="changeIndex(pageIndex + 1)">
									<a href="javascript:void(0);">&raquo;</a>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import '../assets/css/common-headers.css';

export default {
	name: "DownloadView",
	components: { Layout },
	data() {
		return {
			isMobilePhone: isMobilePhone(),
			downloadList: [],
			searchKeyword: '',
			pageIndex: 1,
			pageSize: 10,
			total: 0,
			totalPage: 1
		}
	},
	mounted() {
		this.$wxShare();
		this.getDownloadList();
	},
	methods: {
		
		getDownloadList() {
			// 模拟数据，实际项目中应替换为真实接口
			this.getRequest("/cms/download/list", {
				'page': this.pageIndex,
				'limit': this.pageSize,
				'title': this.searchKeyword
			}).then(resp => {
				if (resp && resp.code == 200) {
					this.downloadList = resp.data.list || [];
					this.total = resp.data.total || 0;
					this.totalPage = resp.data.totalPage || 1;
				} else {
					this.downloadList = [];
					this.total = 0;
					this.totalPage = 1;
				}
			})
		},
		searchCourses() {
			this.pageIndex = 1; // 搜索时重置为第一页
			this.getDownloadList();
		},
		changeIndex(p) {
			if (p < 1) {
				this.pageIndex = 1;
			} else if (p > this.totalPage) {
				this.pageIndex = this.totalPage;
			} else {
				this.pageIndex = p;
				this.getDownloadList();
			}
		},
		
		getFileIcon(fileType) {
			const iconMap = {
				'pdf': 'am-icon-file-pdf-o',
				'doc': 'am-icon-file-word-o',
				'docx': 'am-icon-file-word-o',
				'xls': 'am-icon-file-excel-o',
				'xlsx': 'am-icon-file-excel-o',
				'ppt': 'am-icon-file-powerpoint-o',
				'pptx': 'am-icon-file-powerpoint-o',
				'zip': 'am-icon-file-archive-o',
				'rar': 'am-icon-file-archive-o',
				'jpg': 'am-icon-file-image-o',
				'jpeg': 'am-icon-file-image-o',
				'png': 'am-icon-file-image-o',
				'gif': 'am-icon-file-image-o',
				'mp3': 'am-icon-file-audio-o',
				'mp4': 'am-icon-file-video-o',
				'txt': 'am-icon-file-text-o'
			};
			
			return iconMap[fileType.toLowerCase()] || 'am-icon-file-o';
		},
		
		downloadFile(item) {
			// 判断是否登录
			const userInfoStr = localStorage.getItem("userInfo") || '{}';
			const userInfo = JSON.parse(userInfoStr);
			if (!userInfo.uid) {
				// 弹窗提示
				this.$message.error('请先登录');
				// 跳转登录
				this.$router.push('/login');
				return;
			}
			this.getRequest("/cmsAttend/download/count", {
				'id': item.id,
				'uid': userInfo.uid
			}).then(resp => {
				if (resp && resp.code == 200) {
					this.$message.success(`正在下载: ${item.title}`);
					window.open(item.fileUrl, '_blank');
					this.getDownloadList();

				}
			})
			
		}
	}
}
</script>

<style scoped>
/* 美化后的页面头部样式 */
.download-header {
	background-image: url('https://img.freepik.com/free-photo/brown-rocks-sea-water-close-up_23-2148220625.jpg') !important;
}

.section {
	padding: 50px 0;
}

.section--header {
	margin-bottom: 30px;
	text-align: center;
}

.section--title {
	font-size: 28px;
	color: #333;
	margin-bottom: 15px;
}

.section--description {
	font-size: 16px;
	color: #666;
	max-width: 800px;
	margin: 0 auto;
}

.download-container {
	max-width: 1000px;
	margin: 0 auto;
}

.download-list {
	margin-top: 30px;
}

.download-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	padding: 20px;
	margin-bottom: 20px;
	transition: all 0.3s ease;
}

.download-item:hover {
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
	transform: translateY(-2px);
}

.download-info {
	display: flex;
	flex: 1;
}

.download-icon {
	margin-right: 20px;
	font-size: 36px;
	color: #2d6ca2;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60px;
}

.download-content {
	flex: 1;
}

.download-title {
	font-size: 18px;
	color: #333;
	margin-bottom: 5px;
}

.download-desc {
	font-size: 14px;
	color: #666;
	margin-bottom: 10px;
	line-height: 1.5;
}

.download-meta {
	display: flex;
	flex-wrap: wrap;
	font-size: 12px;
	color: #999;
}

.meta-item {
	margin-right: 15px;
}

.meta-item i {
	margin-right: 5px;
}

.download-action {
	margin-left: 20px;
}

.download-btn {
	background-color: #2d6ca2;
	color: white;
	border: none;
	border-radius: 4px;
	padding: 8px 15px;
	font-size: 14px;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.download-btn:hover {
	background-color: #245a8b;
}

.download-btn i {
	margin-right: 5px;
}

.no-data {
	text-align: center;
	padding: 50px 0;
	color: #999;
}

.no-data i {
	font-size: 48px;
	margin-bottom: 15px;
	display: block;
}

/* 响应式调整 */
@media (max-width: 768px) {
	.download-item {
		flex-direction: column;
		align-items: flex-start;
	}
	
	.download-info {
		margin-bottom: 15px;
		width: 100%;
	}
	
	.download-action {
		margin-left: 0;
		width: 100%;
	}
	
	.download-btn {
		width: 100%;
		padding: 10px;
	}
	
	.download-icon {
		width: 40px;
		font-size: 24px;
		margin-right: 15px;
	}
}
</style>
