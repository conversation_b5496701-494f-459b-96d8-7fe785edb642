{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from '@/components/common/Layout.vue';\nexport default {\n  components: {\n    Layout\n  },\n  name: 'ChakraTestIntro',\n  data() {\n    return {\n      chakraList: [{\n        name: '海底轮',\n        english: 'Root Chakra',\n        color: '#993734',\n        location: '脊椎底部',\n        function: '生存、安全感、稳定',\n        keywords: '根基、安全、生存',\n        description: '海底轮是人体能量系统的根基，掌管着我们的生存本能、安全感和与大地的连接。当海底轮平衡时，我们会感到稳定、安全和有根基。'\n      }, {\n        name: '脐轮',\n        english: 'Sacral Chakra',\n        color: '#be6f2a',\n        location: '下腹部',\n        function: '创造力、情感、性能量',\n        keywords: '创造、情感、欲望',\n        description: '脐轮是创造力和情感的中心，掌管着我们的创造力、性能量和情感表达。平衡的脐轮让我们能够自由地表达情感和创造力。'\n      }, {\n        name: '太阳轮',\n        english: 'Solar Plexus Chakra',\n        color: '#d7c34a',\n        location: '上腹部',\n        function: '个人力量、自信、意志',\n        keywords: '力量、自信、意志',\n        description: '太阳轮是个人力量的中心，掌管着我们的自信、意志力和个人力量。当太阳轮平衡时，我们会感到自信、有力量和能够掌控自己的生活。'\n      }, {\n        name: '心轮',\n        english: 'Heart Chakra',\n        color: '#5f9057',\n        location: '胸部中央',\n        function: '爱、同情、连接',\n        keywords: '爱、同情、和谐',\n        description: '心轮是爱和同情的中心，掌管着我们给予和接受爱的能力。平衡的心轮让我们能够无条件地爱自己和他人。'\n      }, {\n        name: '喉轮',\n        english: 'Throat Chakra',\n        color: '#5b8aa4',\n        location: '喉咙',\n        function: '沟通、表达、真实',\n        keywords: '表达、沟通、真实',\n        description: '喉轮是沟通和表达的中心，掌管着我们说出真相和表达自己的能力。平衡的喉轮让我们能够清晰、诚实地表达自己。'\n      }, {\n        name: '眉心轮',\n        english: 'Third Eye Chakra',\n        color: '#2c3485',\n        location: '眉心',\n        function: '直觉、洞察、智慧',\n        keywords: '直觉、洞察、智慧',\n        description: '眉心轮是直觉和洞察的中心，掌管着我们的第六感和内在智慧。平衡的眉心轮让我们能够清晰地看到真相和拥有敏锐的直觉。'\n      }, {\n        name: '顶轮',\n        english: 'Crown Chakra',\n        color: '#7e4997',\n        location: '头顶',\n        function: '灵性、连接、觉醒',\n        keywords: '灵性、觉醒、连接',\n        description: '顶轮是灵性连接的中心，掌管着我们与宇宙和神圣的连接。平衡的顶轮让我们感到与万物合一和拥有深刻的灵性体验。'\n      }]\n    };\n  },\n  methods: {\n    goToTest() {\n      this.$router.push('/chakra-test');\n    },\n    goToBalance() {\n      this.$router.push('/chakra-test/balance');\n    },\n    // 返回上一页\n    goBack() {\n      this.$router.back();\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "components", "name", "data", "chakraList", "english", "color", "location", "function", "keywords", "description", "methods", "goToTest", "$router", "push", "goToBalance", "goBack", "back"], "sources": ["src/views/ChakraTestIntro.vue"], "sourcesContent": ["<template>\r\n  <Layout>\r\n    <div class=\"chakra-intro-page\">\r\n    <!-- 标题栏 -->\r\n    <div class=\"nav-title\">\r\n      <div class=\"nav-left\">\r\n        <van-button \r\n          class=\"back-button\"\r\n          @click=\"goBack\"\r\n          plain\r\n        >\r\n          <van-icon name=\"arrow-left\" size=\"18\" />\r\n        </van-button>\r\n        <div class=\"color-bar\"></div>\r\n        <div class=\"title-text\">脉轮简介</div>\r\n      </div>\r\n      <div class=\"nav-right\">\r\n        <van-button \r\n          class=\"test-button-small\"\r\n          @click=\"goToTest\"\r\n          size=\"small\"\r\n        >\r\n          <van-icon name=\"play\" size=\"14\" />\r\n          <span>开始测试</span>\r\n        </van-button>\r\n        <van-button \r\n          class=\"balance-button-small\"\r\n          @click=\"goToBalance\"\r\n          size=\"small\"\r\n          type=\"primary\"\r\n        >\r\n          <van-icon name=\"balance\" size=\"14\" />\r\n          <span>平衡脉轮</span>\r\n        </van-button>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 内容区域 -->\r\n    <div class=\"content-container\">\r\n      <div class=\"intro-content\">\r\n        <div class=\"chakra-item\" v-for=\"(chakra, index) in chakraList\" :key=\"index\">\r\n          <div class=\"chakra-header\">\r\n            <div class=\"chakra-dot\" :style=\"{ backgroundColor: chakra.color }\"></div>\r\n            <div class=\"chakra-info\">\r\n              <h3 class=\"chakra-name\">{{ chakra.name }}</h3>\r\n              <p class=\"chakra-english\">{{ chakra.english }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"chakra-description\">\r\n            <p>{{ chakra.description }}</p>\r\n          </div>\r\n          <div class=\"chakra-details\">\r\n            <div class=\"detail-item\">\r\n              <span class=\"label\">位置：</span>\r\n              <span class=\"value\">{{ chakra.location }}</span>\r\n            </div>\r\n            <div class=\"detail-item\">\r\n              <span class=\"label\">功能：</span>\r\n              <span class=\"value\">{{ chakra.function }}</span>\r\n            </div>\r\n            <div class=\"detail-item\">\r\n              <span class=\"label\">关键词：</span>\r\n              <span class=\"value\">{{ chakra.keywords }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    </div>\r\n  </Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from '@/components/common/Layout.vue'\r\n\r\nexport default {\r\n  components: {\r\n    Layout\r\n  },\r\n  name: 'ChakraTestIntro',\r\n  data() {\r\n    return {\r\n      chakraList: [\r\n        {\r\n          name: '海底轮',\r\n          english: 'Root Chakra',\r\n          color: '#993734',\r\n          location: '脊椎底部',\r\n          function: '生存、安全感、稳定',\r\n          keywords: '根基、安全、生存',\r\n          description: '海底轮是人体能量系统的根基，掌管着我们的生存本能、安全感和与大地的连接。当海底轮平衡时，我们会感到稳定、安全和有根基。'\r\n        },\r\n        {\r\n          name: '脐轮',\r\n          english: 'Sacral Chakra',\r\n          color: '#be6f2a',\r\n          location: '下腹部',\r\n          function: '创造力、情感、性能量',\r\n          keywords: '创造、情感、欲望',\r\n          description: '脐轮是创造力和情感的中心，掌管着我们的创造力、性能量和情感表达。平衡的脐轮让我们能够自由地表达情感和创造力。'\r\n        },\r\n        {\r\n          name: '太阳轮',\r\n          english: 'Solar Plexus Chakra',\r\n          color: '#d7c34a',\r\n          location: '上腹部',\r\n          function: '个人力量、自信、意志',\r\n          keywords: '力量、自信、意志',\r\n          description: '太阳轮是个人力量的中心，掌管着我们的自信、意志力和个人力量。当太阳轮平衡时，我们会感到自信、有力量和能够掌控自己的生活。'\r\n        },\r\n        {\r\n          name: '心轮',\r\n          english: 'Heart Chakra',\r\n          color: '#5f9057',\r\n          location: '胸部中央',\r\n          function: '爱、同情、连接',\r\n          keywords: '爱、同情、和谐',\r\n          description: '心轮是爱和同情的中心，掌管着我们给予和接受爱的能力。平衡的心轮让我们能够无条件地爱自己和他人。'\r\n        },\r\n        {\r\n          name: '喉轮',\r\n          english: 'Throat Chakra',\r\n          color: '#5b8aa4',\r\n          location: '喉咙',\r\n          function: '沟通、表达、真实',\r\n          keywords: '表达、沟通、真实',\r\n          description: '喉轮是沟通和表达的中心，掌管着我们说出真相和表达自己的能力。平衡的喉轮让我们能够清晰、诚实地表达自己。'\r\n        },\r\n        {\r\n          name: '眉心轮',\r\n          english: 'Third Eye Chakra',\r\n          color: '#2c3485',\r\n          location: '眉心',\r\n          function: '直觉、洞察、智慧',\r\n          keywords: '直觉、洞察、智慧',\r\n          description: '眉心轮是直觉和洞察的中心，掌管着我们的第六感和内在智慧。平衡的眉心轮让我们能够清晰地看到真相和拥有敏锐的直觉。'\r\n        },\r\n        {\r\n          name: '顶轮',\r\n          english: 'Crown Chakra',\r\n          color: '#7e4997',\r\n          location: '头顶',\r\n          function: '灵性、连接、觉醒',\r\n          keywords: '灵性、觉醒、连接',\r\n          description: '顶轮是灵性连接的中心，掌管着我们与宇宙和神圣的连接。平衡的顶轮让我们感到与万物合一和拥有深刻的灵性体验。'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    goToTest() {\r\n      this.$router.push('/chakra-test')\r\n    },\r\n    \r\n    goToBalance() {\r\n      this.$router.push('/chakra-test/balance')\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      this.$router.back()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.chakra-intro-page {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n}\r\n\r\n.nav-title {\r\n  padding: 15px 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  height: 60px;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n  box-shadow: 0 4px 15px rgba(86, 70, 128, 0.1);\r\n  margin-bottom: 20px;\r\n  border-radius: 0 0 20px 20px;\r\n}\r\n\r\n.nav-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.nav-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.back-button {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background: rgba(86, 70, 128, 0.08);\r\n  border: 1px solid rgba(86, 70, 128, 0.2);\r\n  color: #564680;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.back-button:hover {\r\n  background: rgba(86, 70, 128, 0.15);\r\n  border-color: rgba(86, 70, 128, 0.4);\r\n  transform: translateX(-2px);\r\n}\r\n\r\n.color-bar {\r\n  width: 6px;\r\n  height: 25px;\r\n  background: linear-gradient(135deg, #564680, #516790, #c9ab79);\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(86, 70, 128, 0.3);\r\n}\r\n\r\n.title-text {\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.test-button-small {\r\n  background: linear-gradient(135deg, #564680, #516790);\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 6px 12px;\r\n  color: white;\r\n  font-weight: 600;\r\n  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.test-button-small:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);\r\n}\r\n\r\n.test-button-small span {\r\n  margin-left: 4px;\r\n}\r\n\r\n.balance-button-small {\r\n  background: linear-gradient(135deg, #c9ab79, #b8996a);\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 6px 12px;\r\n  color: white;\r\n  font-weight: 600;\r\n  box-shadow: 0 3px 10px rgba(201, 171, 121, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.balance-button-small:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(201, 171, 121, 0.4);\r\n}\r\n\r\n.balance-button-small span {\r\n  margin-left: 4px;\r\n}\r\n\r\n.content-container {\r\n  padding: 0 15px 20px;\r\n}\r\n\r\n.chakra-item {\r\n  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);\r\n  border-radius: 16px;\r\n  margin-bottom: 20px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 25px rgba(86, 70, 128, 0.08);\r\n  transition: all 0.4s ease;\r\n  border: 2px solid transparent;\r\n  position: relative;\r\n}\r\n\r\n.chakra-item::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, #564680, #516790, #c9ab79);\r\n}\r\n\r\n.chakra-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 15px 35px rgba(86, 70, 128, 0.12);\r\n  border-color: rgba(86, 70, 128, 0.2);\r\n}\r\n\r\n.chakra-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 2px solid rgba(86, 70, 128, 0.1);\r\n  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);\r\n  position: relative;\r\n}\r\n\r\n.chakra-header::after {\r\n  content: '';\r\n  position: absolute;\r\n  left: 20px;\r\n  bottom: -2px;\r\n  width: 40px;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, #564680, #c9ab79);\r\n}\r\n\r\n.chakra-dot {\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  margin-right: 15px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.chakra-item:hover .chakra-dot {\r\n  transform: scale(1.2) rotate(15deg);\r\n  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.chakra-info {\r\n  flex: 1;\r\n}\r\n\r\n.chakra-name {\r\n  font-size: 18px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  margin: 0 0 6px 0;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.chakra-english {\r\n  font-size: 13px;\r\n  color: #666;\r\n  margin: 0;\r\n  font-style: italic;\r\n  font-weight: 500;\r\n}\r\n\r\n.chakra-description {\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(86, 70, 128, 0.08);\r\n  \r\n  p {\r\n    font-size: 15px;\r\n    line-height: 1.7;\r\n    color: #555;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n.chakra-details {\r\n  padding: 15px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.label {\r\n  color: #666;\r\n  min-width: 50px;\r\n}\r\n\r\n.value {\r\n  color: #333;\r\n  flex: 1;\r\n}\r\n\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 768px) {\r\n  .chakra-item {\r\n    margin-bottom: 16px;\r\n    border-radius: 12px;\r\n  }\r\n  \r\n  .chakra-header {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .chakra-name {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .chakra-description {\r\n    padding: 16px;\r\n    \r\n    p {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n  \r\n  .nav-title {\r\n    padding: 12px 20px;\r\n    height: 55px;\r\n  }\r\n  \r\n  .nav-left {\r\n    gap: 8px;\r\n  }\r\n  \r\n  .nav-right {\r\n    gap: 6px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n  \r\n  .title-text {\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .test-button-small,\r\n  .balance-button-small {\r\n    padding: 5px 10px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .nav-title {\r\n    padding: 10px 15px;\r\n    height: 50px;\r\n  }\r\n  \r\n  .nav-left {\r\n    gap: 6px;\r\n  }\r\n  \r\n  .nav-right {\r\n    gap: 4px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 32px;\r\n    height: 32px;\r\n  }\r\n  \r\n  .color-bar {\r\n    width: 4px;\r\n    height: 20px;\r\n  }\r\n  \r\n  .title-text {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .test-button-small,\r\n  .balance-button-small {\r\n    padding: 4px 8px;\r\n    font-size: 11px;\r\n  }\r\n  \r\n  .content-container {\r\n    padding: 0 12px 20px;\r\n  }\r\n  \r\n  .chakra-header {\r\n    padding: 14px;\r\n  }\r\n  \r\n  .chakra-name {\r\n    font-size: 15px;\r\n  }\r\n  \r\n  .chakra-description {\r\n    padding: 14px;\r\n  }\r\n  \r\n  .chakra-details {\r\n    padding: 14px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA,OAAAA,MAAA;AAEA;EACAC,UAAA;IACAD;EACA;EACAE,IAAA;EACAC,KAAA;IACA;MACAC,UAAA,GACA;QACAF,IAAA;QACAG,OAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;MACA,GACA;QACAR,IAAA;QACAG,OAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;MACA,GACA;QACAR,IAAA;QACAG,OAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;MACA,GACA;QACAR,IAAA;QACAG,OAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;MACA,GACA;QACAR,IAAA;QACAG,OAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;MACA,GACA;QACAR,IAAA;QACAG,OAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;MACA,GACA;QACAR,IAAA;QACAG,OAAA;QACAC,KAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;MACA;IAEA;EACA;EACAC,OAAA;IACAC,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IAEAC,YAAA;MACA,KAAAF,OAAA,CAAAC,IAAA;IACA;IAEA;IACAE,OAAA;MACA,KAAAH,OAAA,CAAAI,IAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}