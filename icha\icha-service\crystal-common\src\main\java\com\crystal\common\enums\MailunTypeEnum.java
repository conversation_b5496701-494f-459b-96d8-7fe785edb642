package com.crystal.common.enums;

import java.math.BigDecimal;

/**
 * 题目类型枚举类
 * 0-单选 1-多选 2-填空
 */
public enum MailunTypeEnum {
    ROOT(0, "海底轮"),
    SACRAL(1, "脐轮"),
    NAVEL(2, "太阳轮"),
    HEART(3, "心轮"),
    THROAT(4, "喉轮"),
    THIRDEYE(5, "眉心轮"),
    CROWN(6, "顶轮");

    private Integer code;
    private String msg;

    MailunTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
    public static String getValueByCode(int code) {
        for (MailunTypeEnum typeEnum : MailunTypeEnum.values()) {
            if (typeEnum.code == code) {
                return typeEnum.msg;
            }
        }
        throw new IllegalArgumentException("No element matches " + code);
    }
    public static Integer getValueByMsg(String code) {
        for (MailunTypeEnum typeEnum : MailunTypeEnum.values()) {
            if (typeEnum.msg.equals(code)) {
                return typeEnum.code;
            }
        }
        throw new IllegalArgumentException("No element matches " + code);
    }
}
