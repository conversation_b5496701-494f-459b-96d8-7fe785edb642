{"ast": null, "code": "/**\r\n * 判断是否为移动设备\r\n * @returns {*|boolean}\r\n */\nexport function isMobilePhone() {\n  // 获取访问链接\n  let href = window.location.href;\n  if (href.includes(\"pc=true\")) {\n    return false;\n  }\n  if (href.includes(\"pc=false\")) {\n    return true;\n  }\n  //获取访问的user-agent\n  let ua = window.navigator.userAgent.toLowerCase();\n  //判断user-agent\n  // isWX = /MicroMessenger/i.test(ua); //微信端\n  // isIOS = /(iPhone|iPad|iPod|iOS)/i.test(ua); //苹果家族\n  // isAndroid = /(android|nexus)/i.test(ua); //安卓家族\n  // isWindows = /(Windows Phone|windows[\\s+]phone)/i.test(ua); //微软家族\n  return /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|MicroMessenger)/i.test(ua);\n}\n\n/**\r\n * 手机号码\r\n * @param {*} s\r\n */\nexport function isMobile(s) {\n  return /^1[0-9]{10}$/.test(s);\n}", "map": {"version": 3, "names": ["isMobilePhone", "href", "window", "location", "includes", "ua", "navigator", "userAgent", "toLowerCase", "test", "isMobile", "s"], "sources": ["C:/Users/<USER>/Desktop/code/crystal-mall/icha/crystal-website/src/utils/index.js"], "sourcesContent": ["\r\n/**\r\n * 判断是否为移动设备\r\n * @returns {*|boolean}\r\n */\r\nexport function isMobilePhone() {\r\n    // 获取访问链接\r\n    let href = window.location.href;\r\n    if(href.includes(\"pc=true\")) {\r\n      return false;\r\n    }\r\n    if(href.includes(\"pc=false\")) {\r\n      return true;\r\n    }\r\n    //获取访问的user-agent\r\n    let ua = window.navigator.userAgent.toLowerCase();\r\n    //判断user-agent\r\n    // isWX = /MicroMessenger/i.test(ua); //微信端\r\n    // isIOS = /(iPhone|iPad|iPod|iOS)/i.test(ua); //苹果家族\r\n    // isAndroid = /(android|nexus)/i.test(ua); //安卓家族\r\n    // isWindows = /(Windows Phone|windows[\\s+]phone)/i.test(ua); //微软家族\r\n    return /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|MicroMessenger)/i.test(ua)\r\n\r\n}\r\n\r\n/**\r\n * 手机号码\r\n * @param {*} s\r\n */\r\nexport function isMobile(s) {\r\n    return /^1[0-9]{10}$/.test(s)\r\n}\r\n"], "mappings": "AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,aAAaA,CAAA,EAAG;EAC5B;EACA,IAAIC,IAAI,GAAGC,MAAM,CAACC,QAAQ,CAACF,IAAI;EAC/B,IAAGA,IAAI,CAACG,QAAQ,CAAC,SAAS,CAAC,EAAE;IAC3B,OAAO,KAAK;EACd;EACA,IAAGH,IAAI,CAACG,QAAQ,CAAC,UAAU,CAAC,EAAE;IAC5B,OAAO,IAAI;EACb;EACA;EACA,IAAIC,EAAE,GAAGH,MAAM,CAACI,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC;EACjD;EACA;EACA;EACA;EACA;EACA,OAAO,gKAAgK,CAACC,IAAI,CAACJ,EAAE,CAAC;AAEpL;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASK,QAAQA,CAACC,CAAC,EAAE;EACxB,OAAO,cAAc,CAACF,IAAI,CAACE,CAAC,CAAC;AACjC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}