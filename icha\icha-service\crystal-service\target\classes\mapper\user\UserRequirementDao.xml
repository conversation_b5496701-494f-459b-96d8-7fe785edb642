<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.crystal.service.dao.UserRequirementDao">

    <!-- 根据包名 模块名 以及类名 生成Mapper XML 配置文件 -->
    <resultMap type="com.crystal.common.model.user.UserRequirement" id="userRequirementMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="addTime" column="add_time"/>
        <result property="isDel" column="is_del"/>
        <result property="age" column="age"/>
        <result property="gender" column="gender"/>
        <result property="occupation" column="occupation"/>
        <result property="purpose" column="purpose"/>
        <result property="budget" column="budget"/>
        <result property="colorPreference" column="color_preference"/>
        <result property="materials" column="materials"/>
        <result property="specialNeeds" column="special_needs"/>
        <result property="aiResponse" column="ai_response"/>
    </resultMap>

</mapper> 