package com.crystal.admin.controller;

import com.crystal.common.model.user.UserProcess;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.UserProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * 用户手串 控制器
 * | Author: 陈佳音
 * ｜ @date Fri Nov 22 16:43:13 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/userprocess")
public class UserProcessController {
    @Autowired
    private UserProcessService userProcessService;

    /**
     * 列表信息
     */
    @RequestMapping("/findByUserId")
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:list')")
    public CommonResult<List<UserProcess>> findByUserId(@RequestParam("userId") Integer userId) {
        List<UserProcess> userProcesses = userProcessService.findByUserId(userId);

        return CommonResult.success(userProcesses);
    }


    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:list')")
    public CommonResult<CommonPage<UserProcess>> list(@Validated UserProcess request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<UserProcess> page = CommonPage.restPage(userProcessService.queryPage(request,pageParamRequest));

        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:info')")
    public CommonResult<UserProcess> info(@PathVariable("id") Integer id){
		UserProcess userProcess = userProcessService.getById(id);

        return CommonResult.success(userProcess);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:save')")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> save(@RequestBody UserProcess userProcess){
        userProcess.setAddTime(new Date());
        if (userProcessService.save(userProcess)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
    @Transactional(rollbackFor = Exception.class)
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:update')")
    public CommonResult<String> update(@RequestBody UserProcess userProcess){

        if (userProcessService.updateById(userProcess)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:delete')")
    public CommonResult<String> delete(@RequestBody Integer[] ids){
        if (userProcessService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
