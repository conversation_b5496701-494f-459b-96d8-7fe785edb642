<template>
	<div class="footer">
		<div style="background-color:#383d61" class="footer--bg"></div>
		<div class="footer--inner">
			<div class="am-g">
				<div class="am-u-md-3 ">
					<div class="footer_main--column">
						<!-- <strong class="footer_main--column_title">产品中心</strong> -->
						<ul class="footer_navigation">
							<li class="footer_navigation--item"><router-link to="/about" class="footer_navigation--link">国际水晶疗愈协会</router-link></li>
						</ul>
					</div>
				</div>
				<div class="am-u-md-6 ">
					<div class="footer_main--column">
						<strong class="footer_main--column_title">关于我们</strong>
						<div class="footer_about">
							<p class="footer_about--text">
								{{ footerabout }}
							</p>
						</div>
					</div>
				</div>
				<div class="am-u-md-3 ">
					<div class="footer_main--column">
						<strong class="footer_main--column_title">联系详情</strong>
						<ul class="footer_contact_info">
							<li class="footer_contact_info--item"><i class="am-icon-phone"></i><span>服务专线：{{ aboutmobile }}</span></li>
							<li class="footer_contact_info--item"><i class="am-icon-map-marker"></i><span>{{ aboutaddress }}</span></li>
							<li class="footer_contact_info--item"><i class="am-icon-clock-o"></i><span>{{ aboutkefutime }}</span></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: "Footer",
	data() {
		return {
			aboutaddress: '',
			aboutemail: '',
			aboutmobile: '',
			footerabout: '',
			aboutkefutime: '',
		}
	},
	mounted() {
		this.getConfigFooter()
	},
	methods: {
		getConfigFooter() {
			this.getRequest("/cms/config/footer").then(resp => {
				if (resp && resp.code == 200) {
					this.aboutaddress = resp.data.aboutaddress;
					this.aboutemail = resp.data.aboutemail;
					this.aboutmobile = resp.data.aboutmobile;
					this.footerabout = resp.data.footerabout;
					this.aboutkefutime = resp.data.aboutkefutime;
				}
			})
		}
	}
}
</script>

<style scoped>

</style>
