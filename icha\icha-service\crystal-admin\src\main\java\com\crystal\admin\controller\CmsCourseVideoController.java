package com.crystal.admin.controller;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoRequest;
import com.aliyuncs.vod.model.v20170321.GetPlayInfoResponse;
import com.crystal.common.config.InitObject;
import com.crystal.common.exception.CrmebException;
import com.crystal.common.model.cms.CmsCourseVideoEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsCourseVideoService;
import com.crystal.service.service.SystemAttachmentService;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 课程视频 控制器
 * | Author: 陈佳音
 * ｜ @date Mon May 14 22:50:31 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/course/video")
public class CmsCourseVideoController {
    @Autowired
    private CmsCourseVideoService cmsCourseVideoService;
    @Autowired
    private SystemAttachmentService systemAttachmentService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
    // @PreAuthorize("hasAuthority('cmscoursevideo:list')")
    public CommonResult<CommonPage<CmsCourseVideoEntity>> list(@Validated CmsCourseVideoEntity request,
            @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsCourseVideoEntity> page = CommonPage
                .restPage(cmsCourseVideoService.queryPage(request, pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
    // @PreAuthorize("hasAuthority('cmscoursevideo:info')")
    public CommonResult<CmsCourseVideoEntity> info(@PathVariable("id") Long id) {
        CmsCourseVideoEntity cmsCourseVideo = cmsCourseVideoService.getById(id);
        return CommonResult.success(cmsCourseVideo);
    }

    /**
     * 根据课程ID查询视频列表
     */
    @RequestMapping("/course/{cmsCourseId}")
    // @PreAuthorize("hasAuthority('cmscoursevideo:list')")
    public CommonResult<List<CmsCourseVideoEntity>> listByCourseId(@PathVariable("cmsCourseId") Long cmsCourseId) {
        List<CmsCourseVideoEntity> list = cmsCourseVideoService.queryByCourseId(cmsCourseId);
        return CommonResult.success(list);
    }

    /**
     * 根据视频名称查询
     */
    @RequestMapping("/name/{name}")
    // @PreAuthorize("hasAuthority('cmscoursevideo:list')")
    public CommonResult<List<CmsCourseVideoEntity>> listByName(@PathVariable("name") String name) {
        List<CmsCourseVideoEntity> list = cmsCourseVideoService.queryByName(name);
        return CommonResult.success(list);
    }

    /**
     * 根据邀请码查询视频
     */
    @RequestMapping("/invite/{inviteCode}")
    // @PreAuthorize("hasAuthority('cmscoursevideo:list')")
    public CommonResult<List<CmsCourseVideoEntity>> listByInviteCode(@PathVariable("inviteCode") String inviteCode) {
        List<CmsCourseVideoEntity> list = cmsCourseVideoService.queryByInviteCode(inviteCode);
        return CommonResult.success(list);
    }

    /**
     * 新增数据
     * 
     * @throws ClientException
     * @throws ServerException
     */
    @RequestMapping("/save")
    // @PreAuthorize("hasAuthority('cmscoursevideo:save')")
    public CommonResult<String> save(@RequestBody CmsCourseVideoEntity cmsCourseVideo)
            throws ServerException, ClientException {
        cmsCourseVideo.setAddTime(new Date());
        cmsCourseVideo.setCover(systemAttachmentService.clearPrefix(cmsCourseVideo.getCover()));
        if (StringUtils.isNotEmpty(cmsCourseVideo.getFileId())) {

            DefaultAcsClient client = InitObject.initVodClient("LTAI5tEUZyqyHUm8uCjCstgp",
                    "******************************");
            GetPlayInfoRequest getVideoListRequest = new GetPlayInfoRequest();
            getVideoListRequest.setVideoId(cmsCourseVideo.getFileId());
            GetPlayInfoResponse acsResponse = client.getAcsResponse(getVideoListRequest);
            List<GetPlayInfoResponse.PlayInfo> playInfoList = acsResponse.getPlayInfoList();
            if (playInfoList.size() > 0) {
                cmsCourseVideo.setMediaUrl(playInfoList.get(0).getPlayURL());
                cmsCourseVideo.setFileSize(playInfoList.get(0).getSize());
                cmsCourseVideo.setDuration(new BigDecimal(playInfoList.get(0).getDuration()));
                cmsCourseVideo.setFrameRate(new BigDecimal(playInfoList.get(0).getFps()));
            } else {
                throw new CrmebException("找不到视频");
            }
        }
        cmsCourseVideoService.save(cmsCourseVideo);
        return CommonResult.success();
    }

    /**
     * 修改数据
          * @throws ClientException 
          * @throws ServerException 
          */
         @RequestMapping("/update")
         // @PreAuthorize("hasAuthority('cmscoursevideo:update')")
         public CommonResult<String> update(@RequestBody CmsCourseVideoEntity cmsCourseVideo) throws ServerException, ClientException {
        cmsCourseVideo.setUpdateTime(new Date());
        cmsCourseVideo.setCover(systemAttachmentService.clearPrefix(cmsCourseVideo.getCover()));
        if (StringUtils.isNotEmpty(cmsCourseVideo.getFileId())) {

            DefaultAcsClient client = InitObject.initVodClient("LTAI5tEUZyqyHUm8uCjCstgp",
                    "******************************");
            GetPlayInfoRequest getVideoListRequest = new GetPlayInfoRequest();
            getVideoListRequest.setVideoId(cmsCourseVideo.getFileId());
            GetPlayInfoResponse acsResponse = client.getAcsResponse(getVideoListRequest);
            List<GetPlayInfoResponse.PlayInfo> playInfoList = acsResponse.getPlayInfoList();
            if (playInfoList.size() > 0) {
                cmsCourseVideo.setMediaUrl(playInfoList.get(0).getPlayURL());
                cmsCourseVideo.setFileSize(playInfoList.get(0).getSize());
                cmsCourseVideo.setDuration(new BigDecimal(playInfoList.get(0).getDuration()));
                cmsCourseVideo.setFrameRate(new BigDecimal(playInfoList.get(0).getFps()));
            } else {
                throw new CrmebException("找不到视频");
            }
        }
        cmsCourseVideoService.updateById(cmsCourseVideo);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
    // @PreAuthorize("hasAuthority('cmscoursevideo:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids) {
        if (cmsCourseVideoService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
}