<template>
  <div>
    <!-- 移动端布局（无Layout包装） -->
    <div v-if="isMobilePhone && courseDetail.isAttend" class="mobile-layout">
      <!-- 固定在顶部的视频播放器 -->
      <div class="mobile-fixed-video" v-if="currentVideo">
        <!-- 返回按钮 -->
        <div class="mobile-back-btn" @click="$router.go(-1)">
          <i class="fa fa-arrow-left"></i>
        </div>
        <video
          ref="videoPlayer"
          :src="currentVideo.mediaUrl"
          :poster="currentVideo.cover || courseDetail.cover"
          controlsList="nodownload noremoteplayback"
          disablePictureInPicture
          disableRemotePlayback
          controls
          :autoplay="false"
          class="mobile-video-player"
          @ended="playNextVideo"
          oncontextmenu="return false"
          @copy.prevent
          @dragstart.prevent
          @timeupdate="onTimeUpdate"
        ></video>
        <div class="video-time-info" v-if="currentDuration > 0">
          {{ currentTime | formatDuration }} / {{ currentDuration | formatDuration }}
        </div>
      </div>

      <!-- 固定Tab导航 -->
      <div class="mobile-fixed-tabs">
        <div class="mobile-tabs">
          <div class="mobile-tab" :class="{ 'active': activeTab === 'videos' }" @click="activeTab = 'videos'">
            <i class="fa fa-list"></i>
            <span>目录</span>
          </div>
          <div class="mobile-tab" :class="{ 'active': activeTab === 'info' }" @click="activeTab = 'info'">
            <i class="fa fa-info-circle"></i>
            <span>介绍</span>
          </div>
          <div v-if="currentVideo && currentVideo.file" class="mobile-tab" :class="{ 'active': activeTab === 'download' }" @click="activeTab = 'download'">
            <i class="fa fa-download"></i>
            <span>资料</span>
          </div>
          <div v-if="courseDetail.url" class="mobile-tab" :class="{ 'active': activeTab === 'exam' }" @click="activeTab = 'exam'">
            <i class="fa fa-clipboard-list"></i>
            <span>考试</span>
          </div>
        </div>
      </div>

      <!-- 可滚动的内容区域 -->
      <div class="mobile-content-area">
        <!-- 视频列表 -->
        <div v-if="activeTab === 'videos'" class="mobile-video-list" ref="videoListContainer">
          <div v-for="(video, index) in videoList" :key="index"
               class="mobile-video-item"
               :class="{ 'active': currentVideoId === video.id }"
               @click="playVideo(video, index)">
            <div class="video-thumbnail">
              <img :src="video.cover || courseDetail.cover" :alt="video.name" />
              <div class="play-overlay">
                <i class="fa fa-play"></i>
              </div>
              <div class="video-duration">{{ video.duration | formatDuration }}</div>
            </div>
            <div class="video-info">
              <h4>{{ index + 1 }}. {{ video.name }}</h4>
              <p v-if="video.description">{{ video.description }}</p>
            </div>
          </div>
        </div>

        <!-- 课程介绍 -->
        <div v-if="activeTab === 'info'" class="mobile-course-info">
          <div class="course-description">
            <div v-html="courseDetail.content"></div>
          </div>
        </div>

        <!-- 资料下载 -->
        <div v-if="activeTab === 'download'" class="mobile-download-section">
          <div v-if="currentVideo && currentVideo.file" class="download-item">
            <div class="download-info">
              <i class="fa fa-file-pdf-o download-icon"></i>
              <div class="download-details">
                <h4>{{ currentVideo.name }} - 课程资料</h4>
                <p>点击下载相关学习资料</p>
              </div>
            </div>
            <button class="download-btn" @click="downloadFile(currentVideo.file)">
              <i class="fa fa-download"></i> 下载
            </button>
          </div>
          <div v-else class="no-data">
            <p>当前视频暂无资料下载</p>
          </div>
        </div>

        <!-- 课程考试 -->
        <div v-if="activeTab === 'exam'" class="mobile-exam-section">
          <iframe :src="courseDetail.url" class="mobile-course-iframe"></iframe>
        </div>
      </div>
    </div>

    <!-- 移动端未报名提示 -->
    <div v-if="isMobilePhone && !courseDetail.isAttend" class="mobile-no-attend-container">
      <!-- 返回按钮 -->
      <div class="mobile-back-btn-fixed" @click="$router.go(-1)">
        <i class="fa fa-arrow-left"></i>
      </div>
      <div class="mobile-no-attend-tip">
        <i class="fa fa-lock"></i>
        <h3>需要报名观看</h3>
        <p>¥{{ courseDetail.price }}</p>
        <button class="crystal-btn" @click="enrollCourse">立即报名</button>
      </div>
    </div>

    <!-- 移动端课程报名弹窗复用组件 -->
    <CourseApplyDialog v-if="isMobilePhone" :visible="showApplyDialog" :course="applyCourse" @close="closeApplyDialog" @success="applySuccess" />

    <!-- 桌面端布局（使用Layout包装） -->
    <Layout v-if="!isMobilePhone">
      <!-- 桌面端头部 -->
      <div class="layout-container" style="width: 100%">
        <!-- 课程详情页头部 -->
        <div class="hero-header-section course-detail-header">
          <div class="hero-content">
            <h1 class="hero-title"><i class="fa fa-play-circle fa-spin-pulse"></i> {{ courseDetail.title }}</h1>
            <p class="hero-subtitle">{{ courseDetail.brief }}</p>
          </div>
        </div>
      </div>

      <!-- 桌面端内容 -->
      <div class="section">
        <div class="container" style="max-width: 1160px">
          <div class="course-detail-container">
            <!-- 课程信息导航栏 -->
            <div class="course-nav-bar">
              <div class="course-basic-info">
                <div class="course-cover-img" v-if="courseDetail.cover" @click="openCourseCover">
                  <img :src="courseDetail.cover" alt="课程封面" />
                  <div class="cover-mask"></div>
                  <div class="cover-label">课程封面</div>
                </div>
                <div class="course-title-container">
                  <h2>{{ courseDetail.title }}</h2>
                  <div class="course-stats">
                    <span><i class="fa fa-users"></i> {{ courseDetail.studentCount || 300 }}人已学习</span>
                  </div>
                </div>
                <div class="course-price-container">
                  <div class="course-price">¥{{ courseDetail.price }}</div>
                  <button v-if="!courseDetail.isAttend" class="crystal-btn" @click="enrollCourse">立即报名</button>
                  <button v-else class="crystal-btn-secondary" disabled>已报名</button>
                </div>
              </div>
            </div>

            <div v-if="courseDetail.isAttend" class="course-content-area">
              <!-- 视频播放区域 -->
              <div class="video-player-container" v-if="currentVideo">
                <video
                  ref="videoPlayer"
                  :src="currentVideo.mediaUrl"
                  :poster="currentVideo.cover || courseDetail.cover"
                  controlsList="nodownload noremoteplayback"
                  disablePictureInPicture
                  disableRemotePlayback
                  controls
                  :autoplay="false"
                  class="video-player"
                  @ended="playNextVideo"
                  oncontextmenu="return false"
                  @copy.prevent
                  @dragstart.prevent
                  @timeupdate="onTimeUpdate"
                ></video>
                <div class="video-time-info" v-if="currentDuration > 0">
                  {{ currentTime | formatDuration }} / {{ currentDuration | formatDuration }}
                </div>
              </div>

              <!-- 视频与课程信息区域 -->
              <div class="video-info-container">
                <!-- 桌面端Tab -->
                <div class="tabs">
                  <div class="tab" :class="{ 'active': activeTab === 'videos' }" @click="activeTab = 'videos'">
                    <i class="fa fa-list"></i> 课程目录
                  </div>
                  <div class="tab" :class="{ 'active': activeTab === 'info' }" @click="activeTab = 'info'">
                    <i class="fa fa-info-circle"></i> 课程介绍
                  </div>
                  <div v-if="currentVideo && currentVideo.file" class="tab" :class="{ 'active': activeTab === 'download' }" @click="activeTab = 'download'">
                    <i class="fa fa-download"></i> 资料下载
                  </div>
                  <div v-if="courseDetail.url" class="tab" :class="{ 'active': activeTab === 'exam' }" @click="activeTab = 'exam'">
                    <i class="fa fa-clipboard-list"></i> 课程考试
                  </div>
                </div>

                <!-- 视频列表标签页 -->
                <div class="tab-content" v-if="activeTab === 'videos'">
                  <div class="video-list">
                    <div v-for="(video, index) in videoList" :key="index" class="video-item"
                      :class="{ 'active': currentVideoId === video.id }" @click="playVideo(video)">
                      <div class="video-item-index">{{ index + 1 }}</div>
                      <div class="video-item-info">
                        <h4>{{ video.name }}</h4>
                        <p>{{ video.duration | formatDuration }}</p>
                      </div>
                      <div class="video-item-status">
                        <i class="fa" :class="currentVideoId === video.id ? 'fa-pause' : 'fa-play'"></i>
                      </div>
                    </div>
                  </div>

                  <!-- 无数据提示 -->
                  <div v-if="videoList.length === 0" class="no-data">
                    <p>暂无视频数据</p>
                  </div>
                </div>

                <!-- 课程信息标签页 -->
                <div class="tab-content" v-if="activeTab === 'info'">
                  <div class="course-description">
                    <div v-html="courseDetail.content"></div>
                  </div>
                </div>

                <!-- 资料下载标签页 -->
                <div class="tab-content" v-if="activeTab === 'download'">
                  <div class="download-section">
                    <div v-if="currentVideo && currentVideo.file" class="download-item">
                      <div class="download-info">
                        <i class="fa fa-file-pdf-o download-icon"></i>
                        <div class="download-details">
                          <h4>{{ currentVideo.name }} - 课程资料</h4>
                          <p>点击下载相关学习资料</p>
                        </div>
                      </div>
                      <button class="download-btn" @click="downloadFile(currentVideo.file)">
                        <i class="fa fa-download"></i> 下载
                      </button>
                    </div>
                    <div v-else class="no-data">
                      <p>当前视频暂无资料下载</p>
                    </div>
                  </div>
                </div>

                <!-- 课程考试标签页 -->
                <div class="tab-content" v-if="activeTab === 'exam'">
                  <div >
                    <!-- iframe -->
                    <iframe :src="courseDetail.url" class="course-iframe"></iframe>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="course-content-area no-attend-mask">
              <!-- 桌面端未报名提示 -->
              <div class="no-attend-tip">
                <i class="fa fa-lock"></i>
                <p>请先报名后观看课程内容</p>
                <button class="crystal-btn" @click="enrollCourse">立即报名</button>
              </div>
            </div>
          </div>
        </div>

        <hr class="section_divider -narrow">

        <!-- 课程报名弹窗复用组件 -->
        <CourseApplyDialog :visible="showApplyDialog" :course="applyCourse" @close="closeApplyDialog" @success="applySuccess" />
      </div>
    </Layout>
  </div>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import CourseApplyDialog from '@/components/CourseApplyDialog.vue';
import '../assets/css/common-headers.css'; // 导入头部共用样式

export default {
  name: "CourseDetailView",
  components: { Layout, CourseApplyDialog },
  data() {
    return {
      isMobilePhone: isMobilePhone(),
      courseId: null,
      courseDetail: {},
      videoList: [],
      currentVideoId: null,
      currentVideo: {},
      activeTab: 'videos', // 默认显示视频列表标签页
      videoBlob: null, // 存储转换后的Blob对象
      currentTime: 0,    // 当前播放时间（秒）
      currentDuration: 0, // 视频总时长（秒）
      showApplyDialog: false,
      applyCourse: null,
      currentVideoIndex: 0 // 当前播放视频的索引
    }
  },
  filters: {
    // 将秒转换为时分秒格式
    formatDuration(seconds) {
      if (!seconds || isNaN(seconds)) return '00:00';
      
      // 将秒数转为整数
      seconds = Math.floor(Number(seconds));
      
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      
      // 格式化时间
      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
      } else {
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
      }
    }
  },
  mounted() {
    this.$wxShare();
    this.courseId = this.$route.query.id;
    if (this.courseId) {
      this.getCourseDetail();
      this.getCourseVideos();
    }

    // 添加全局事件监听，防止视频被下载
    window.addEventListener('keydown', this.preventSaveVideo);
    document.addEventListener('contextmenu', this.preventContextMenu);
  },
  methods: {
    // 防止通过键盘保存视频
    preventSaveVideo(e) {
      // 防止Ctrl+S, Cmd+S等保存快捷键
      if ((e.ctrlKey || e.metaKey) && (e.key === 's' || e.key === 'S')) {
        e.preventDefault();
        return false;
      }
    },
    
    // 防止右键菜单
    preventContextMenu(e) {
      // 如果是在视频播放区域右键，阻止默认行为
      const videoContainer = document.querySelector('.video-player-container');
      if (videoContainer && videoContainer.contains(e.target)) {
        e.preventDefault();
        return false;
      }
    },

    // 获取课程详情
    getCourseDetail() {
      let userInfoStr = localStorage.getItem("userInfo") || '{}';
      let userInfo = JSON.parse(userInfoStr);
      this.getRequest(`/cms/course/info?id=${this.courseId}&userId=${userInfo.uid || ''}`).then(resp => {
        if (resp && resp.code == 200) {
          this.courseDetail = resp.data || {};
        }
      });
    },

    // 获取课程视频列表
    getCourseVideos() {
      this.getRequest(`/cms/course/videos?id=${this.courseId}`).then(resp => {
        if (resp && resp.code == 200) {
          this.videoList = resp.data || [];

          // 默认播放第一个视频
          if (this.videoList.length > 0) {
            this.playVideo(this.videoList[0], 0);
          }
        }
      });
    },

    // 播放视频
    playVideo(video, index) {
      this.currentVideoId = video.id;
      this.currentVideo = video;
      if (typeof index !== 'undefined') {
        this.currentVideoIndex = index;
      }
      // 请求接口获取视频详细信息
      this.getRequest(`/cms/course/videos/info?id=${video.id}`).then(resp => {
        if (resp && resp.code == 200) {
          this.currentVideo = resp.data || {};
        }
      });
    },
    // 播放下一个视频
    playNextVideo() {
      const currentIndex = this.videoList.findIndex(v => v.id === this.currentVideoId);
      if (currentIndex > -1 && currentIndex < this.videoList.length - 1) {
        this.playVideo(this.videoList[currentIndex + 1]);
      }
    },

    // 报名课程
    enrollCourse() {
      this.applyCourse = this.courseDetail;
      this.showApplyDialog = true;
    },

    // 关闭报名弹窗
    closeApplyDialog() {
      this.showApplyDialog = false;
      this.applyCourse = null;
    },

    // 报名成功
    applySuccess() {
      this.getCourseDetail();
    },

    // 监听视频播放时间更新
    onTimeUpdate(e) {
      if (e.target) {
        this.currentTime = Math.floor(e.target.currentTime);
        if (!this.currentDuration && e.target.duration) {
          this.currentDuration = Math.floor(e.target.duration);
        }
      }
    },

    // 打开课程封面
    openCourseCover() {
      // 实现打开课程封面的逻辑
    },

    // 下载文件
    downloadFile(fileUrl) {
      if (!fileUrl) {
        this.$message.error('文件链接不存在');
        return;
      }

      // 判断是否登录
      const userInfoStr = localStorage.getItem("userInfo") || '{}';
      const userInfo = JSON.parse(userInfoStr);
      if (!userInfo.uid) {
        this.$message.error('请先登录');
        this.$router.push('/login');
        return;
      }

      this.$message.success('正在下载文件...');
      window.open(fileUrl, '_blank');
    },


  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('keydown', this.preventSaveVideo);
    document.removeEventListener('contextmenu', this.preventContextMenu);
  }
}
</script>

<style lang="less" scoped>
/* 移动端布局 */
.mobile-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

/* 移动端固定视频播放器 */
.mobile-fixed-video {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: black;
}

.mobile-video-player {
  width: 100%;
  height: 250px;
  display: block;
}

/* 移动端返回按钮 */
.mobile-back-btn {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 1001;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
}

.mobile-back-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.mobile-back-btn i {
  font-size: 16px;
}

/* 移动端未报名容器 */
.mobile-no-attend-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  padding: 20px;
  position: relative;
}

/* 移动端固定返回按钮 */
.mobile-back-btn-fixed {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1001;
  width: 40px;
  height: 40px;
  background: rgba(123, 67, 151, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(123, 67, 151, 0.3);
}

.mobile-back-btn-fixed:hover {
  background: rgba(123, 67, 151, 1);
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(123, 67, 151, 0.4);
}

.mobile-back-btn-fixed i {
  font-size: 16px;
}

/* 课程详情页头部样式 */
.course-detail-header {
  background-image: url('https://img.freepik.com/free-photo/abstract-luxury-gradient-blue-background-smooth-dark-blue-with-black-vignette-studio-banner_1258-63452.jpg') !important;
}

/* 课程信息导航栏 */
.course-nav-bar {
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.course-basic-info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 30px;
}

.course-title-container h2 {
  font-size: 22px;
  color: #3a2c58;
  margin-bottom: 10px;
}

.course-stats span {
  display: inline-block;
  margin-right: 20px;
  color: #666;
  font-size: 14px;
}

.course-stats i {
  margin-right: 8px;
  color: #7b4397;
}

.course-price-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.course-price {
  font-size: 24px;
  font-weight: bold;
  color: #dc2430;
}

/* 课程内容区域 */
.course-content-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 移动端固定Tab */
.mobile-fixed-tabs {
  position: fixed;
  top: 250px; /* 视频播放器高度 */
  left: 0;
  right: 0;
  z-index: 999;
  background: white;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-tabs {
  display: flex;
  justify-content: space-around;
  padding: 0;
}

.mobile-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  cursor: pointer;
  position: relative;
  color: #666;
  transition: all 0.3s;
  font-size: 12px;
}

.mobile-tab i {
  font-size: 16px;
  margin-bottom: 4px;
}

.mobile-tab.active {
  color: #7b4397;
}

.mobile-tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: linear-gradient(135deg, #7b4397, #dc2430);
  border-radius: 2px;
}

/* 移动端内容区域 */
.mobile-content-area {
  flex: 1;
  margin-top: 310px; /* 视频播放器高度 + Tab高度 */
  overflow: hidden;
}

/* 移动端视频列表 */
.mobile-video-list {
  height: 100%;
  overflow-y: auto;
  padding: 0;
  scroll-behavior: smooth;
}

.mobile-video-item {
  display: flex;
  padding: 15px;
  background: white;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: all 0.3s;
}

.mobile-video-item:hover {
  background: #f8f9fa;
}

.mobile-video-item.active {
  background: linear-gradient(135deg, rgba(123, 67, 151, 0.05), rgba(220, 36, 48, 0.05));
  border-left: 4px solid #7b4397;
}

.video-thumbnail {
  position: relative;
  width: 120px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 15px;
  flex-shrink: 0;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.video-duration {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.video-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.video-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
  line-height: 1.4;
}

.video-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 移动端课程介绍 */
.mobile-course-info {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  background: white;
}

/* 移动端下载区域 */
.mobile-download-section {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  background: white;
}

/* 移动端考试区域 */
.mobile-exam-section {
  height: 100%;
  overflow: hidden;
}

.mobile-course-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* 视频播放器 */
.video-player-container {
  background: black;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  position: relative;
}

.video-player-container::before {
  /* content: "禁止下载，请在当前页面观看"; */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 0;
  text-align: center;
  font-size: 14px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
}

.video-player-container:hover::before {
  opacity: 1;
}

.video-player {
  width: 100%;
  height: 500px;
}

/* 视频信息区域 */
.video-info-container {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 600px; /* 固定最小高度，避免切换Tab时高度变化 */
  display: flex;
  flex-direction: column;
}

/* 标签页 */
.tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tab {
  padding: 15px 25px;
  cursor: pointer;
  position: relative;
  font-weight: 500;
  color: #666;
  transition: all 0.3s;
}

.tab.active {
  color: #7b4397;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #7b4397, #dc2430);
}

.tab-content {
  padding: 20px;
  flex: 1; /* 占据剩余空间 */
  overflow: hidden; /* 防止内容溢出 */
  display: flex;
  flex-direction: column;
}

/* 视频列表 */
.video-list {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 减小间距 */
  max-height: 480px; /* 固定最大高度 */
  overflow-y: auto; /* 添加滚动 */
  padding-right: 8px; /* 为滚动条留出空间 */
  flex: 1;
}

/* 自定义滚动条样式 */
.video-list::-webkit-scrollbar {
  width: 6px;
}

.video-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.video-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #7b4397, #dc2430);
  border-radius: 3px;
}

.video-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #dc2430, #7b4397);
}

.video-item {
  display: flex;
  align-items: center;
  padding: 12px; /* 减小内边距 */
  border-radius: 8px; /* 减小圆角 */
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s;
  min-height: 60px; /* 固定最小高度 */
  flex-shrink: 0; /* 防止压缩 */
}

.video-item:hover {
  background: #f1f1f1;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.video-item.active {
  background: linear-gradient(135deg, rgba(123, 67, 151, 0.1), rgba(220, 36, 48, 0.1));
  border-left: 4px solid #7b4397;
}

.video-item-index {
  width: 32px; /* 减小尺寸 */
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7b4397, #dc2430);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px; /* 减小间距 */
  font-weight: bold;
  font-size: 14px; /* 减小字体 */
  flex-shrink: 0; /* 防止压缩 */
}

.video-item-info {
  flex: 1;
}

.video-item-info h4 {
  font-size: 15px; /* 稍微减小字体 */
  color: #333;
  margin-bottom: 4px; /* 减小间距 */
  line-height: 1.3; /* 调整行高 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 防止标题过长 */
}

.video-item-info p {
  font-size: 13px; /* 减小字体 */
  color: #666;
  margin: 0;
  line-height: 1.2;
}

.video-item-status {
  color: #7b4397;
  font-size: 16px; /* 减小图标尺寸 */
  flex-shrink: 0; /* 防止压缩 */
}

/* 课程描述 */
.course-description {
  color: #555;
  line-height: 1.6;
  max-height: 480px; /* 与视频列表保持一致的高度 */
  overflow-y: auto; /* 添加滚动 */
  padding-right: 8px; /* 为滚动条留出空间 */
  flex: 1;
  /deep/ img {
    width: 100%;
  }
}

/* 课程描述滚动条样式 */
.course-description::-webkit-scrollbar {
  width: 6px;
}

.course-description::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.course-description::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #7b4397, #dc2430);
  border-radius: 3px;
}

.course-description::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #dc2430, #7b4397);
}
.course-iframe {
  width: 100%;
  height: 480px; /* 与其他内容保持一致的高度 */
  border: none;
  border-radius: 8px;
  flex: 1;
}

.course-cover-img {
  position: relative;
  width: 140px;
  min-width: 100px;
  max-width: 180px;
  height: 100px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0,0,0,0.13);
  margin-right: 32px;
  background: linear-gradient(135deg, #e0c3fc 0%, #8ec5fc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.25s cubic-bezier(.4,2,.6,1);
  border: 2.5px solid #7b4397;
  cursor: pointer;
}
.course-cover-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 14px;
  z-index: 1;
  transition: transform 0.25s cubic-bezier(.4,2,.6,1);
}
.course-cover-img:hover {
  transform: scale(1.045);
  box-shadow: 0 10px 32px rgba(123,67,151,0.18);
}
.course-cover-img .cover-mask {
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  background: linear-gradient(135deg,rgba(123,67,151,0.08),rgba(220,36,48,0.08));
  z-index: 2;
  pointer-events: none;
}
.course-cover-img .cover-label {
  position: absolute;
  right: 8px;
  bottom: 8px;
  background: rgba(123,67,151,0.85);
  color: #fff;
  font-size: 12px;
  padding: 2px 10px;
  border-radius: 10px;
  z-index: 3;
  letter-spacing: 1px;
  box-shadow: 0 2px 8px rgba(123,67,151,0.10);
  user-select: none;
  pointer-events: none;
  font-weight: 500;
}

/* 资料下载样式 */
.download-section {
  padding: 0; /* 移除内边距，由tab-content统一控制 */
  max-height: 480px; /* 与其他内容保持一致的高度 */
  overflow-y: auto; /* 添加滚动 */
  flex: 1;
}

/* 下载区域滚动条样式 */
.download-section::-webkit-scrollbar {
  width: 6px;
}

.download-section::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.download-section::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #7b4397, #dc2430);
  border-radius: 3px;
}

.download-section::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #dc2430, #7b4397);
}

.download-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: #f8f9fa; /* 与视频项保持一致的背景色 */
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;
  transition: all 0.3s;
}

.download-item:hover {
  background: #f1f1f1;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.download-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.download-icon {
  font-size: 32px;
  color: #dc2430;
  margin-right: 15px;
}

.download-details h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 16px;
}

.download-details p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.download-btn {
  background: linear-gradient(135deg, #7b4397, #dc2430);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(123, 67, 151, 0.3);
}

/* 移动端简化的未报名提示 */
.mobile-no-attend-tip {
  text-align: center;
  color: #333;
  background: white;
  border-radius: 12px;
  padding: 40px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  width: 100%;
}

.mobile-no-attend-tip i {
  font-size: 48px;
  color: #7b4397;
  margin-bottom: 20px;
}

.mobile-no-attend-tip h3 {
  font-size: 20px;
  margin: 0 0 15px 0;
  color: #333;
}

.mobile-no-attend-tip p {
  font-size: 28px;
  font-weight: bold;
  color: #dc2430;
  margin: 15px 0 25px 0;
}

.no-data {
  text-align: center;
  padding: 60px 20px; /* 增加内边距 */
  color: #888;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.no-data p {
  margin: 0;
  font-size: 16px;
}

.no-data i {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 15px;
}

/* 响应式适配 */
@media (max-width: 992px) {
  .course-basic-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .course-price-container {
    width: 100%;
    justify-content: space-between;
  }

  .video-player {
    height: 400px;
  }

  .course-cover-img {
    display: none;
  }
}

@media (max-width: 768px) {
  /* 桌面端在移动设备上的样式调整 */
  .section {
    padding: 20px 0;
  }

  .container {
    padding: 0 15px;
  }

  .course-detail-container {
    margin: 0;
  }

  .tabs {
    flex-wrap: wrap;
  }

  .tab {
    padding: 12px 15px;
    font-size: 14px;
  }

  .video-player {
    height: 250px;
    border-radius: 0;
  }

  .video-player-container {
    border-radius: 8px;
    margin-bottom: 15px;
  }

  .course-content-area {
    gap: 15px;
  }

  .video-watermark {
    font-size: 14px;
    bottom: 40px;
    right: 10px;
  }

  .tab-content {
    padding: 15px;
  }

  .video-info-container {
    border-radius: 8px;
  }

  .download-item {
    margin: 0 0 15px 0;
    border-radius: 8px;
  }

  /* 移动端布局样式 */
  .mobile-layout {
    height: 100vh;
  }

  .mobile-video-player {
    height: 200px; /* 在小屏幕上稍微减小高度 */
  }

  .mobile-fixed-tabs {
    top: 200px; /* 对应调整Tab位置 */
  }

  .mobile-content-area {
    margin-top: 260px; /* 对应调整内容区域位置 */
  }

  .video-thumbnail {
    width: 100px;
    height: 70px;
  }

  .video-info h4 {
    font-size: 15px;
  }

  .video-info p {
    font-size: 13px;
  }
}

/* 按钮样式 */
.crystal-btn {
  background: linear-gradient(135deg, #7b4397, #dc2430);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(123, 67, 151, 0.3);
}

.crystal-btn:hover {
  background: linear-gradient(135deg, #dc2430, #7b4397);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(123, 67, 151, 0.4);
}

.crystal-btn i {
  margin-right: 8px;
}

.video-watermark {
  position: absolute;
  bottom: 60px;
  right: 20px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 18px;
  pointer-events: none;
  user-select: none;
  z-index: 10;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  opacity: 0.7;
}

.video-time-info {
  position: absolute;
  bottom: 10px;
  right: 15px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 3px 8px;
  border-radius: 4px;
  z-index: 5;
  pointer-events: none;
}

/* 未报名遮罩提示 */
.no-attend-mask {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}
.no-attend-tip {
  text-align: center;
  color: #888;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 18px rgba(0,0,0,0.08);
  padding: 60px 40px 40px 40px;
  max-width: 400px;
}
.no-attend-tip i {
  font-size: 48px;
  color: #7b4397;
  margin-bottom: 18px;
}
.no-attend-tip p {
  font-size: 18px;
  margin-bottom: 28px;
}

.crystal-btn-secondary {
  background: linear-gradient(135deg, #2bff00, #1900ff);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(180, 180, 180, 0.18);
  margin-left: 10px;
  animation: none;
}
.crystal-btn-secondary:hover {
  background: linear-gradient(135deg, #2bff00, #1900ff);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(180, 180, 180, 0.28);
}

</style>