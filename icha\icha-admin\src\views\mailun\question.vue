<template>
  <div class="divBox relative">
    <el-form :inline="true" :model="dataForm">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="题目名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" show-overflow-tooltip align="center" label="题目名称">
      </el-table-column>
      <el-table-column prop="type" header-align="center" align="center" label="题目类型">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-' + (scope.row.type)">{{
            questionType[scope.row.type].value }}</el-tag>
        </div>
      </el-table-column>
      <el-table-column prop="type" header-align="center" align="center" label="脉轮类型">
        <div slot-scope="scope">
          <el-tag type="primary" :class="'tag-color tag-color-' + (scope.row.mailun)">{{
            mailun[scope.row.mailun].value }}</el-tag>
        </div>
      </el-table-column>
      <!-- <el-table-column prop="charOptionId" header-align="center" align="center" label="正确答案">
      </el-table-column>
      <el-table-column prop="points" header-align="center" align="center" label="自定义分值">
      </el-table-column> -->
      <el-table-column prop="addTime" header-align="center" align="center" label="创建时间">
      </el-table-column>
      <el-table-column prop="updateTime" header-align="center" align="center" label="更新时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="150" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
      <el-pagination :page-sizes="[20, 40, 60, 80]" :page-size="dataForm.limit" :current-page="dataForm.page"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="pageChange" />
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import { QuestionCreateApi, questionUpdateApi, questionDetailApi, questionDeleteApi, questionListApi, } from '@/api/question'
import { questionType,mailun } from '@/data/question'
import AddOrUpdate from './question-add-or-update'
export default {
  data() {
    return {
      questionType,
      mailun,
      dataForm: {
        name: '',
        page: 1,
        limit: 20,
      },
      total: 0,
      dataList: [],
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false
    }
  },
  components: {
    AddOrUpdate
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      questionListApi(this.dataForm).then(res => {
        this.dataList = res.list || [];
        this.total = parseInt(res.total);
        this.dataListLoading = false
      }).catch(() => {
        this.dataList = []
        this.total = 0
        this.dataListLoading = false
      })
    },

    pageChange(page) {
      this.dataForm.page = page
      this.getDataList()
    },
    handleSizeChange(val) {
      this.dataForm.limit = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      // var ids = id ? [id] : this.dataListSelections.map(item => {
      //   return item.id
      // })
      this.$confirm(`确定删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        questionDeleteApi(id).then(() => {
          this.$message.success("删除成功");
          this.getDataList();
        }).catch((res) => {
          this.$message.error(res.message)
        });
      })
    }
  }
}
</script>
