{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone } from \"@/utils/index\";\nimport Message from \"@/utils/message\";\nexport default {\n  name: \"LoginView\",\n  data() {\n    return {\n      redirect: '',\n      isMobilePhone: isMobilePhone(),\n      loading: false,\n      isLoggedIn: false,\n      userInfo: {},\n      rememberMe: false,\n      loginType: 'code',\n      // 'password' 或 'code'\n      loginForm: {\n        username: '',\n        password: ''\n      },\n      codeForm: {\n        phone: '',\n        code: ''\n      },\n      codeSending: false,\n      cooldown: 0,\n      timer: null\n    };\n  },\n  components: {\n    Layout\n  },\n  mounted() {\n    this.redirect = decodeURIComponent(this.$route.query.redirect || '');\n    this.$wxShare();\n    this.checkLoginStatus();\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    switchLoginType(type) {\n      this.loginType = type;\n    },\n    checkLoginStatus() {\n      const token = localStorage.getItem(\"token\");\n      if (token) {\n        try {\n          const userInfoStr = localStorage.getItem(\"userInfo\");\n          if (userInfoStr) {\n            this.userInfo = JSON.parse(userInfoStr);\n            this.isLoggedIn = true;\n          } else {\n            // 有token但没有用户信息，尝试获取用户信息\n            this.getUserInfo();\n          }\n        } catch (error) {\n          console.error(\"解析用户信息失败\", error);\n          this.clearLoginInfo();\n        }\n      }\n    },\n    getUserInfo() {\n      this.getRequest(\"/user\").then(resp => {\n        if (resp && resp.code == 200) {\n          this.userInfo = resp.data;\n          this.isLoggedIn = true;\n          // 更新存储的用户信息\n          sessionStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\n          localStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\n        } else {\n          this.clearLoginInfo();\n        }\n      });\n    },\n    onSubmit() {\n      this.loading = true;\n      // 登录请求\n      this.postRequest(\"/loginCms\", {\n        account: this.loginForm.username,\n        password: this.loginForm.password\n      }).then(resp => {\n        this.loading = false;\n        if (resp && resp.code == 200) {\n          // 根据记住我选项决定存储位置\n          // const storage = this.rememberMe ? localStorage : sessionStorage;\n          // 保存token\n          localStorage.setItem(\"token\", resp.data.token);\n          // 保存用户信息\n          localStorage.setItem(\"userInfo\", JSON.stringify(resp.data.userInfo));\n          this.userInfo = resp.data.userInfo;\n          this.isLoggedIn = true;\n          Message.success(\"登录成功\");\n\n          // 检查是否有重定向URL\n          if (this.redirect) {\n            location.href = this.redirect;\n          } else {\n            // 跳转到首页\n            this.$router.push(\"/\");\n          }\n        } else {\n          Message.error(resp.message || \"登录失败，请检查用户名和密码\");\n        }\n      });\n    },\n    onCodeSubmit() {\n      if (!this.codeForm.phone || !this.codeForm.code) {\n        Message.error(\"请输入手机号和验证码\");\n        return;\n      }\n      this.loading = true;\n      // 验证码登录请求\n      this.postRequest(\"/login/mobile\", {\n        phone: this.codeForm.phone,\n        captcha: this.codeForm.code\n      }).then(resp => {\n        this.loading = false;\n        if (resp && resp.code == 200) {\n          // 保存token\n          localStorage.setItem(\"token\", resp.data.token);\n          // 保存用户信息\n          localStorage.setItem(\"userInfo\", JSON.stringify(resp.data.userInfo));\n          this.userInfo = resp.data.userInfo;\n          this.isLoggedIn = true;\n          Message.success(\"登录成功\");\n\n          // 检查是否有重定向URL\n          if (this.redirect) {\n            location.href = this.redirect;\n          } else {\n            // 跳转到首页\n            this.$router.push(\"/\");\n          }\n        } else {\n          Message.error(resp.message || \"登录失败，请检查手机号和验证码\");\n        }\n      });\n    },\n    getVerificationCode() {\n      if (this.codeSending || this.cooldown > 0) return;\n      const phone = this.codeForm.phone;\n      if (!phone) {\n        Message.error(\"请输入手机号\");\n        return;\n      }\n      if (!/^1[3-9]\\d{9}$/.test(phone)) {\n        Message.error(\"请输入正确的手机号码\");\n        return;\n      }\n      this.codeSending = true;\n\n      // 发送验证码请求\n      this.postRequestParams(\"/sendCode\", {\n        phone: phone\n      }).then(resp => {\n        this.codeSending = false;\n        if (resp && resp.code == 200) {\n          Message.success(\"验证码已发送，请注意查收\");\n          // 开始倒计时\n          this.cooldown = 60;\n          this.timer = setInterval(() => {\n            this.cooldown--;\n            if (this.cooldown <= 0) {\n              clearInterval(this.timer);\n            }\n          }, 1000);\n        } else {\n          Message.error(resp.message || \"验证码发送失败，请稍后重试\");\n        }\n      }).catch(() => {\n        this.codeSending = false;\n        Message.error(\"验证码发送失败，请稍后重试\");\n      });\n    },\n    goToUserCenter() {\n      this.$router.push(\"/user\");\n    },\n    logout() {\n      // 退出登录请求\n      this.postRequest(\"/logout\").finally(() => {\n        this.clearLoginInfo();\n        Message.success(\"已退出登录\");\n        // 刷新页面\n        this.isLoggedIn = false;\n        this.userInfo = {};\n        location.reload();\n      });\n    },\n    clearLoginInfo() {\n      // 清除所有存储的登录信息\n      sessionStorage.removeItem(\"token\");\n      sessionStorage.removeItem(\"userInfo\");\n      localStorage.removeItem(\"token\");\n      localStorage.removeItem(\"userInfo\");\n      this.isLoggedIn = false;\n      this.userInfo = {};\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "Message", "name", "data", "redirect", "loading", "isLoggedIn", "userInfo", "rememberMe", "loginType", "loginForm", "username", "password", "codeForm", "phone", "code", "codeSending", "cooldown", "timer", "components", "mounted", "decodeURIComponent", "$route", "query", "$wxShare", "checkLoginStatus", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "switchLoginType", "type", "token", "localStorage", "getItem", "userInfoStr", "JSON", "parse", "getUserInfo", "error", "console", "clearLoginInfo", "getRequest", "then", "resp", "sessionStorage", "setItem", "stringify", "onSubmit", "postRequest", "account", "success", "location", "href", "$router", "push", "message", "onCodeSubmit", "<PERSON><PERSON>a", "getVerificationCode", "test", "postRequestParams", "setInterval", "catch", "goToUserCenter", "logout", "finally", "reload", "removeItem"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t<div class=\"login-page\">\r\n\t\t<div class=\"login-container\">\r\n\t\t\t<div class=\"login-left\">\r\n\t\t\t\t<div class=\"login-logo\">\r\n\t\t\t\t\t<img src=\"@/assets/images/logo.png\" alt=\"国际水晶疗愈协会\" class=\"logo-image\" />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"login-welcome\">\r\n\t\t\t\t\t<h2>欢迎来到</h2>\r\n\t\t\t\t\t<h1>国际水晶疗愈协会</h1>\r\n\t\t\t\t\t<p>登录后享受专业的水晶疗愈服务和丰富的学习资源</p>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"login-decoration\">\r\n\t\t\t\t\t<div class=\"crystal-1\"></div>\r\n\t\t\t\t\t<div class=\"crystal-2\"></div>\r\n\t\t\t\t\t<div class=\"crystal-3\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<div class=\"login-right\">\r\n\t\t\t\t<div class=\"login-form-container\" v-if=\"!isLoggedIn\">\r\n\t\t\t\t\t<h2 class=\"login-title\">用户登录</h2>\r\n\t\t\t\t\t<p class=\"login-subtitle\">欢迎回来，请登录您的账号</p>\r\n                    \r\n                    <div class=\"login-tabs\">\r\n                        <div \r\n                            :class=\"['tab-item', { active: loginType === 'code' }]\" \r\n                            @click=\"switchLoginType('code')\"\r\n                        >\r\n                            验证码登录\r\n                        </div>\r\n                        <div \r\n                            :class=\"['tab-item', { active: loginType === 'password' }]\" \r\n                            @click=\"switchLoginType('password')\"\r\n                        >\r\n                            账号密码登录\r\n                        </div>\r\n                    </div>\r\n\r\n\t\t\t\t\t<van-form @submit=\"onSubmit\" v-if=\"loginType === 'password'\">\r\n\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t<label for=\"username\">用户名/手机号</label>\r\n\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\tv-model=\"loginForm.username\"\r\n\t\t\t\t\t\t\t\tname=\"username\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入用户名或手机号\"\r\n\t\t\t\t\t\t\t\t:rules=\"[{ required: true, message: '请输入用户名/手机号' }]\"\r\n\t\t\t\t\t\t\t\tclass=\"login-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t<label for=\"password\">密码</label>\r\n\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\tv-model=\"loginForm.password\"\r\n\t\t\t\t\t\t\t\ttype=\"password\"\r\n\t\t\t\t\t\t\t\tname=\"password\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入密码\"\r\n\t\t\t\t\t\t\t\t:rules=\"[{ required: true, message: '请输入密码' }]\"\r\n\t\t\t\t\t\t\t\tclass=\"login-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"login-options\">\r\n\t\t\t\t\t\t\t<div class=\"remember-me\">\r\n\t\t\t\t\t\t\t\t<van-checkbox v-model=\"rememberMe\">记住我</van-checkbox>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<!-- <router-link to=\"/forgotPassword\" class=\"forgot-password\">忘记密码？</router-link> -->\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t<van-button round block type=\"primary\" native-type=\"submit\" :loading=\"loading\">\r\n\t\t\t\t\t\t\t\t登录\r\n\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</van-form>\r\n\r\n                    <van-form @submit=\"onCodeSubmit\" v-else>\r\n\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t<label for=\"phone\">手机号</label>\r\n\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\tv-model=\"codeForm.phone\"\r\n\t\t\t\t\t\t\t\tname=\"phone\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入手机号\"\r\n\t\t\t\t\t\t\t\t:rules=\"[\r\n                                    { required: true, message: '请输入手机号' },\r\n                                    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码' }\r\n                                ]\"\r\n\t\t\t\t\t\t\t\tclass=\"login-input\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t<label for=\"code\">验证码</label>\r\n                            <div class=\"code-field\">\r\n                                <van-field\r\n                                    v-model=\"codeForm.code\"\r\n                                    name=\"code\"\r\n                                    placeholder=\"请输入验证码\"\r\n                                    :rules=\"[{ required: true, message: '请输入验证码' }]\"\r\n                                    class=\"login-input code-input\"\r\n                                />\r\n                                <van-button \r\n                                    size=\"small\" \r\n                                    type=\"primary\" \r\n                                    class=\"code-button\" \r\n                                    :disabled=\"codeSending || cooldown > 0\"\r\n                                    @click.prevent=\"getVerificationCode\"\r\n                                >\r\n                                    {{ cooldown > 0 ? `${cooldown}秒后重新获取` : '获取验证码' }}\r\n                                </van-button>\r\n                            </div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t<van-button round block type=\"primary\" native-type=\"submit\" :loading=\"loading\">\r\n\t\t\t\t\t\t\t\t登录\r\n\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</van-form>\r\n\r\n\t\t\t\t\t<div class=\"register-link\">\r\n\t\t\t\t\t\t<span>还没有账号？</span>\r\n\t\t\t\t\t\t<router-link to=\"/register\">立即注册</router-link>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"logged-in-container\" v-else>\r\n\t\t\t\t\t<div class=\"user-info\">\r\n\t\t\t\t\t\t<img :src=\"userInfo.avatar || require('@/assets/images/pattern-dark.png')\" alt=\"头像\" class=\"user-avatar\">\r\n\t\t\t\t\t\t<h3 class=\"user-name\">{{ userInfo.nickname || userInfo.nickName || userInfo.username }}</h3>\r\n\t\t\t\t\t\t<p class=\"user-role\">{{ userInfo.phone || '暂无手机号' }}</p>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"action-buttons\">\r\n\t\t\t\t\t\t<!-- <van-button round block type=\"primary\" @click=\"goToUserCenter\">\r\n\t\t\t\t\t\t\t进入个人中心\r\n\t\t\t\t\t\t</van-button> -->\r\n\t\t\t\t\t\t<van-button round block plain @click=\"logout\" class=\"logout-btn\">\r\n\t\t\t\t\t\t\t退出登录\r\n\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport Message from \"@/utils/message\";\r\n\r\nexport default {\r\n\tname: \"LoginView\",\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tredirect: '',\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tloading: false,\r\n\t\t\tisLoggedIn: false,\r\n\t\t\tuserInfo: {},\r\n\t\t\trememberMe: false,\r\n            loginType: 'code', // 'password' 或 'code'\r\n\t\t\tloginForm: {\r\n\t\t\t\tusername: '',\r\n\t\t\t\tpassword: ''\r\n\t\t\t},\r\n            codeForm: {\r\n                phone: '',\r\n                code: ''\r\n            },\r\n            codeSending: false,\r\n            cooldown: 0,\r\n            timer: null\r\n\t\t}\r\n\t},\r\n\tcomponents: {\r\n\t\tLayout\r\n\t},\r\n\tmounted() {\r\n\t\tthis.redirect = decodeURIComponent(this.$route.query.redirect || '');\r\n\t\tthis.$wxShare();\r\n\t\tthis.checkLoginStatus();\r\n\t},\r\n    beforeDestroy() {\r\n        if (this.timer) {\r\n            clearInterval(this.timer);\r\n        }\r\n    },\r\n\tmethods: {\r\n        switchLoginType(type) {\r\n            this.loginType = type;\r\n        },\r\n\t\tcheckLoginStatus() {\r\n\t\t\tconst token = localStorage.getItem(\"token\");\r\n\t\t\tif (token) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst userInfoStr = localStorage.getItem(\"userInfo\") ;\r\n\t\t\t\t\tif (userInfoStr) {\r\n\t\t\t\t\t\tthis.userInfo = JSON.parse(userInfoStr);\r\n\t\t\t\t\t\tthis.isLoggedIn = true;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 有token但没有用户信息，尝试获取用户信息\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"解析用户信息失败\", error);\r\n\t\t\t\t\tthis.clearLoginInfo();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetUserInfo() {\r\n\t\t\tthis.getRequest(\"/user\").then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.userInfo = resp.data;\r\n\t\t\t\t\tthis.isLoggedIn = true;\r\n\t\t\t\t\t// 更新存储的用户信息\r\n\t\t\t\t\tsessionStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\r\n\t\t\t\t\tlocalStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.clearLoginInfo();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tonSubmit() {\r\n\t\t\tthis.loading = true;\r\n\t\t\t// 登录请求\r\n\t\t\tthis.postRequest(\"/loginCms\", {\r\n\t\t\t\taccount: this.loginForm.username,\r\n\t\t\t\tpassword: this.loginForm.password\r\n\t\t\t})\r\n\t\t\t\t.then(resp => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\t\t// 根据记住我选项决定存储位置\r\n\t\t\t\t\t\t// const storage = this.rememberMe ? localStorage : sessionStorage;\r\n\t\t\t\t\t\t// 保存token\r\n\t\t\t\t\t\tlocalStorage.setItem(\"token\", resp.data.token);\r\n\t\t\t\t\t\t// 保存用户信息\r\n\t\t\t\t\t\tlocalStorage.setItem(\"userInfo\", JSON.stringify(resp.data.userInfo));\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.userInfo = resp.data.userInfo;\r\n\t\t\t\t\t\tthis.isLoggedIn = true;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tMessage.success(\"登录成功\");\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 检查是否有重定向URL\r\n\t\t\t\t\t\tif (this.redirect) {\r\n\t\t\t\t\t\t\tlocation.href = this.redirect;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 跳转到首页\r\n\t\t\t\t\t\t\tthis.$router.push(\"/\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tMessage.error(resp.message || \"登录失败，请检查用户名和密码\");\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n        onCodeSubmit() {\r\n            if (!this.codeForm.phone || !this.codeForm.code) {\r\n                Message.error(\"请输入手机号和验证码\");\r\n                return;\r\n            }\r\n\r\n            this.loading = true;\r\n            // 验证码登录请求\r\n            this.postRequest(\"/login/mobile\", {\r\n                phone: this.codeForm.phone,\r\n                captcha: this.codeForm.code\r\n            })\r\n            .then(resp => {\r\n                this.loading = false;\r\n                if (resp && resp.code == 200) {\r\n                    // 保存token\r\n                    localStorage.setItem(\"token\", resp.data.token);\r\n                    // 保存用户信息\r\n                    localStorage.setItem(\"userInfo\", JSON.stringify(resp.data.userInfo));\r\n                    \r\n                    this.userInfo = resp.data.userInfo;\r\n                    this.isLoggedIn = true;\r\n                    \r\n                    Message.success(\"登录成功\");\r\n                    \r\n                    // 检查是否有重定向URL\r\n                    if (this.redirect) {\r\n\t\t\t\t\t\tlocation.href = this.redirect;\r\n                    } else {\r\n                        // 跳转到首页\r\n                        this.$router.push(\"/\");\r\n                    }\r\n                } else {\r\n                    Message.error(resp.message || \"登录失败，请检查手机号和验证码\");\r\n                }\r\n            });\r\n        },\r\n        getVerificationCode() {\r\n            if (this.codeSending || this.cooldown > 0) return;\r\n            \r\n            const phone = this.codeForm.phone;\r\n            if (!phone) {\r\n                Message.error(\"请输入手机号\");\r\n                return;\r\n            }\r\n            \r\n            if (!/^1[3-9]\\d{9}$/.test(phone)) {\r\n                Message.error(\"请输入正确的手机号码\");\r\n                return;\r\n            }\r\n            \r\n            this.codeSending = true;\r\n            \r\n            // 发送验证码请求\r\n            this.postRequestParams(\"/sendCode\", {\r\n                phone: phone,\r\n            })\r\n            .then(resp => {\r\n                this.codeSending = false;\r\n                if (resp && resp.code == 200) {\r\n                    Message.success(\"验证码已发送，请注意查收\");\r\n                    // 开始倒计时\r\n                    this.cooldown = 60;\r\n                    this.timer = setInterval(() => {\r\n                        this.cooldown--;\r\n                        if (this.cooldown <= 0) {\r\n                            clearInterval(this.timer);\r\n                        }\r\n                    }, 1000);\r\n                } else {\r\n                    Message.error(resp.message || \"验证码发送失败，请稍后重试\");\r\n                }\r\n            })\r\n            .catch(() => {\r\n                this.codeSending = false;\r\n                Message.error(\"验证码发送失败，请稍后重试\");\r\n            });\r\n        },\r\n\t\tgoToUserCenter() {\r\n\t\t\tthis.$router.push(\"/user\");\r\n\t\t},\r\n\t\tlogout() {\r\n\t\t\t// 退出登录请求\r\n\t\t\tthis.postRequest(\"/logout\").finally(() => {\r\n\t\t\t\tthis.clearLoginInfo();\r\n\t\t\t\tMessage.success(\"已退出登录\");\r\n\t\t\t\t// 刷新页面\r\n\t\t\t\tthis.isLoggedIn = false;\r\n\t\t\t\tthis.userInfo = {};\r\n\t\t\t\tlocation.reload();\r\n\t\t\t});\r\n\t\t},\r\n\t\tclearLoginInfo() {\r\n\t\t\t// 清除所有存储的登录信息\r\n\t\t\tsessionStorage.removeItem(\"token\");\r\n\t\t\tsessionStorage.removeItem(\"userInfo\");\r\n\t\t\tlocalStorage.removeItem(\"token\");\r\n\t\t\tlocalStorage.removeItem(\"userInfo\");\r\n\t\t\tthis.isLoggedIn = false;\r\n\t\t\tthis.userInfo = {};\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.login-page {\r\n\tmin-height: 100vh;\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground-color: #f8f9fa;\r\n\tbackground-image: url('@/assets/images/pattern-dark.png');\r\n\tbackground-repeat: repeat;\r\n\tpadding: 40px 20px;\r\n}\r\n\r\n.login-container {\r\n\tdisplay: flex;\r\n\twidth: 1200px;\r\n\tmin-height: 680px;\r\n\tborder-radius: 20px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.login-left {\r\n\tflex: 1;\r\n\tbackground: linear-gradient(135deg, #5e258f, #8647ad);\r\n\tbackground-image: linear-gradient(135deg, rgba(94, 37, 143, 0.95), rgba(134, 71, 173, 0.9)), url('https://images.unsplash.com/photo-1521320226546-87b106cd055d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');\r\n\tbackground-size: cover;\r\n\tbackground-position: center;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tposition: relative;\r\n\tcolor: #fff;\r\n\tpadding: 50px;\r\n\toverflow: hidden;\r\n}\r\n\r\n.login-logo {\r\n\tmargin-bottom: 40px;\r\n\ttext-align: center;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.logo-image {\r\n\twidth: 180px;\r\n\theight: auto;\r\n}\r\n\r\n.login-welcome {\r\n\ttext-align: center;\r\n\tmargin-bottom: 40px;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.login-welcome h2 {\r\n\tfont-size: 24px;\r\n\tfont-weight: 400;\r\n\tmargin-bottom: 20px;\r\n\tcolor: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.login-welcome h1 {\r\n\tfont-size: 38px;\r\n\tfont-weight: 700;\r\n\tmargin-bottom: 20px;\r\n\ttext-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.login-welcome p {\r\n\tfont-size: 16px;\r\n\tline-height: 1.6;\r\n\tmax-width: 400px;\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.login-decoration {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.crystal-1, .crystal-2, .crystal-3 {\r\n\tposition: absolute;\r\n\tbackground: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));\r\n\tbackdrop-filter: blur(5px);\r\n\tborder: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.crystal-1 {\r\n\twidth: 300px;\r\n\theight: 300px;\r\n\ttop: -100px;\r\n\tleft: -150px;\r\n}\r\n\r\n.crystal-2 {\r\n\twidth: 200px;\r\n\theight: 200px;\r\n\tbottom: 50px;\r\n\tright: -50px;\r\n}\r\n\r\n.crystal-3 {\r\n\twidth: 150px;\r\n\theight: 150px;\r\n\tbottom: -50px;\r\n\tleft: 100px;\r\n}\r\n\r\n.login-right {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 60px;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.login-form-container, .logged-in-container {\r\n\twidth: 100%;\r\n\tmax-width: 400px;\r\n}\r\n\r\n.login-title {\r\n\tfont-size: 32px;\r\n\tfont-weight: 700;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10px;\r\n\ttext-align: center;\r\n}\r\n\r\n.login-subtitle {\r\n\tfont-size: 16px;\r\n\tcolor: #666;\r\n\tmargin-bottom: 40px;\r\n\ttext-align: center;\r\n}\r\n\r\n.login-tabs {\r\n    display: flex;\r\n    margin-bottom: 30px;\r\n    border-bottom: 1px solid #e8eaee;\r\n}\r\n\r\n.tab-item {\r\n    flex: 1;\r\n    text-align: center;\r\n    padding: 15px 0;\r\n    font-size: 16px;\r\n    color: #666;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    position: relative;\r\n}\r\n\r\n.tab-item.active {\r\n    color: #5e258f;\r\n    font-weight: 500;\r\n}\r\n\r\n.tab-item.active:after {\r\n    content: \"\";\r\n    position: absolute;\r\n    bottom: -1px;\r\n    left: 20%;\r\n    width: 60%;\r\n    height: 3px;\r\n    background: #5e258f;\r\n    border-radius: 3px 3px 0 0;\r\n}\r\n\r\n.form-item {\r\n\tmargin-bottom: 25px;\r\n}\r\n\r\n.form-item label {\r\n\tdisplay: block;\r\n\tmargin-bottom: 8px;\r\n\tfont-size: 14px;\r\n\tcolor: #555;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.login-input {\r\n\t/deep/ .van-field__control {\r\n\t\theight: 48px;\r\n\t\tpadding: 0 15px;\r\n\t\tfont-size: 15px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder: 1px solid #e8eaee;\r\n\t\tborder-radius: 8px;\r\n\t}\r\n\r\n\t/deep/ .van-field__control:focus {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-color: #5e258f;\r\n\t\tbox-shadow: 0 0 0 3px rgba(94, 37, 143, 0.1);\r\n\t}\r\n}\r\n\r\n.code-field {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.code-input {\r\n    flex: 1;\r\n    margin-right: 10px;\r\n}\r\n\r\n.code-button {\r\n    white-space: nowrap;\r\n    height: 48px;\r\n    background: linear-gradient(45deg, #5e258f, #8647ad);\r\n    border-color: #5e258f;\r\n    \r\n    &:disabled {\r\n        background: #cccccc;\r\n        border-color: #bbbbbb;\r\n        color: #ffffff;\r\n    }\r\n}\r\n\r\n.login-options {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 30px;\r\n}\r\n\r\n.remember-me {\r\n\t/deep/ .van-checkbox__label {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t/deep/ .van-checkbox__icon--checked {\r\n\t\tbackground-color: #5e258f;\r\n\t\tborder-color: #5e258f;\r\n\t}\r\n}\r\n\r\n.forgot-password {\r\n\tcolor: #5e258f;\r\n\tfont-size: 14px;\r\n\ttext-decoration: none;\r\n\ttransition: color 0.2s;\r\n\t\r\n\t&:hover {\r\n\t\tcolor: #8647ad;\r\n\t\ttext-decoration: underline;\r\n\t}\r\n}\r\n\r\n.form-submit {\r\n\tmargin-bottom: 30px;\r\n\t\r\n\t.van-button {\r\n\t\theight: 50px;\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 500;\r\n\t\tbackground: linear-gradient(45deg, #5e258f, #8647ad);\r\n\t\tborder-color: #5e258f;\r\n\t\ttransition: all 0.3s ease;\r\n\t\t\r\n\t\t&:hover {\r\n\t\t\tbackground: linear-gradient(45deg, #4b1e73, #733c94);\r\n\t\t\tborder-color: #4b1e73;\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 6px 15px rgba(94, 37, 143, 0.25);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.register-link {\r\n\ttext-align: center;\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n\t\r\n\ta {\r\n\t\tcolor: #5e258f;\r\n\t\tfont-weight: 500;\r\n\t\ttext-decoration: none;\r\n\t\tmargin-left: 5px;\r\n\t\t\r\n\t\t&:hover {\r\n\t\t\ttext-decoration: underline;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.logged-in-container {\r\n\ttext-align: center;\r\n\tpadding: 20px 0;\r\n\t\r\n\t.user-info {\r\n\t\tmargin-bottom: 40px;\r\n\t\t\r\n\t\t.user-avatar {\r\n\t\t\twidth: 110px;\r\n\t\t\theight: 110px;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tobject-fit: cover;\r\n\t\t\tborder: 4px solid rgba(94, 37, 143, 0.2);\r\n\t\t\tmargin-bottom: 20px;\r\n\t\t}\r\n\t\t\r\n\t\t.user-name {\r\n\t\t\tfont-size: 24px;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tmargin-bottom: 8px;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t\t\r\n\t\t.user-role {\r\n\t\t\tfont-size: 16px;\r\n\t\t\tcolor: #666;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.action-buttons {\r\n\t\t.van-button {\r\n\t\t\tmargin-bottom: 16px;\r\n\t\t\theight: 50px;\r\n\t\t\tfont-size: 16px;\r\n\t\t\t\r\n\t\t\t&--primary {\r\n\t\t\t\tbackground: linear-gradient(45deg, #5e258f, #8647ad);\r\n\t\t\t\tborder-color: #5e258f;\r\n\t\t\t\t\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground: linear-gradient(45deg, #4b1e73, #733c94);\r\n\t\t\t\t\tborder-color: #4b1e73;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.logout-btn {\r\n\t\t\tborder-color: #f56c6c;\r\n\t\t\tcolor: #f56c6c;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground-color: rgba(245, 108, 108, 0.05);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n\t.login-container {\r\n\t\twidth: 95%;\r\n\t\tflex-direction: column;\r\n\t\tmin-height: auto;\r\n\t}\r\n\t\r\n\t.login-left {\r\n\t\tpadding: 40px 20px;\r\n\t}\r\n\t\r\n\t.login-right {\r\n\t\tpadding: 40px 20px;\r\n\t}\r\n\t\r\n\t.login-welcome h1 {\r\n\t\tfont-size: 32px;\r\n\t}\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\t.login-page {\r\n\t\tpadding: 20px 10px;\r\n\t}\r\n\t\r\n\t.login-container {\r\n\t\twidth: 100%;\r\n\t\tborder-radius: 10px;\r\n\t}\r\n\t\r\n\t.login-left {\r\n\t\tpadding: 30px 15px;\r\n\t}\r\n\t\r\n\t.login-right {\r\n\t\tpadding: 30px 15px;\r\n\t}\r\n\t\r\n\t.login-logo .logo-image {\r\n\t\twidth: 150px;\r\n\t}\r\n\t\r\n\t.login-welcome h1 {\r\n\t\tfont-size: 28px;\r\n\t}\r\n\t\r\n\t.login-welcome p {\r\n\t\tfont-size: 14px;\r\n\t}\r\n\t\r\n\t.login-title {\r\n\t\tfont-size: 28px;\r\n\t}\r\n\t\r\n\t.login-subtitle {\r\n\t\tfont-size: 14px;\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsJA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MACAJ,aAAA,EAAAA,aAAA;MACAK,OAAA;MACAC,UAAA;MACAC,QAAA;MACAC,UAAA;MACAC,SAAA;MAAA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,QAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACAC,WAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,UAAA;IACApB;EACA;EACAqB,QAAA;IACA,KAAAhB,QAAA,GAAAiB,kBAAA,MAAAC,MAAA,CAAAC,KAAA,CAAAnB,QAAA;IACA,KAAAoB,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,cAAA;IACA,SAAAR,KAAA;MACAS,aAAA,MAAAT,KAAA;IACA;EACA;EACAU,OAAA;IACAC,gBAAAC,IAAA;MACA,KAAArB,SAAA,GAAAqB,IAAA;IACA;IACAL,iBAAA;MACA,MAAAM,KAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,IAAAF,KAAA;QACA;UACA,MAAAG,WAAA,GAAAF,YAAA,CAAAC,OAAA;UACA,IAAAC,WAAA;YACA,KAAA3B,QAAA,GAAA4B,IAAA,CAAAC,KAAA,CAAAF,WAAA;YACA,KAAA5B,UAAA;UACA;YACA;YACA,KAAA+B,WAAA;UACA;QACA,SAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,aAAAA,KAAA;UACA,KAAAE,cAAA;QACA;MACA;IACA;IACAH,YAAA;MACA,KAAAI,UAAA,UAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA5B,IAAA;UACA,KAAAR,QAAA,GAAAoC,IAAA,CAAAxC,IAAA;UACA,KAAAG,UAAA;UACA;UACAsC,cAAA,CAAAC,OAAA,aAAAV,IAAA,CAAAW,SAAA,CAAAH,IAAA,CAAAxC,IAAA;UACA6B,YAAA,CAAAa,OAAA,aAAAV,IAAA,CAAAW,SAAA,CAAAH,IAAA,CAAAxC,IAAA;QACA;UACA,KAAAqC,cAAA;QACA;MACA;IACA;IACAO,SAAA;MACA,KAAA1C,OAAA;MACA;MACA,KAAA2C,WAAA;QACAC,OAAA,OAAAvC,SAAA,CAAAC,QAAA;QACAC,QAAA,OAAAF,SAAA,CAAAE;MACA,GACA8B,IAAA,CAAAC,IAAA;QACA,KAAAtC,OAAA;QACA,IAAAsC,IAAA,IAAAA,IAAA,CAAA5B,IAAA;UACA;UACA;UACA;UACAiB,YAAA,CAAAa,OAAA,UAAAF,IAAA,CAAAxC,IAAA,CAAA4B,KAAA;UACA;UACAC,YAAA,CAAAa,OAAA,aAAAV,IAAA,CAAAW,SAAA,CAAAH,IAAA,CAAAxC,IAAA,CAAAI,QAAA;UAEA,KAAAA,QAAA,GAAAoC,IAAA,CAAAxC,IAAA,CAAAI,QAAA;UACA,KAAAD,UAAA;UAEAL,OAAA,CAAAiD,OAAA;;UAEA;UACA,SAAA9C,QAAA;YACA+C,QAAA,CAAAC,IAAA,QAAAhD,QAAA;UACA;YACA;YACA,KAAAiD,OAAA,CAAAC,IAAA;UACA;QACA;UACArD,OAAA,CAAAqC,KAAA,CAAAK,IAAA,CAAAY,OAAA;QACA;MACA;IACA;IACAC,aAAA;MACA,UAAA3C,QAAA,CAAAC,KAAA,UAAAD,QAAA,CAAAE,IAAA;QACAd,OAAA,CAAAqC,KAAA;QACA;MACA;MAEA,KAAAjC,OAAA;MACA;MACA,KAAA2C,WAAA;QACAlC,KAAA,OAAAD,QAAA,CAAAC,KAAA;QACA2C,OAAA,OAAA5C,QAAA,CAAAE;MACA,GACA2B,IAAA,CAAAC,IAAA;QACA,KAAAtC,OAAA;QACA,IAAAsC,IAAA,IAAAA,IAAA,CAAA5B,IAAA;UACA;UACAiB,YAAA,CAAAa,OAAA,UAAAF,IAAA,CAAAxC,IAAA,CAAA4B,KAAA;UACA;UACAC,YAAA,CAAAa,OAAA,aAAAV,IAAA,CAAAW,SAAA,CAAAH,IAAA,CAAAxC,IAAA,CAAAI,QAAA;UAEA,KAAAA,QAAA,GAAAoC,IAAA,CAAAxC,IAAA,CAAAI,QAAA;UACA,KAAAD,UAAA;UAEAL,OAAA,CAAAiD,OAAA;;UAEA;UACA,SAAA9C,QAAA;YACA+C,QAAA,CAAAC,IAAA,QAAAhD,QAAA;UACA;YACA;YACA,KAAAiD,OAAA,CAAAC,IAAA;UACA;QACA;UACArD,OAAA,CAAAqC,KAAA,CAAAK,IAAA,CAAAY,OAAA;QACA;MACA;IACA;IACAG,oBAAA;MACA,SAAA1C,WAAA,SAAAC,QAAA;MAEA,MAAAH,KAAA,QAAAD,QAAA,CAAAC,KAAA;MACA,KAAAA,KAAA;QACAb,OAAA,CAAAqC,KAAA;QACA;MACA;MAEA,qBAAAqB,IAAA,CAAA7C,KAAA;QACAb,OAAA,CAAAqC,KAAA;QACA;MACA;MAEA,KAAAtB,WAAA;;MAEA;MACA,KAAA4C,iBAAA;QACA9C,KAAA,EAAAA;MACA,GACA4B,IAAA,CAAAC,IAAA;QACA,KAAA3B,WAAA;QACA,IAAA2B,IAAA,IAAAA,IAAA,CAAA5B,IAAA;UACAd,OAAA,CAAAiD,OAAA;UACA;UACA,KAAAjC,QAAA;UACA,KAAAC,KAAA,GAAA2C,WAAA;YACA,KAAA5C,QAAA;YACA,SAAAA,QAAA;cACAU,aAAA,MAAAT,KAAA;YACA;UACA;QACA;UACAjB,OAAA,CAAAqC,KAAA,CAAAK,IAAA,CAAAY,OAAA;QACA;MACA,GACAO,KAAA;QACA,KAAA9C,WAAA;QACAf,OAAA,CAAAqC,KAAA;MACA;IACA;IACAyB,eAAA;MACA,KAAAV,OAAA,CAAAC,IAAA;IACA;IACAU,OAAA;MACA;MACA,KAAAhB,WAAA,YAAAiB,OAAA;QACA,KAAAzB,cAAA;QACAvC,OAAA,CAAAiD,OAAA;QACA;QACA,KAAA5C,UAAA;QACA,KAAAC,QAAA;QACA4C,QAAA,CAAAe,MAAA;MACA;IACA;IACA1B,eAAA;MACA;MACAI,cAAA,CAAAuB,UAAA;MACAvB,cAAA,CAAAuB,UAAA;MACAnC,YAAA,CAAAmC,UAAA;MACAnC,YAAA,CAAAmC,UAAA;MACA,KAAA7D,UAAA;MACA,KAAAC,QAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}