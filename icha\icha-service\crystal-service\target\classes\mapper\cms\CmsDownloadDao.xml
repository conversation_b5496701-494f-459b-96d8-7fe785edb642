<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.crystal.service.dao.CmsDownloadDao">

    <!-- 根据包名 模块名 以及类名 生成Mapper XML 配置文件 -->
    <resultMap type="com.crystal.common.model.cms.CmsDownloadEntity" id="cmsDownloadMap">
        <result property="id" column="id"/>
        <result property="addTime" column="add_time"/>
        <result property="isDel" column="is_del"/>
        <result property="title" column="title"/>
        <result property="brief" column="brief"/>
        <result property="fileType" column="file_type"/>
        <result property="tags" column="tags"/>
        <result property="fileUrl" column="file_url"/>
        <result property="fileSize" column="file_size"/>
        <result property="downloads" column="downloads"/>
    </resultMap>

</mapper> 