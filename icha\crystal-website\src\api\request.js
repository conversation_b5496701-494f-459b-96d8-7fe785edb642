import axios from "axios";
import Message from "@/utils/message";
import router from "@/router";
let base = "api/front";
const service = axios.create({
  baseURL: base,
  timeout: 60000, // 过期时间
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    // 发送请求之前做的
    const token = localStorage.getItem("token");
    if (token) {
      config.headers["Authori-zation"] = token;
    }
    if (/get/i.test(config.method)) {
      config.params = config.params || {};
      config.params.temp = Date.parse(new Date()) / 1000;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  (response) => {
      const res = response.data;
    // if the custom code is not 20000, it is judged as an error.
    if (res.code === 401) {
      // to re-login
      Message.error("无效的会话，或者登录已过期，请重新登录。");
      // 
      router.push("/login?redirect=" + encodeURIComponent(window.location.href));
    } else if (res.code === 403) {
      Message.error("没有权限访问。");
    }
    if (res.code != 200 && res.code != 401) {
      Message.error(res.message || "Error");
      return res;
    } else {
      return res;
    }
  },
  (error) => {
    Message.error(error.message);
    return Promise.reject(error);
  }
);

export default service;
