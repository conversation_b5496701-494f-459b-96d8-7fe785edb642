<template>
	<Layout>
		<div class="layout-container" style="width: 100%">
			<!-- 美化后的页面头部 -->
			<div class="hero-header-section course-header">
				<div class="hero-content">
					<h1 class="hero-title"><i class="fa fa-graduation-cap fa-spin-pulse"></i> 认证课程</h1>
					<p class="hero-subtitle">系统学习水晶疗愈，成为专业认证疗愈师</p>
				</div>
			</div>
		</div>

		<div class="section">
			<div class="container" style="max-width: 1160px">
				<!-- 搜索区域 -->
				<div class="course-search">
					<div class="search-container">
						<input type="text" v-model="searchKeyword" placeholder="搜索课程名称或关键词" class="search-input" />
						<button class="search-btn" @click="searchCourses">搜索</button>
					</div>
				</div>

				<!-- 课程列表 -->
				<div class="crystal-course-container">
					<div class="crystal-grid" :class="{'crystal-grid-mobile': isMobilePhone}">
						<div v-for="(course, index) in courseList" :key="index" class="crystal-course-card">
							<div class="crystal-course-img">
								<img :src="course.cover" alt="">
								<div class="course-overlay"></div>
							</div>
							<div class="crystal-course-info">
								<h3>{{course.title}}</h3>
								<p>{{course.brief}}</p>
								<!-- <div class="crystal-course-meta">
									<span><i class="fa fa-calendar"></i> {{course.date}}</span>
									<span><i class="fa fa-map-marker"></i> {{course.location}}</span>
								</div> -->
								<div class="crystal-course-footer">
									<span class="crystal-course-price">¥{{course.price}}</span>
									<button class="crystal-btn pulse-btn" @click="showApply(course)" v-if="!course.isAttend">立即报名</button>
									<button class="crystal-btn crystal-btn-secondary" @click="goCourseDetail(course.id)" v-else>观看课程</button>
								</div>
							</div>
						</div>
					</div>

					<!-- 无数据提示 -->
					<div v-if="courseList.length == 0" class="no-data">
						<p>暂无课程数据</p>
					</div>

					<!-- 分页 -->
					<ul class="am-pagination" style="text-align: center;" v-if="total > 0">
						<li :class="pageIndex == 1 ? 'am-disabled':''" @click="changeIndex(pageIndex - 1)">
							<a href="javascript:void(0);">&laquo;</a>
						</li>
						
						<li v-for="p in totalPage" :key="p" @click="changeIndex(p)" :class="pageIndex == p ? 'am-active':''">
							<a href="javascript:void(0);">{{p}}</a>
						</li>
						
						<li :class="pageIndex == totalPage ? 'am-disabled':''" @click="changeIndex(pageIndex + 1)">
							<a href="javascript:void(0);">&raquo;</a>
						</li>
					</ul>
				</div>
			</div>
		</div>

		<hr class="section_divider -narrow">

		<!-- 课程报名弹窗复用组件 -->
		<CourseApplyDialog :visible="showApplyDialog" :course="applyCourse" @close="closeApplyDialog" @success="applySuccess" />
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import CourseApplyDialog from '@/components/CourseApplyDialog.vue';
import '../assets/css/common-headers.css'; // 导入头部共用样式

export default {
	name: "CourseView",
	components: { Layout, CourseApplyDialog },
	data() {
		return {
			isMobilePhone: isMobilePhone(),
			courseList: [],
			searchKeyword: '',
			pageIndex: 1,
			pageSize: 6,
			total: 0,
			totalPage: 1,
			showApplyDialog: false, // 报名弹窗显示状态
			applyCourse: null, // 当前报名的课程对象
		}
	},
	mounted() {
		this.$wxShare();
		this.getCourses();
		// this.useMockData();
	},
	methods: {
		getCourses() {
			const userInfoStr = localStorage.getItem("userInfo") || '{}';
			const userInfo = JSON.parse(userInfoStr);
			// 模拟数据，实际项目中应替换为真实接口
			this.getRequest("/cms/course/list", {
				'page': this.pageIndex,
				'limit': this.pageSize,
				'title': this.searchKeyword,
				'userId': userInfo.uid || ''
			}).then(resp => {
				if (resp && resp.code == 200) {
					this.courseList = resp.data.list || [];
					this.total = resp.data.total || 0;
					this.totalPage = resp.data.totalPage || 1;
				} else {
					this.courseList = [];
					this.total = 0;
					this.totalPage = 1;
				}
			})
		},
		searchCourses() {
			this.pageIndex = 1; // 搜索时重置为第一页
			this.getCourses();
		},
		changeIndex(p) {
			if (p < 1) {
				this.pageIndex = 1;
			} else if (p > this.totalPage) {
				this.pageIndex = this.totalPage;
			} else {
				this.pageIndex = p;
				this.getCourses();
			}
		},
		goCourseDetail(courseId) {
			this.$router.push({
				path: '/course-detail',
				query: {
					id: courseId
				}
			});
		},
		showApply(course) {
			this.applyCourse = course;
			this.showApplyDialog = true;
		},
		closeApplyDialog() {
			this.showApplyDialog = false;
			this.applyCourse = null;
		},
		applySuccess() {
			this.getCourses();
		}
	}
}
</script>

<style scoped>
/* 美化后的页面头部样式 */
.course-header {
	background-image: url('https://img.freepik.com/free-photo/watercolor-pastel-texture-background_53876-98173.jpg') !important;
}

.course-search {
	margin: 20px 0 30px;
	display: flex;
	justify-content: center;
}

.search-container {
	width: 100%;
	max-width: 500px;
	display: flex;
}

.search-input {
	flex: 1;
	padding: 10px 15px;
	border: 1px solid #ddd;
	border-radius: 4px 0 0 4px;
	font-size: 14px;
}

.search-btn {
	background-color: #516790;
	color: #fff;
	border: none;
	padding: 0 20px;
	border-radius: 0 4px 4px 0;
	cursor: pointer;
	transition: background-color 0.3s;
}

.search-btn:hover {
	background-color: #3e5178;
}

.crystal-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30px;
	margin-bottom: 30px;
}

.crystal-grid-mobile {
	grid-template-columns: 1fr;
	/* padding: 0 15px; */
}

/* 课程卡片样式优化 */
.crystal-course-card {
	width: 350px;
	background: #fff;
	border-radius: 15px;
	overflow: hidden;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	transform: translateY(0);
}

.crystal-course-card:hover {
	transform: translateY(-10px);
	box-shadow: 0 15px 30px rgba(86, 70, 128, 0.2);
}

.crystal-course-img {
	height: 230px;
	overflow: hidden;
	position: relative;
}

.crystal-course-img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.6s;
}

.course-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100%;
	background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
	opacity: 0;
	transition: all 0.3s;
}

.crystal-course-card:hover .course-overlay {
	opacity: 1;
}

.crystal-course-card:hover .crystal-course-img img {
	transform: scale(1.08);
}

.crystal-course-tag {
	position: absolute;
	top: 15px;
	right: 15px;
	background: linear-gradient(135deg, #7b4397, #dc2430);
	color: white;
	padding: 8px 15px;
	border-radius: 25px;
	font-size: 13px;
	font-weight: 500;
	z-index: 2;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.crystal-course-info {
	padding: 25px;
}

.crystal-course-info h3 {
	font-size: 20px;
	color: #3a2c58;
	margin-bottom: 12px;
	font-weight: 600;
	height: 48px;
	overflow: hidden;
}

.crystal-course-info h3 i {
	color: #7b4397;
	margin-right: 8px;
}

.crystal-course-info p {
	color: #666;
	font-size: 15px;
	line-height: 1.6;
	margin-bottom: 15px;
	height: 48px;
	overflow: hidden;
}

.crystal-course-info p i {
	font-size: 16px;
	margin-right: 5px;
	opacity: 0.7;
}

.crystal-course-meta {
	display: flex;
	flex-wrap: wrap;
	gap: 15px;
	margin-bottom: 20px;
}

.crystal-course-meta span {
	color: #888;
	font-size: 14px;
}

.crystal-course-meta i {
	margin-right: 5px;
	color: #7b4397;
}

.crystal-course-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.crystal-course-price {
	font-size: 20px;
	color: #dc2430;
	font-weight: bold;
}

.pulse-btn {
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(123, 67, 151, 0.4);
	}
	70% {
		box-shadow: 0 0 0 10px rgba(123, 67, 151, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(123, 67, 151, 0);
	}
}

.no-data {
	text-align: center;
	padding: 40px 0;
	color: #888;
}

/* 响应式适配 */
@media (max-width: 992px) {
	.crystal-grid {
		grid-template-columns: repeat(2, 1fr);
		padding: 0 15px;
	}
}

@media (max-width: 768px) {
	.crystal-grid {
		grid-template-columns: 1fr;
		padding: 0 15px;
	}
	
	.container {
		/* padding-left: 15px;
		padding-right: 15px; */
	}
	
	.search-container {
		width: 90%;
		margin: 0 auto;
	}
}

.crystal-btn {
	background: linear-gradient(135deg, #7b4397, #dc2430);
	color: white;
	border: none;
	padding: 10px 20px;
	border-radius: 25px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s;
	box-shadow: 0 4px 15px rgba(123, 67, 151, 0.3);
}

.crystal-btn:hover {
	background: linear-gradient(135deg, #dc2430, #7b4397);
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(123, 67, 151, 0.4);
}

.crystal-btn i {
	margin-right: 8px;
}

.crystal-more {
	text-align: center;
	margin-top: 40px;
	display: inline-block;
	position: relative;
	left: 50%;
	transform: translateX(-50%);
}

.apply-dialog-mask {
	position: fixed;
	z-index: 9999;
	left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.35);
	display: flex;
	align-items: center;
	justify-content: center;
}
.apply-dialog {
	background: #fff;
	border-radius: 12px;
	padding: 30px 30px 20px 30px;
	min-width: 320px;
	max-width: 90vw;
	box-shadow: 0 8px 32px rgba(0,0,0,0.18);
	position: relative;
}
.apply-dialog-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 18px;
	font-weight: 600;
	margin-bottom: 18px;
}
.apply-dialog-close {
	cursor: pointer;
	font-size: 20px;
	color: #888;
	transition: color 0.2s;
}
.apply-dialog-close:hover {
	color: #dc2430;
}
.apply-dialog-body {
	margin-bottom: 18px;
	font-size: 15px;
}
.invite-input {
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 6px 12px;
	font-size: 15px;
	margin-left: 8px;
}
.apply-dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}

.crystal-btn-secondary {
	background: linear-gradient(135deg, #2bff00, #1900ff);
	color: white;
	border: none;
	padding: 10px 20px;
	border-radius: 25px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s;
	box-shadow: 0 4px 15px rgba(180, 180, 180, 0.18);
	margin-left: 10px;
	animation: none;
}
.crystal-btn-secondary:hover {
	background: linear-gradient(135deg, #2bff00, #1900ff);
	color: white;
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(180, 180, 180, 0.28);
}
</style>
