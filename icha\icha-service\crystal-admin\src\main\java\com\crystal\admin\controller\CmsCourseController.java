package com.crystal.admin.controller;

import com.crystal.common.model.cms.CmsCourseEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsCourseCodeService;
import com.crystal.service.service.CmsCourseService;
import com.crystal.service.service.SystemAttachmentService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;

/**
 * 课程 控制器
 * | Author: 陈佳音
 * ｜ @date Mon Apr 29 10:10:51 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/course")
public class CmsCourseController {
    @Autowired
    private CmsCourseService cmsCourseService;
    @Autowired
    private SystemAttachmentService systemAttachmentService;
    @Autowired
    private CmsCourseCodeService cmsCourseCodeService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('cmscourse:list')")
    public CommonResult<CommonPage<CmsCourseEntity>> list(@Validated CmsCourseEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsCourseEntity> page = CommonPage.restPage(cmsCourseService.queryPage(request,pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('cmscourse:info')")
    public CommonResult<CmsCourseEntity> info(@PathVariable("id") Long id){
        CmsCourseEntity cmsCourse = cmsCourseService.getById(id);
        return CommonResult.success(cmsCourse);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('cmscourse:save')")
    public CommonResult<String> save(@RequestBody CmsCourseEntity cmsCourse){
        cmsCourse.setAddTime(new Date());
        cmsCourse.setCover(systemAttachmentService.clearPrefix(cmsCourse.getCover()));
        cmsCourse.setContent(systemAttachmentService.clearPrefix(cmsCourse.getContent()));
        cmsCourseService.save(cmsCourse);
        // 是否随机邀请码
        if(cmsCourse.getType().equals(2)) {
            // 生成邀请码
            cmsCourseCodeService.createInviteCode(cmsCourse.getId(), cmsCourse.getNumber());
        }
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('cmscourse:update')")
    public CommonResult<String> update(@RequestBody CmsCourseEntity cmsCourse){
        cmsCourse.setCover(systemAttachmentService.clearPrefix(cmsCourse.getCover()));
        cmsCourse.setContent(systemAttachmentService.clearPrefix(cmsCourse.getContent()));
        cmsCourseService.updateById(cmsCourse);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('cmscourse:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids){
        if (cmsCourseService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 