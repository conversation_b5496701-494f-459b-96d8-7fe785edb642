{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone } from \"@/utils/index\";\nimport Message from \"@/utils/message\";\nimport CourseApplyDialog from '@/components/CourseApplyDialog.vue';\nexport default {\n  name: \"IndexView\",\n  components: {\n    Layout,\n    CourseApplyDialog\n  },\n  data() {\n    return {\n      indexImage: '',\n      indexTitle: '',\n      indexDesc: '',\n      isMobilePhone: isMobilePhone(),\n      tabList: [],\n      tabIndex: 0,\n      slideshow: [],\n      // 水晶疗愈师列表\n      healerList: [],\n      // 水晶疗愈课程列表\n      courseList: [],\n      // 会员机构福利\n      memberBenefits: [{\n        id: 1,\n        icon: 'https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/Objects/Chart%20Increasing.png',\n        faIcon: 'fa-line-chart',\n        title: '优质流量',\n        desc: '获得平台精准推荐，提高曝光度和客户转化率'\n      }, {\n        id: 2,\n        icon: 'https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/Objects/Page%20with%20Curl.png',\n        faIcon: 'fa-certificate',\n        title: '专业认证',\n        desc: '获得行业权威认证，提升机构专业形象和信誉度'\n      }, {\n        id: 3,\n        icon: 'https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/Objects/Books.png',\n        faIcon: 'fa-share-alt',\n        title: '资源共享',\n        desc: '共享行业最新资讯、教材和教学方法，保持竞争优势'\n      }, {\n        id: 4,\n        icon: 'https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/People%20with%20professions/Handshake.png',\n        faIcon: 'fa-handshake-o',\n        title: '商业合作',\n        desc: '接触更多优质合作伙伴，拓展业务发展空间'\n      }],\n      showApplyDialog: false,\n      applyCourse: null\n    };\n  },\n  mounted() {\n    this.getIndex();\n    this.getIndexConfig();\n    this.$wxShare();\n  },\n  methods: {\n    goCourseDetail(id) {\n      this.$router.push({\n        path: '/course-detail',\n        query: {\n          id: id\n        }\n      });\n    },\n    turnJoin() {\n      this.$router.push({\n        path: '/join'\n      });\n    },\n    turnAbout() {\n      this.$router.push({\n        path: '/about'\n      });\n    },\n    // 图片点击放大\n    showImg(e) {\n      if (this.isMobilePhone) {\n        vant.ImagePreview({\n          images: [e],\n          // 图片集合\n          closeable: true // 关闭按钮\n        });\n      }\n    },\n\n    getIndex() {\n      const userInfoStr = localStorage.getItem(\"userInfo\") || '{}';\n      const userInfo = JSON.parse(userInfoStr);\n      this.getRequest(\"/cms/index\", {\n        userId: userInfo.uid || ''\n      }).then(resp => {\n        console.log(resp);\n        if (resp.data && resp.code == 200) {\n          this.courseList = resp.data.course;\n          this.healerList = resp.data.healers;\n          this.healerList.forEach(item => {\n            item.tags = item.tags ? item.tags.split(',') : [];\n          });\n          //console.log(this.slideshow)\n        } else {\n          this.courseList = [];\n          this.healerList = [];\n          Message.error(resp.data.message || '获取首页数据失败');\n        }\n      });\n    },\n    getIndexConfig() {\n      this.getRequest(\"/cms/config/index\").then(resp => {\n        console.log(resp);\n        if (resp.data && resp.code == 200) {\n          this.indexImage = resp.data.indexImage;\n          this.indexTitle = resp.data.indexTitle;\n          this.indexDesc = resp.data.indexDesc;\n        } else {}\n      });\n    },\n    // 显示疗愈师详情\n    showHealerDetail(healer) {\n      this.$router.push(`/healer-detail/${healer.id}?from=index`);\n    },\n    // 查看更多\n    showMore(type) {\n      console.log('查看更多:', type);\n      // 这里可以添加跳转到列表页的逻辑\n      if (type == 'healer') {\n        this.$router.push({\n          path: '/healers'\n        });\n      } else if (type == 'course') {\n        this.$router.push({\n          path: '/course'\n        });\n      }\n    },\n    showApply(course) {\n      this.applyCourse = course;\n      this.showApplyDialog = true;\n    },\n    closeApplyDialog() {\n      this.showApplyDialog = false;\n      this.applyCourse = null;\n    },\n    applySuccess() {\n      this.getIndex();\n    },\n    // 脉轮测试相关方法\n    goToChakraTest() {\n      this.$router.push('/chakra-test');\n    },\n    goToChakraIntro() {\n      this.$router.push('/chakra-test/intro');\n    },\n    goToChakraBalance() {\n      this.$router.push('/chakra-test/balance');\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "Message", "CourseApplyDialog", "name", "components", "data", "indexImage", "indexTitle", "indexDesc", "tabList", "tabIndex", "slideshow", "healerList", "courseList", "memberBenefits", "id", "icon", "faIcon", "title", "desc", "showApplyDialog", "applyCourse", "mounted", "getIndex", "getIndexConfig", "$wxShare", "methods", "goCourseDetail", "$router", "push", "path", "query", "<PERSON><PERSON><PERSON><PERSON>", "turnAbout", "showImg", "e", "vant", "ImagePreview", "images", "closeable", "userInfoStr", "localStorage", "getItem", "userInfo", "JSON", "parse", "getRequest", "userId", "uid", "then", "resp", "console", "log", "code", "course", "healers", "for<PERSON>ach", "item", "tags", "split", "error", "message", "showHealerDetail", "healer", "showMore", "type", "showApply", "closeApplyDialog", "applySuccess", "goToChakraTest", "goToChakraIntro", "goToChakraBalance"], "sources": ["src/views/IndexView.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t<!-- 页面顶部背景区域 -->\r\n\t\t<div class=\"hero-section\" :style=\"{backgroundImage: 'url(' + indexImage + ')'}\">\r\n\t\t\t<div class=\"hero-content\">\r\n\t\t\t\t<h1 class=\"hero-title\"><i class=\"fa fa-diamond fa-spin-pulse\"></i> {{indexTitle}}</h1>\r\n\t\t\t\t<p class=\"hero-subtitle\"><i class=\"fa fa-heart\"></i> {{indexDesc}}</p>\r\n\t\t\t\t<div class=\"hero-buttons\">\r\n\t\t\t\t\t<!-- <button class=\"hero-btn\"><i class=\"fa fa-compass\"></i> 开始探索</button> -->\r\n\t\t\t\t\t<button @click=\"turnAbout\" class=\"hero-btn outline\"><i class=\"fa fa-info-circle\"></i> 了解更多</button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 寻找水晶疗愈师 -->\r\n\t\t<div class=\"section section-healer\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"section--header fancy-title\">\r\n\t\t\t\t\t<div class=\"crystal-icon\">\r\n\t\t\t\t\t\t<i class=\"fa fa-user-md fa-2x\"></i>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<h2 class=\"section--title\">寻找水晶疗愈师</h2>\r\n\t\t\t\t\t<div class=\"title-decoration\">\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<p class=\"section--desc\"><i class=\"fa fa-certificate fa-spin-slow\"></i> 专业的水晶疗愈师，为您带来身心灵的平衡</p>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"crystal-healer-container\">\r\n\t\t\t\t\t<div class=\"crystal-grid\" :class=\"{'crystal-grid-mobile': isMobilePhone}\">\r\n\t\t\t\t\t\t<div v-for=\"(healer, index) in healerList\" :key=\"index\" class=\"crystal-card\" @click=\"showHealerDetail(healer)\">\r\n\t\t\t\t\t\t\t<div class=\"crystal-card-img\">\r\n\t\t\t\t\t\t\t\t<img :src=\"healer.avatar\" alt=\"\">\r\n\t\t\t\t\t\t\t\t<div class=\"card-overlay\"></div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"crystal-badge\"><i class=\"fa fa-certificate\"></i> 认证</div>\r\n\t\t\t\t\t\t\t<div class=\"crystal-card-info\">\r\n\t\t\t\t\t\t\t\t<h3><i class=\"fa fa-user-circle-o\"></i> {{healer.name}}</h3>\r\n\t\t\t\t\t\t\t\t<div class=\"crystal-tags\">\r\n\t\t\t\t\t\t\t\t\t<span v-for=\"(tag, i) in healer.tags\" :key=\"i\"><i class=\"fa fa-star-o\"></i> {{tag}}</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<p><i class=\"fa fa-quote-left quote-icon\"></i> {{healer.intro}}</p>\r\n\t\t\t\t\t\t\t\t<div class=\"crystal-card-footer\">\r\n\t\t\t\t\t\t\t\t\t<span class=\"crystal-card-location\"><i class=\"fa fa-map-marker\"></i> {{healer.location}}</span>\r\n\t\t\t\t\t\t\t\t\t<button class=\"crystal-btn\" @click=\"showHealerDetail(healer)\"><i class=\"fa fa-info-circle\"></i> 查看详情</button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"crystal-more\" @click=\"showMore('healer')\">\r\n\t\t\t\t\t\t<span>查看更多</span>\r\n\t\t\t\t\t\t<i class=\"fa fa-angle-right\"></i>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 查找水晶疗愈课程 -->\r\n\t\t<div class=\"section section-course\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"section--header fancy-title\">\r\n\t\t\t\t\t<div class=\"crystal-icon\">\r\n\t\t\t\t\t\t<i class=\"fa fa-graduation-cap fa-2x\"></i>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<h2 class=\"section--title\">查找水晶疗愈课程</h2>\r\n\t\t\t\t\t<div class=\"title-decoration\">\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<p class=\"section--desc\"><i class=\"fa fa-book\"></i> 找到适合您的水晶疗愈课程，开启身心灵之旅</p>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"crystal-course-container\">\r\n\t\t\t\t\t<div class=\"crystal-grid\" :class=\"{'crystal-grid-mobile': isMobilePhone}\">\r\n\t\t\t\t\t\t<div v-for=\"(course, index) in courseList\" :key=\"index\" class=\"crystal-course-card\">\r\n\t\t\t\t\t\t\t<div class=\"crystal-course-img\">\r\n\t\t\t\t\t\t\t\t<img :src=\"course.cover\" alt=\"\">\r\n\t\t\t\t\t\t\t\t<div class=\"course-overlay\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"crystal-course-tag\"><i class=\"fa fa-bookmark-o\"></i> {{course.tag || '热门'}}</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"crystal-course-info\">\r\n\t\t\t\t\t\t\t\t<h3><i class=\"fa fa-diamond\"></i> {{course.title}}</h3>\r\n\t\t\t\t\t\t\t\t<p><i class=\"fa fa-info-circle info-icon\"></i> {{course.brief}}</p>\r\n\t\t\t\t\t\t\t\t<!-- <div class=\"crystal-course-meta\">\r\n\t\t\t\t\t\t\t\t\t<span><i class=\"fa fa-calendar\"></i> {{course.startTime}}</span>\r\n\t\t\t\t\t\t\t\t\t<span><i class=\"fa fa-map-marker\"></i> {{course.location}}</span>\r\n\t\t\t\t\t\t\t\t</div> -->\r\n\t\t\t\t\t\t\t\t<div class=\"crystal-course-footer\">\r\n\t\t\t\t\t\t\t\t\t<span class=\"crystal-course-price\"><i class=\"fa fa-jpy\"></i> ¥{{course.price}}</span>\r\n\t\t\t\t\t\t\t\t\t<button v-if=\"!course.isAttend\" @click=\"showApply(course)\" class=\"crystal-btn pulse-btn\"><i class=\"fa fa-pencil\"></i> 立即报名</button>\r\n\t\t\t\t\t\t\t\t\t<button v-else @click=\"goCourseDetail(course.id)\" class=\"crystal-btn crystal-btn-secondary\">观看课程</button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"crystal-more\" @click=\"showMore('course')\">\r\n\t\t\t\t\t\t<span>查看更多</span>\r\n\t\t\t\t\t\t<i class=\"fa fa-angle-right\"></i>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 脉轮测试 -->\r\n\t\t<!-- <div class=\"section section-chakra\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"section--header fancy-title\">\r\n\t\t\t\t\t<div class=\"crystal-icon\">\r\n\t\t\t\t\t\t<i class=\"fa fa-circle-o fa-2x\"></i>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<h2 class=\"section--title\">脉轮测试</h2>\r\n\t\t\t\t\t<div class=\"title-decoration\">\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<p class=\"section--desc\"><i class=\"fa fa-magic\"></i> 了解您的脉轮能量状态，开启身心灵平衡之旅</p>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"chakra-test-container\">\r\n\t\t\t\t\t<div class=\"chakra-intro\">\r\n\t\t\t\t\t\t<div class=\"chakra-intro-content\">\r\n\t\t\t\t\t\t\t<h3><i class=\"fa fa-lightbulb-o\"></i> 什么是脉轮测试？</h3>\r\n\t\t\t\t\t\t\t<p>脉轮是人体能量系统的重要组成部分，通过专业的脉轮测试，您可以了解自己七个主要脉轮的能量状态，发现需要平衡和调整的部分。</p>\r\n\r\n\t\t\t\t\t\t\t<div class=\"chakra-features\">\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-feature\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"fa fa-check-circle\"></i>\r\n\t\t\t\t\t\t\t\t\t<span>专业测试问卷</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-feature\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"fa fa-chart-bar\"></i>\r\n\t\t\t\t\t\t\t\t\t<span>详细结果分析</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-feature\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"fa fa-heart\"></i>\r\n\t\t\t\t\t\t\t\t\t<span>个性化建议</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"chakra-intro-image\">\r\n\t\t\t\t\t\t\t<div class=\"chakra-circles\">\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-circle\" style=\"background: #993734;\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-circle\" style=\"background: #be6f2a;\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-circle\" style=\"background: #d7c34a;\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-circle\" style=\"background: #5f9057;\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-circle\" style=\"background: #5b8aa4;\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-circle\" style=\"background: #2c3485;\"></div>\r\n\t\t\t\t\t\t\t\t<div class=\"chakra-circle\" style=\"background: #7e4997;\"></div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div class=\"chakra-actions\">\r\n\t\t\t\t\t\t<button @click=\"goToChakraTest\" class=\"crystal-btn-large pulse-btn\">\r\n\t\t\t\t\t\t\t<i class=\"fa fa-play-circle\"></i> 开始脉轮测试\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<div class=\"chakra-links\">\r\n\t\t\t\t\t\t\t<a @click=\"goToChakraIntro\" class=\"chakra-link\">\r\n\t\t\t\t\t\t\t\t<i class=\"fa fa-info-circle\"></i> 脉轮简介\r\n\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t<a @click=\"goToChakraBalance\" class=\"chakra-link\">\r\n\t\t\t\t\t\t\t\t<i class=\"fa fa-balance-scale\"></i> 平衡脉轮\r\n\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div> -->\r\n\r\n\t\t<!-- 成为会员机构 -->\r\n\t\t<div class=\"section member-section\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"section--header fancy-title\">\r\n\t\t\t\t\t<div class=\"crystal-icon\">\r\n\t\t\t\t\t\t<i class=\"fa fa-users fa-2x\"></i>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<h2 class=\"section--title\">成为会员机构</h2>\r\n\t\t\t\t\t<div class=\"title-decoration\">\r\n\t\t\t\t\t\t<span></span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<p class=\"section--desc\"><i class=\"fa fa-handshake-o\"></i> 加入我们的专业网络，共同推广水晶疗愈</p>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"member-benefits\">\r\n\t\t\t\t\t<div class=\"member-grid\" :class=\"{'member-grid-mobile': isMobilePhone}\">\r\n\t\t\t\t\t\t<div v-for=\"(benefit, index) in memberBenefits\" :key=\"index\" class=\"member-benefit-card\">\r\n\t\t\t\t\t\t\t<div class=\"member-benefit-icon\">\r\n\t\t\t\t\t\t\t\t<i :class=\"benefit.faIcon\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>{{benefit.title}}</h3>\r\n\t\t\t\t\t\t\t<p>{{benefit.desc}}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"member-join\" @click=\"turnJoin\">\r\n\t\t\t\t\t\t<button class=\"crystal-btn-large shine-btn\"><i class=\"fa fa-rocket\"></i> 立即加入</button>\r\n\t\t\t\t\t\t<p class=\"member-join-text\"><i class=\"fa fa-building\"></i> 已有<span>328</span>家机构成为我们的会员</p>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t<!-- 装饰元素 -->\r\n\t\t<div class=\"decoration-element top-right\"><i class=\"fa fa-diamond\"></i></div>\r\n\t\t<div class=\"decoration-element bottom-left\"><i class=\"fa fa-sun-o fa-spin-slow\"></i></div>\r\n\r\n\t\t<!-- 课程报名弹窗复用组件 -->\r\n\t\t<CourseApplyDialog :visible=\"showApplyDialog\" :course=\"applyCourse\" @close=\"closeApplyDialog\" @success=\"applySuccess\" />\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport Message from \"@/utils/message\";\r\nimport CourseApplyDialog from '@/components/CourseApplyDialog.vue';\r\nexport default {\r\n\tname: \"IndexView\",\r\n\tcomponents: { Layout, CourseApplyDialog },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tindexImage: '',\r\n\t\t\tindexTitle: '',\r\n\t\t\tindexDesc: '',\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\ttabList: [],\r\n\t\t\ttabIndex: 0,\r\n\t\t\tslideshow: [],\r\n\r\n\t\t\t// 水晶疗愈师列表\r\n\t\t\thealerList: [\r\n\t\t\t],\r\n\t\t\t// 水晶疗愈课程列表\r\n\t\t\tcourseList: [\r\n\t\t\t],\r\n\t\t\t// 会员机构福利\r\n\t\t\tmemberBenefits: [\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\ticon: 'https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/Objects/Chart%20Increasing.png',\r\n\t\t\t\t\tfaIcon: 'fa-line-chart',\r\n\t\t\t\t\ttitle: '优质流量',\r\n\t\t\t\t\tdesc: '获得平台精准推荐，提高曝光度和客户转化率'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\ticon: 'https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/Objects/Page%20with%20Curl.png',\r\n\t\t\t\t\tfaIcon: 'fa-certificate',\r\n\t\t\t\t\ttitle: '专业认证',\r\n\t\t\t\t\tdesc: '获得行业权威认证，提升机构专业形象和信誉度'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 3,\r\n\t\t\t\t\ticon: 'https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/Objects/Books.png',\r\n\t\t\t\t\tfaIcon: 'fa-share-alt',\r\n\t\t\t\t\ttitle: '资源共享',\r\n\t\t\t\t\tdesc: '共享行业最新资讯、教材和教学方法，保持竞争优势'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 4,\r\n\t\t\t\t\ticon: 'https://raw.githubusercontent.com/Tarikul-Islam-Anik/Animated-Fluent-Emojis/master/Emojis/People%20with%20professions/Handshake.png',\r\n\t\t\t\t\tfaIcon: 'fa-handshake-o',\r\n\t\t\t\t\ttitle: '商业合作',\r\n\t\t\t\t\tdesc: '接触更多优质合作伙伴，拓展业务发展空间'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tshowApplyDialog: false,\r\n\t\t\tapplyCourse: null,\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.getIndex();\r\n\t\tthis.getIndexConfig();\r\n\t\tthis.$wxShare();\r\n\t},\r\n\tmethods: {\r\n\t\tgoCourseDetail(id) {\r\n\t\t\tthis.$router.push({\r\n\t\t\t\tpath: '/course-detail',\r\n\t\t\t\tquery: { id: id }\r\n\t\t\t})\r\n\t\t},\r\n\t\tturnJoin() {\r\n\t\t\tthis.$router.push({\r\n\t\t\t\tpath: '/join',\r\n\t\t\t})\r\n\t\t},\r\n\t\tturnAbout() {\r\n\t\t\tthis.$router.push({\r\n\t\t\t\tpath: '/about',\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 图片点击放大\r\n\t\tshowImg(e) {\r\n\t\t\tif (this.isMobilePhone) {\r\n\t\t\t\tvant.ImagePreview({\r\n\t\t\t\t\timages: [e], // 图片集合\r\n\t\t\t\t\tcloseable: true, // 关闭按钮\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetIndex() {\r\n\t\t\tconst userInfoStr = localStorage.getItem(\"userInfo\") || '{}';\r\n\t\t\tconst userInfo = JSON.parse(userInfoStr);\r\n\t\t\tthis.getRequest(\"/cms/index\", {\r\n\t\t\t\tuserId: userInfo.uid || ''\r\n\t\t\t}).then(resp => {\r\n\t\t\t\tconsole.log(resp)\r\n\t\t\t\tif (resp.data && resp.code == 200) {\r\n\t\t\t\t\tthis.courseList = resp.data.course\r\n\t\t\t\t\tthis.healerList = resp.data.healers\r\n\t\t\t\t\tthis.healerList.forEach(item => {\r\n\t\t\t\t\t\titem.tags = item.tags ? item.tags.split(',') : []\r\n\t\t\t\t\t})\r\n\t\t\t\t\t//console.log(this.slideshow)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.courseList = []\r\n\t\t\t\t\tthis.healerList = []\r\n\t\t\t\t\tMessage.error(resp.data.message || '获取首页数据失败')\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetIndexConfig() {\r\n\t\t\tthis.getRequest(\"/cms/config/index\").then(resp => {\r\n\t\t\t\tconsole.log(resp)\r\n\t\t\t\tif (resp.data && resp.code == 200) {\r\n\t\t\t\t\tthis.indexImage = resp.data.indexImage\r\n\t\t\t\t\tthis.indexTitle = resp.data.indexTitle\r\n\t\t\t\t\tthis.indexDesc = resp.data.indexDesc\r\n\t\t\t\t} else {\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 显示疗愈师详情\r\n\t\tshowHealerDetail(healer) {\r\n\t\t\tthis.$router.push(`/healer-detail/${healer.id}?from=index`);\r\n\t\t},\r\n\t\t// 查看更多\r\n\t\tshowMore(type) {\r\n\t\t\tconsole.log('查看更多:', type);\r\n\t\t\t// 这里可以添加跳转到列表页的逻辑\r\n\t\t\tif(type == 'healer'){\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tpath: '/healers',\r\n\t\t\t\t})\r\n\t\t\t}else if(type == 'course'){\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tpath: '/course',\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tshowApply(course) {\r\n\t\t\tthis.applyCourse = course;\r\n\t\t\tthis.showApplyDialog = true;\r\n\t\t},\r\n\t\tcloseApplyDialog() {\r\n\t\t\tthis.showApplyDialog = false;\r\n\t\t\tthis.applyCourse = null;\r\n\t\t},\r\n\t\tapplySuccess() {\r\n\t\t\tthis.getIndex();\r\n\t\t},\r\n\t\t// 脉轮测试相关方法\r\n\t\tgoToChakraTest() {\r\n\t\t\tthis.$router.push('/chakra-test');\r\n\t\t},\r\n\t\tgoToChakraIntro() {\r\n\t\t\tthis.$router.push('/chakra-test/intro');\r\n\t\t},\r\n\t\tgoToChakraBalance() {\r\n\t\t\tthis.$router.push('/chakra-test/balance');\r\n\t\t},\r\n\t},\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 基础样式 */\r\n.product {\r\n\ttext-align: center;\r\n\theight: 80px;\r\n\tline-height: 80px;\r\n\tfont-size: 24px;\r\n\tfont-weight: 600;\r\n\tcolor: #564680;\r\n}\r\n\r\n.am-g {\r\n\twidth: 96%;\r\n}\r\n\r\n.item-img {\r\n\tpadding: 10px;\r\n\tbox-shadow: 2px 3px 10px 0px rgba(0, 0, 0, 0.16);\r\n\tborder-radius: 8px;\r\n\tmargin-top: 20px;\r\n}\r\n\r\n/* 英雄区域样式 */\r\n.hero-section {\r\n\theight: 500px;\r\n\tbackground: linear-gradient(45deg, #7b4397, #dc2430);\r\n\tbackground-size: cover;\r\n\tbackground-position: center;\r\n\tbackground-blend-mode: overlay;\r\n\tposition: relative;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tcolor: white;\r\n\ttext-align: center;\r\n\tmargin-bottom: 50px;\r\n\twidth: 100%;\r\n}\r\n\r\n.hero-section::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(55, 30, 94, 0.7);\r\n}\r\n\r\n.hero-content {\r\n\tposition: relative;\r\n\tz-index: 2;\r\n\tmax-width: 800px;\r\n\tpadding: 0 20px;\r\n}\r\n\r\n.hero-title {\r\n\tfont-size: 48px;\r\n\tfont-weight: 700;\r\n\tmargin-bottom: 20px;\r\n\ttext-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\r\n\tanimation: fadeInDown 1s ease-out;\r\n}\r\n\r\n.hero-title i {\r\n\tmargin-right: 10px;\r\n\tcolor: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.hero-subtitle {\r\n\tfont-size: 20px;\r\n\tfont-weight: 400;\r\n\tmargin-bottom: 30px;\r\n\ttext-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);\r\n\tanimation: fadeInUp 1s ease-out;\r\n}\r\n\r\n.hero-subtitle i {\r\n\tmargin-right: 10px;\r\n\tcolor: #ff6b8b;\r\n}\r\n\r\n.hero-buttons {\r\n\tdisplay: flex;\r\n\tgap: 20px;\r\n\tjustify-content: center;\r\n\tmargin-top: 30px;\r\n\tanimation: fadeInUp 1.5s ease-out;\r\n}\r\n\r\n.hero-btn {\r\n\tbackground: linear-gradient(135deg, #7b4397, #dc2430);\r\n\tcolor: white;\r\n\tborder: none;\r\n\tpadding: 12px 25px;\r\n\tborder-radius: 30px;\r\n\tfont-size: 16px;\r\n\tfont-weight: 500;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s;\r\n\tbox-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.hero-btn i {\r\n\tmargin-right: 8px;\r\n}\r\n\r\n.hero-btn:hover {\r\n\ttransform: translateY(-3px);\r\n\tbox-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.hero-btn.outline {\r\n\tbackground: transparent;\r\n\tborder: 2px solid white;\r\n}\r\n\r\n.hero-btn.outline:hover {\r\n\tbackground: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* 区块通用样式 */\r\n.section {\r\n\tpadding: 80px 0;\r\n\tposition: relative;\r\n}\r\n\r\n.section::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground-image: url('https://img.freepik.com/free-vector/abstract-background-with-geometric-pattern_1319-74.jpg');\r\n\tbackground-size: cover;\r\n\tbackground-position: center;\r\n\topacity: 0.03;\r\n\tz-index: -1;\r\n}\r\n\r\n.section-healer {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.section-course {\r\n\tbackground-color: #f8f5ff;\r\n}\r\n\r\n/* 标题样式优化 */\r\n.fancy-title {\r\n\ttext-align: center;\r\n\tmargin-bottom: 60px;\r\n\tposition: relative;\r\n}\r\n\r\n.crystal-icon {\r\n\tmargin: 0 auto 15px;\r\n\twidth: 80px;\r\n\theight: 80px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tborder-radius: 50%;\r\n\tbackground: linear-gradient(135deg, #ddd6f3, #faaca8);\r\n\tbox-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.crystal-icon i {\r\n\tcolor: white;\r\n}\r\n\r\n.section--title {\r\n\tfont-size: 36px;\r\n\tfont-weight: 700;\r\n\tcolor: #3a2c58;\r\n\tmargin-bottom: 20px;\r\n\tposition: relative;\r\n\tdisplay: inline-block;\r\n}\r\n\r\n.section--desc i {\r\n\tmargin-right: 8px;\r\n\tcolor: #7b4397;\r\n}\r\n\r\n.title-decoration {\r\n\tposition: relative;\r\n\theight: 2px;\r\n\twidth: 120px;\r\n\tbackground: linear-gradient(to right, transparent, #564680, transparent);\r\n\tmargin: 0 auto 25px;\r\n}\r\n\r\n.title-decoration span {\r\n\tposition: absolute;\r\n\ttop: -7px;\r\n\tleft: 50%;\r\n\ttransform: translateX(-50%);\r\n\twidth: 15px;\r\n\theight: 15px;\r\n\tbackground: #564680;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.title-decoration span::before,\r\n.title-decoration span::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 50%;\r\n\twidth: 8px;\r\n\theight: 8px;\r\n\tbackground: #784ba0;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.title-decoration span::before {\r\n\tleft: -20px;\r\n\ttransform: translateY(-50%);\r\n}\r\n\r\n.title-decoration span::after {\r\n\tright: -20px;\r\n\ttransform: translateY(-50%);\r\n}\r\n\r\n.section--desc {\r\n\tfont-size: 18px;\r\n\tcolor: #666;\r\n\tmax-width: 700px;\r\n\tmargin: 0 auto;\r\n\tline-height: 1.6;\r\n}\r\n\r\n/* 水晶疗愈师卡片样式优化 */\r\n.crystal-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 30px;\r\n\tjustify-content: center;\r\n}\r\n\r\n.crystal-grid-mobile {\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n\r\n.crystal-card {\r\n\twidth: 350px;\r\n\tbackground: #fff;\r\n\tborder-radius: 15px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n\ttransition: all 0.3s ease;\r\n\tcursor: pointer;\r\n\ttransform: translateY(0);\r\n\tposition: relative;\r\n}\r\n\r\n.crystal-card:hover {\r\n\ttransform: translateY(-10px);\r\n\tbox-shadow: 0 15px 30px rgba(86, 70, 128, 0.2);\r\n}\r\n\r\n.crystal-card-img {\r\n\theight: 230px;\r\n\toverflow: hidden;\r\n\tposition: relative;\r\n}\r\n\r\n.crystal-card-img img {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n\ttransition: transform 0.6s;\r\n}\r\n\r\n.card-overlay {\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 50%;\r\n\tbackground: linear-gradient(to top, rgba(86, 70, 128, 0.7), transparent);\r\n\topacity: 0;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.crystal-card:hover .card-overlay {\r\n\topacity: 1;\r\n}\r\n\r\n.crystal-card:hover .crystal-card-img img {\r\n\ttransform: scale(1.08);\r\n}\r\n\r\n.crystal-card-info {\r\n\tpadding: 25px;\r\n\tposition: relative;\r\n}\r\n\r\n.crystal-card-info h3 {\r\n\tfont-size: 22px;\r\n\tcolor: #3a2c58;\r\n\tmargin-bottom: 12px;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.crystal-card-info h3 i {\r\n\tcolor: #7b4397;\r\n\tmargin-right: 8px;\r\n}\r\n\r\n.crystal-tags {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 8px;\r\n\tmargin-bottom: 15px;\r\n}\r\n\r\n.crystal-tags span {\r\n\tbackground: linear-gradient(135deg, #ddd6f3, #faaca8);\r\n\tcolor: #3a2c58;\r\n\tfont-size: 12px;\r\n\tpadding: 5px 12px;\r\n\tborder-radius: 20px;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.crystal-tags span i {\r\n\tfont-size: 10px;\r\n\tmargin-right: 4px;\r\n}\r\n\r\n.crystal-card-info p {\r\n\tcolor: #666;\r\n\tfont-size: 15px;\r\n\tline-height: 1.6;\r\n\tmargin-bottom: 20px;\r\n\theight: 72px;\r\n\toverflow: hidden;\r\n}\r\n\r\n.crystal-card-info p i {\r\n\tfont-size: 16px;\r\n\tmargin-right: 5px;\r\n\topacity: 0.7;\r\n}\r\n\r\n.quote-icon,\r\n.info-icon {\r\n\tcolor: #ddd6f3;\r\n\tfont-size: 16px;\r\n\tmargin-right: 5px;\r\n\topacity: 0.7;\r\n}\r\n\r\n.crystal-card-footer {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.crystal-card-location {\r\n\tcolor: #888;\r\n\tfont-size: 14px;\r\n}\r\n\r\n.crystal-card-location i {\r\n\tmargin-right: 8px;\r\n}\r\n\r\n.crystal-btn {\r\n\tbackground: linear-gradient(135deg, #7b4397, #dc2430);\r\n\tcolor: white;\r\n\tborder: none;\r\n\tpadding: 10px 20px;\r\n\tborder-radius: 25px;\r\n\tfont-weight: 500;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s;\r\n\tbox-shadow: 0 4px 15px rgba(123, 67, 151, 0.3);\r\n}\r\n\r\n.crystal-btn:hover {\r\n\tbackground: linear-gradient(135deg, #dc2430, #7b4397);\r\n\ttransform: translateY(-2px);\r\n\tbox-shadow: 0 6px 20px rgba(123, 67, 151, 0.4);\r\n}\r\n\r\n.crystal-btn i {\r\n\tmargin-right: 8px;\r\n}\r\n\r\n.crystal-more {\r\n\ttext-align: center;\r\n\tmargin-top: 40px;\r\n\tdisplay: inline-block;\r\n\tposition: relative;\r\n\tleft: 50%;\r\n\ttransform: translateX(-50%);\r\n}\r\n\r\n.crystal-more span {\r\n\tcolor: #564680;\r\n\tfont-size: 17px;\r\n\tfont-weight: 500;\r\n\tmargin-right: 5px;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.crystal-more i {\r\n\ttransition: all 0.3s;\r\n\tposition: relative;\r\n\ttop: 1px;\r\n}\r\n\r\n.crystal-more:hover span {\r\n\tcolor: #dc2430;\r\n}\r\n\r\n.crystal-more:hover i {\r\n\ttransform: translateX(5px);\r\n\tcolor: #dc2430;\r\n}\r\n\r\n/* 课程卡片样式优化 */\r\n.crystal-course-card {\r\n\twidth: 350px;\r\n\tbackground: #fff;\r\n\tborder-radius: 15px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\r\n\ttransition: all 0.3s ease;\r\n\ttransform: translateY(0);\r\n}\r\n\r\n.crystal-course-card:hover {\r\n\ttransform: translateY(-10px);\r\n\tbox-shadow: 0 15px 30px rgba(86, 70, 128, 0.2);\r\n}\r\n\r\n.crystal-course-img {\r\n\theight: 230px;\r\n\toverflow: hidden;\r\n\tposition: relative;\r\n}\r\n\r\n.crystal-course-img img {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n\ttransition: transform 0.6s;\r\n}\r\n\r\n.course-overlay {\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 100%;\r\n\tbackground: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);\r\n\topacity: 0;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.crystal-course-card:hover .course-overlay {\r\n\topacity: 1;\r\n}\r\n\r\n.crystal-course-card:hover .crystal-course-img img {\r\n\ttransform: scale(1.08);\r\n}\r\n\r\n.crystal-course-tag {\r\n\tposition: absolute;\r\n\ttop: 15px;\r\n\tright: 15px;\r\n\tbackground: linear-gradient(135deg, #7b4397, #dc2430);\r\n\tcolor: white;\r\n\tpadding: 8px 15px;\r\n\tborder-radius: 25px;\r\n\tfont-size: 13px;\r\n\tfont-weight: 500;\r\n\tz-index: 2;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.crystal-course-info {\r\n\tpadding: 25px;\r\n}\r\n\r\n.crystal-course-info h3 {\r\n\tfont-size: 20px;\r\n\tcolor: #3a2c58;\r\n\tmargin-bottom: 12px;\r\n\tfont-weight: 600;\r\n\theight: 48px;\r\n\toverflow: hidden;\r\n}\r\n\r\n.crystal-course-info h3 i {\r\n\tcolor: #7b4397;\r\n\tmargin-right: 8px;\r\n}\r\n\r\n.crystal-course-info p {\r\n\tcolor: #666;\r\n\tfont-size: 15px;\r\n\tline-height: 1.6;\r\n\tmargin-bottom: 15px;\r\n\theight: 48px;\r\n\toverflow: hidden;\r\n}\r\n\r\n.crystal-course-info p i {\r\n\tfont-size: 16px;\r\n\tmargin-right: 5px;\r\n\topacity: 0.7;\r\n}\r\n\r\n.crystal-course-meta {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 15px;\r\n\tmargin-bottom: 20px;\r\n}\r\n\r\n.crystal-course-meta span {\r\n\tcolor: #888;\r\n\tfont-size: 14px;\r\n}\r\n\r\n.crystal-course-meta i {\r\n\tmargin-right: 5px;\r\n\tcolor: #7b4397;\r\n}\r\n\r\n.crystal-course-footer {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.crystal-course-price {\r\n\tfont-size: 20px;\r\n\tcolor: #dc2430;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.pulse-btn {\r\n\tanimation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n\t0% {\r\n\t\tbox-shadow: 0 0 0 0 rgba(123, 67, 151, 0.4);\r\n\t}\r\n\t70% {\r\n\t\tbox-shadow: 0 0 0 10px rgba(123, 67, 151, 0);\r\n\t}\r\n\t100% {\r\n\t\tbox-shadow: 0 0 0 0 rgba(123, 67, 151, 0);\r\n\t}\r\n}\r\n\r\n/* 会员机构部分优化 */\r\n.member-section {\r\n\t/* background: linear-gradient(135deg, #f5f7fa, #eef2f7); */\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.member-section::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\twidth: 300px;\r\n\theight: 300px;\r\n\tbackground: radial-gradient(circle, rgba(123, 67, 151, 0.1), transparent);\r\n\ttop: -150px;\r\n\tleft: -150px;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.member-section::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\twidth: 200px;\r\n\theight: 200px;\r\n\tbackground: radial-gradient(circle, rgba(220, 36, 48, 0.1), transparent);\r\n\tbottom: -100px;\r\n\tright: -100px;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.member-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 30px;\r\n\tjustify-content: center;\r\n\tmargin-bottom: 60px;\r\n}\r\n\r\n.member-grid-mobile {\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n\r\n.member-benefit-card {\r\n\twidth: 260px;\r\n\tbackground: white;\r\n\tborder-radius: 15px;\r\n\tpadding: 35px 25px;\r\n\ttext-align: center;\r\n\tbox-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\r\n\ttransition: all 0.3s;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.member-benefit-card::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 5px;\r\n\tbackground: linear-gradient(to right, #7b4397, #dc2430);\r\n}\r\n\r\n.member-benefit-card:hover {\r\n\ttransform: translateY(-10px);\r\n\tbox-shadow: 0 15px 40px rgba(86, 70, 128, 0.15);\r\n}\r\n\r\n.member-benefit-icon {\r\n\tmargin-bottom: 25px;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.member-benefit-card:hover .member-benefit-icon {\r\n\ttransform: translateY(-5px);\r\n}\r\n\r\n.member-benefit-icon i {\r\n\tfont-size: 70px;\r\n\tcolor: #7b4397;\r\n}\r\n\r\n.member-benefit-card h3 {\r\n\tfont-size: 20px;\r\n\tcolor: #3a2c58;\r\n\tmargin-bottom: 15px;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.member-benefit-card p {\r\n\tcolor: #666;\r\n\tfont-size: 15px;\r\n\tline-height: 1.6;\r\n}\r\n\r\n.member-join {\r\n\ttext-align: center;\r\n\tpadding: 20px;\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n\tborder-radius: 15px;\r\n\tbox-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);\r\n\tmax-width: 500px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n.crystal-btn-large {\r\n\tbackground: linear-gradient(135deg, #7b4397, #dc2430);\r\n\tcolor: white;\r\n\tborder: none;\r\n\tpadding: 15px 40px;\r\n\tborder-radius: 30px;\r\n\tfont-size: 18px;\r\n\tfont-weight: 600;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s;\r\n\tmargin-bottom: 20px;\r\n\tbox-shadow: 0 10px 20px rgba(123, 67, 151, 0.3);\r\n}\r\n\r\n.crystal-btn-large:hover {\r\n\tbackground: linear-gradient(135deg, #dc2430, #7b4397);\r\n\ttransform: translateY(-5px);\r\n\tbox-shadow: 0 15px 30px rgba(123, 67, 151, 0.4);\r\n}\r\n\r\n.shine-btn {\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.shine-btn::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: -50%;\r\n\tleft: -50%;\r\n\twidth: 200%;\r\n\theight: 200%;\r\n\tbackground: rgba(255, 255, 255, 0.2);\r\n\ttransform: rotate(30deg);\r\n\tanimation: shine 3s infinite;\r\n}\r\n\r\n@keyframes shine {\r\n\t0% { transform: translateX(-100%) rotate(30deg); }\r\n\t100% { transform: translateX(100%) rotate(30deg); }\r\n}\r\n\r\n.member-join-text {\r\n\tcolor: #666;\r\n\tfont-size: 16px;\r\n}\r\n\r\n.member-join-text i {\r\n\tmargin-right: 8px;\r\n\tcolor: #7b4397;\r\n}\r\n\r\n.member-join-text span {\r\n\tcolor: #dc2430;\r\n\tfont-weight: bold;\r\n\tfont-size: 20px;\r\n\tmargin: 0 5px;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes fadeInDown {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\ttransform: translateY(-30px);\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t\ttransform: translateY(0);\r\n\t}\r\n}\r\n\r\n@keyframes fadeInUp {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\ttransform: translateY(30px);\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t\ttransform: translateY(0);\r\n\t}\r\n}\r\n\r\n/* 装饰元素 */\r\n.decoration-element {\r\n\tposition: fixed;\r\n\tfont-size: 160px;\r\n\tcolor: rgba(123, 67, 151, 0.03);\r\n\tz-index: 0;\r\n\tpointer-events: none;\r\n}\r\n\r\n.top-right {\r\n\ttop: 100px;\r\n\tright: 50px;\r\n}\r\n\r\n.bottom-left {\r\n\tbottom: 100px;\r\n\tleft: 50px;\r\n}\r\n\r\n/* 动画效果 */\r\n.fa-spin-pulse {\r\n\tanimation: spin-pulse 2s infinite alternate;\r\n}\r\n\r\n.fa-spin-slow {\r\n\tanimation: spin 10s linear infinite;\r\n}\r\n\r\n@keyframes spin-pulse {\r\n\t0% {\r\n\t\ttransform: scale(1) rotate(0);\r\n\t}\r\n\t100% {\r\n\t\ttransform: scale(1.1) rotate(15deg);\r\n\t}\r\n}\r\n\r\n@keyframes spin {\r\n\t0% {\r\n\t\ttransform: rotate(0);\r\n\t}\r\n\t100% {\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n\r\n/* 移动端适配优化 */\r\n@media (max-width: 768px) {\r\n\t.hero-section {\r\n\t\theight: 400px;\r\n\t}\r\n\r\n\t.hero-title {\r\n\t\tfont-size: 36px;\r\n\t}\r\n\r\n\t.hero-subtitle {\r\n\t\tfont-size: 18px;\r\n\t}\r\n\t\r\n\t.hero-buttons {\r\n\t\tflex-direction: column;\r\n\t\tgap: 15px;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.hero-btn {\r\n\t\twidth: 200px;\r\n\t}\r\n\r\n\t.decoration-element {\r\n\t\tfont-size: 100px;\r\n\t}\r\n\t\r\n\t.top-right {\r\n\t\ttop: 50px;\r\n\t\tright: 20px;\r\n\t}\r\n\t\r\n\t.bottom-left {\r\n\t\tbottom: 50px;\r\n\t\tleft: 20px;\r\n\t}\r\n}\r\n.crystal-badge {\r\n\tposition: absolute;\r\n\ttop: 15px;\r\n\tright: 15px;\r\n\tbackground: linear-gradient(135deg, #7b4397, #dc2430);\r\n\tcolor: white;\r\n\tpadding: 5px 10px;\r\n\tborder-radius: 25px;\r\n\tfont-size: 12px;\r\n\tfont-weight: 500;\r\n\tz-index: 2;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.crystal-btn-secondary {\r\n\tbackground: linear-gradient(135deg, #2bff00, #1900ff);\r\n\tcolor: white;\r\n\tborder: none;\r\n\tpadding: 10px 20px;\r\n\tborder-radius: 25px;\r\n\tfont-weight: 500;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s;\r\n\tbox-shadow: 0 4px 15px rgba(180, 180, 180, 0.18);\r\n\tmargin-left: 10px;\r\n\tanimation: none;\r\n}\r\n.crystal-btn-secondary:hover {\r\n\tbackground: linear-gradient(135deg, #2bff00, #1900ff);\r\n\tcolor: white;\r\n\ttransform: translateY(-2px);\r\n\tbox-shadow: 0 6px 20px rgba(180, 180, 180, 0.28);\r\n}\r\n\r\n/* 脉轮测试部分样式 */\r\n.section-chakra {\r\n\tbackground: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n\tpadding: 80px 0;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.chakra-test-container {\r\n\tmargin-top: 50px;\r\n}\r\n\r\n.chakra-intro {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 50px;\r\n\tmargin-bottom: 40px;\r\n}\r\n\r\n.chakra-intro-content {\r\n\tflex: 1;\r\n}\r\n\r\n.chakra-intro-content h3 {\r\n\tfont-size: 24px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20px;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.chakra-intro-content p {\r\n\tfont-size: 16px;\r\n\tline-height: 1.6;\r\n\tcolor: #666;\r\n\tmargin-bottom: 30px;\r\n}\r\n\r\n.chakra-features {\r\n\tdisplay: flex;\r\n\tgap: 30px;\r\n}\r\n\r\n.chakra-feature {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8px;\r\n\tfont-size: 14px;\r\n\tcolor: #555;\r\n}\r\n\r\n.chakra-feature i {\r\n\tcolor: #c9ab79;\r\n\tfont-size: 16px;\r\n}\r\n\r\n.chakra-intro-image {\r\n\tflex: 0 0 300px;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.chakra-circles {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 10px;\r\n\talign-items: center;\r\n}\r\n\r\n.chakra-circle {\r\n\twidth: 30px;\r\n\theight: 30px;\r\n\tborder-radius: 50%;\r\n\tbox-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n\tanimation: chakra-pulse 2s infinite;\r\n}\r\n\r\n@keyframes chakra-pulse {\r\n\t0%, 100% { transform: scale(1); }\r\n\t50% { transform: scale(1.1); }\r\n}\r\n\r\n.chakra-actions {\r\n\ttext-align: center;\r\n}\r\n\r\n.chakra-links {\r\n\tmargin-top: 20px;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\tgap: 30px;\r\n}\r\n\r\n.chakra-link {\r\n\tcolor: #c9ab79;\r\n\ttext-decoration: none;\r\n\tfont-size: 14px;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.chakra-link:hover {\r\n\tcolor: #b8996a;\r\n\ttransform: translateY(-2px);\r\n}\r\n\r\n.chakra-link i {\r\n\tmargin-right: 5px;\r\n}\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 768px) {\r\n\t.chakra-intro {\r\n\t\tflex-direction: column;\r\n\t\tgap: 30px;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.chakra-intro-image {\r\n\t\tflex: none;\r\n\t}\r\n\r\n\t.chakra-features {\r\n\t\tflex-direction: column;\r\n\t\tgap: 15px;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.chakra-links {\r\n\t\tflex-direction: column;\r\n\t\tgap: 15px;\r\n\t}\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiNA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA,OAAAC,OAAA;AACA,OAAAC,iBAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAL,MAAA;IAAAG;EAAA;EACAG,KAAA;IACA;MACAC,UAAA;MACAC,UAAA;MACAC,SAAA;MACAR,aAAA,EAAAA,aAAA;MACAS,OAAA;MACAC,QAAA;MACAC,SAAA;MAEA;MACAC,UAAA,IACA;MACA;MACAC,UAAA,IACA;MACA;MACAC,cAAA,GACA;QACAC,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAJ,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAJ,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAJ,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,KAAA;QACAC,IAAA;MACA,EACA;MACAC,eAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;IACA,KAAAC,cAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAC,eAAAZ,EAAA;MACA,KAAAa,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UAAAhB,EAAA,EAAAA;QAAA;MACA;IACA;IACAiB,SAAA;MACA,KAAAJ,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;IACAG,UAAA;MACA,KAAAL,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;IACA;IACAI,QAAAC,CAAA;MACA,SAAAnC,aAAA;QACAoC,IAAA,CAAAC,YAAA;UACAC,MAAA,GAAAH,CAAA;UAAA;UACAI,SAAA;QACA;MACA;IACA;;IACAhB,SAAA;MACA,MAAAiB,WAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,MAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,WAAA;MACA,KAAAM,UAAA;QACAC,MAAA,EAAAJ,QAAA,CAAAK,GAAA;MACA,GAAAC,IAAA,CAAAC,IAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;QACA,IAAAA,IAAA,CAAA7C,IAAA,IAAA6C,IAAA,CAAAG,IAAA;UACA,KAAAxC,UAAA,GAAAqC,IAAA,CAAA7C,IAAA,CAAAiD,MAAA;UACA,KAAA1C,UAAA,GAAAsC,IAAA,CAAA7C,IAAA,CAAAkD,OAAA;UACA,KAAA3C,UAAA,CAAA4C,OAAA,CAAAC,IAAA;YACAA,IAAA,CAAAC,IAAA,GAAAD,IAAA,CAAAC,IAAA,GAAAD,IAAA,CAAAC,IAAA,CAAAC,KAAA;UACA;UACA;QACA;UACA,KAAA9C,UAAA;UACA,KAAAD,UAAA;UACAX,OAAA,CAAA2D,KAAA,CAAAV,IAAA,CAAA7C,IAAA,CAAAwD,OAAA;QACA;MACA;IACA;IACArC,eAAA;MACA,KAAAsB,UAAA,sBAAAG,IAAA,CAAAC,IAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;QACA,IAAAA,IAAA,CAAA7C,IAAA,IAAA6C,IAAA,CAAAG,IAAA;UACA,KAAA/C,UAAA,GAAA4C,IAAA,CAAA7C,IAAA,CAAAC,UAAA;UACA,KAAAC,UAAA,GAAA2C,IAAA,CAAA7C,IAAA,CAAAE,UAAA;UACA,KAAAC,SAAA,GAAA0C,IAAA,CAAA7C,IAAA,CAAAG,SAAA;QACA,QACA;MACA;IACA;IACA;IACAsD,iBAAAC,MAAA;MACA,KAAAnC,OAAA,CAAAC,IAAA,mBAAAkC,MAAA,CAAAhD,EAAA;IACA;IACA;IACAiD,SAAAC,IAAA;MACAd,OAAA,CAAAC,GAAA,UAAAa,IAAA;MACA;MACA,IAAAA,IAAA;QACA,KAAArC,OAAA,CAAAC,IAAA;UACAC,IAAA;QACA;MACA,WAAAmC,IAAA;QACA,KAAArC,OAAA,CAAAC,IAAA;UACAC,IAAA;QACA;MACA;IACA;IACAoC,UAAAZ,MAAA;MACA,KAAAjC,WAAA,GAAAiC,MAAA;MACA,KAAAlC,eAAA;IACA;IACA+C,iBAAA;MACA,KAAA/C,eAAA;MACA,KAAAC,WAAA;IACA;IACA+C,aAAA;MACA,KAAA7C,QAAA;IACA;IACA;IACA8C,eAAA;MACA,KAAAzC,OAAA,CAAAC,IAAA;IACA;IACAyC,gBAAA;MACA,KAAA1C,OAAA,CAAAC,IAAA;IACA;IACA0C,kBAAA;MACA,KAAA3C,OAAA,CAAAC,IAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}