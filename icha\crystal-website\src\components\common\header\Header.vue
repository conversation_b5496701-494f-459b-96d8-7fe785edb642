<template>
	<div class="header-wrapper">
		<!-- 顶部栏 -->
		<div class="header">
			<div class="header-content">
				<div class="header-left">
					<img src="@/assets/images/logo.png" alt="Logo" />
				</div>
				<div class="header-mid">
					<div class="header-item">
						<strong>国际水晶疗愈协会</strong>
						<span v-if="!isMobilePhone" :style="'margin-left: 20px;'">International Crystal Healing Association, <span style="font-size: 12px;font-weight: 600;">ICHA</span></span>
						<span v-else>International Crystal Healing Association<br />ICHA</span>
					</div>
				</div>
				<div :class="isMobilePhone ? 'header-right-mobile' : 'header-right'">
					<button type="button" class="login-btn" @click="goLogin">{{userInfo && isLoggedIn ? (userInfo.nickname || userInfo.nickName) : '登录'}}</button>
					<!-- 移动端汉堡菜单按钮 -->
					<div :class="['menu-toggle', {'active': mobileMenuActive}]" @click="toggleMenu">
						<span></span>
						<span></span>
						<span></span>
					</div>
				</div>
			</div>
		</div>

		<!-- 导航菜单 -->
		<div :class="['nav-wrapper', 'header-default', {'mobile-active': mobileMenuActive}]">
			<div class="nav">
				<ul class="nav-list">
					<li v-for="(item, index) in navItems" :key="index" :style="getMobileItemStyle(index)">
						<router-link class="router" :to="item.path">{{ item.name }}</router-link>
					</li>
				</ul>
			</div>
		</div>

		<!-- 移动菜单背景遮罩 -->
		<div 
			class="mobile-menu-overlay" 
			v-if="isMobilePhone" 
			:class="{'active': mobileMenuActive}"
			@click="toggleMenu"
		></div>
	</div>
</template>

<script>
// import Try from "./components/try";
import AppFunctions from "@/utils/AppFunctions";
import { isMobilePhone } from "@/utils/index";
export default {
	name: "Header",
	components: {
		// Try,
	},
	data() {
		return {
			isLoggedIn: false,
			userInfo: {},
			isMobilePhone: isMobilePhone(),
			AppFunctions,
			mobileMenuActive: false,
			navItems: [
				{ name: '主页', path: '/index' },
				{ name: '关于我们', path: '/about' },
				{ name: '认证课程', path: '/course' },
				{ name: '工作坊', path: '/workshop' },
				// { name: '脉轮测试', path: '/chakra-test' },
				{ name: '证书查询', path: '/certificate' },
				{ name: '资料下载', path: '/download' },
				{ name: '加入我们', path: '/join' },
			]
		};
	},
	methods: {
		checkLoginStatus() {
			const token = localStorage.getItem("token");
			if (token) {
				try {
					const userInfoStr = localStorage.getItem("userInfo") ;
					if (userInfoStr) {
						this.userInfo = JSON.parse(userInfoStr);
						this.isLoggedIn = true;
					} else {
						// 有token但没有用户信息，尝试获取用户信息
						this.getUserInfo();
					}
				} catch (error) {
					console.error("解析用户信息失败", error);
					this.clearLoginInfo();
				}
			}
		},
		goLogin() {
			this.$router.push({
				path: '/login'
			})
		},
		tryHandle() {
			this.$router.push({
				name: 'try'
			})
		// 	this.tryVisible = true;
		// 	this.$nextTick(() => {
		// 		this.$refs.try.init();
		// 	});
		},
		toggleMenu() {
			this.mobileMenuActive = !this.mobileMenuActive;
			// 控制body滚动
			if (this.mobileMenuActive) {
				document.body.style.overflow = 'hidden';
			} else {
				document.body.style.overflow = '';
			}
		},
		getMobileItemStyle(index) {
			// 仅在移动端添加动画延迟
			if (this.isMobilePhone) {
				return {
					transitionDelay: `${index * 0.05}s`
				};
			}
			return {};
		},
		toggleStickyHeader() {
			if(isMobilePhone()){
				return;
			}
			const scrolled = document.documentElement.scrollTop;
			if (scrolled > 100) {
				AppFunctions.addClass(".header-default", "sticky");
			} else if (scrolled <= 100) {
				AppFunctions.removeClass(".header-default", "sticky");
			}
		},
		clearLoginInfo() {
			// 清除所有存储的登录信息
			sessionStorage.removeItem("token");
			sessionStorage.removeItem("userInfo");
			localStorage.removeItem("token");
			localStorage.removeItem("userInfo");
			this.isLoggedIn = false;
			this.userInfo = {};
		}
	},
	created() {
		window.addEventListener("scroll", this.toggleStickyHeader);
		this.checkLoginStatus();
	},
	mounted() {
		this.toggleStickyHeader();
	},
	beforeDestroy() {
		window.removeEventListener("scroll", this.toggleStickyHeader);
	},
};
</script>

<style scoped>
/* 基础样式 */
.header-wrapper {
	width: 100%;
	position: relative;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header {
	padding: 15px 20px;
	background-color: #fff;
}

.header-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	max-width: 1200px;
	margin: 0 auto;
}

/* Logo区域 */
.header-left img {
	height: 60px;
	width: auto;
}

/* 中间标题区域 */
.header-mid {
	flex: 1;
	margin: 0 20px;
}

.header-mid .item {
	display: flex;
	flex-direction: column;
}

.header-mid strong {
	font-size: 22px;
	color: #333;
	margin-bottom: 2px;
}

.header-mid span {
	font-size: 14px;
	color: #666;
}

/* 右侧登录按钮 */
.header-right {
	display: flex;
	align-items: center;
	margin-left:100px;
}
.header-right-mobile {
	display: flex;
	align-items: center;
}

.login-btn {
	background-color: #516790;
	color: white;
	border: none;
	padding: 8px 22px;
	border-radius: 4px;
	font-size: 14px;
	cursor: pointer;
	transition: background-color 0.3s;
}

.login-btn:hover {
	background-color: #3f5273;
}

/* 导航菜单 */
.nav-wrapper {
	background-color: #f8f8f8;
	border-top: 1px solid #eee;
	transition: all 0.3s ease;
	width: 100%;
}

.nav {
	max-width: 1200px;
	margin: 0 auto;
}

.nav-list {
	display: flex;
	justify-content: space-around;
	list-style: none;
	margin: 0;
	padding: 0;
}

.nav-list li {
	position: relative;
}

.router {
	display: block;
	padding: 15px 10px;
	color: #333;
	text-decoration: none;
	font-size: 16px;
	transition: color 0.3s;
	text-align: center;
}

.router:hover {
	color: #516790;
}

.router.router-link-active {
	cursor: default;
	font-weight: 600;
	color: #516790;
}

/* 粘性导航 */
.sticky {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 900;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
	animation: slideDown 0.3s ease;
}

@keyframes slideDown {
	from {
		transform: translateY(-100%);
	}
	to {
		transform: translateY(0);
	}
}

/* 汉堡菜单 */
.menu-toggle {
	display: none;
	flex-direction: column;
	justify-content: space-between;
	width: 30px;
	height: 20px;
	cursor: pointer;
	margin-left: 20px;
	position: relative;
	z-index: 1001;
}

.menu-toggle span {
	display: block;
	height: 2px;
	width: 90%;
	background-color: #333;
	border-radius: 3px;
	transition: transform 0.3s ease, opacity 0.2s ease;
	transform-origin: center;
}

/* 汉堡菜单变X动画 */
.menu-toggle.active span:nth-child(1) {
	transform: translateY(9px) rotate(45deg);
}

.menu-toggle.active span:nth-child(2) {
	opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
	transform: translateY(-9px) rotate(-45deg);
}

/* 移动菜单背景遮罩 */
.mobile-menu-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 999;
	visibility: hidden;
	opacity: 0;
	transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-menu-overlay.active {
	visibility: visible;
	opacity: 1;
}

/* 媒体查询 - 移动端适配 */
@media (max-width: 768px) {
	/* 移动端基础样式 */
	.header-wrapper {
		overflow-x: hidden;
	}

	.header-mid strong {
		font-size: 18px;
	}
	
	.header-mid span {
		font-size: 12px;
	}
	
	.menu-toggle {
		display: flex;
	}
	
	.login-btn {
		padding: 6px 15px;
		font-size: 13px;
	}
	
	/* 移动导航样式增强 */
	.nav-wrapper {
		position: fixed;
		top: 0;
		right: -280px;
		width: 280px;
		height: 100%;
		background-color: #fff;
		z-index: 1000;
		transition: transform 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
		box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
		overflow-y: auto;
		padding-top: 60px;
	}
	
	.nav-wrapper.sticky {
		right: -280px;
		position: fixed;
		animation: none;
	}
	
	.nav-wrapper.mobile-active {
		transform: translateX(-280px);
	}
	
	.nav-wrapper.sticky.mobile-active {
		transform: translateX(-280px);
	}
	
	.nav {
		max-width: 100%;
	}
	
	.nav-list {
		flex-direction: column;
		height: auto;
		padding: 20px 0;
	}
	
	.nav-list li {
		opacity: 0;
		transform: translateX(20px);
		transition: opacity 0.4s ease, transform 0.4s ease;
	}
	
	.mobile-active .nav-list li {
		opacity: 1;
		transform: translateX(0);
	}
	
	.router {
		padding: 15px 25px;
		text-align: left;
		border-bottom: 1px solid #eee;
	}
	
	.router:active {
		background-color: #f5f5f5;
	}
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
	.header-left img {
		height: 40px;
	}
	
	.header-mid {
		margin: 0 10px;
	}
	
	.header-mid strong {
		font-size: 16px;
	}
	
	.header-mid span {
		font-size: 10px;
	}
	
	.header {
		padding: 10px 15px;
	}
}
</style>
