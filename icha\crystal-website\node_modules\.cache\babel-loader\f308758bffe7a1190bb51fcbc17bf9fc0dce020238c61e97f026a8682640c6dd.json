{"ast": null, "code": "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Header from \"@/components/common/header/Header\";\nimport Footer from \"@/components/common/footer/Footer\";\nexport default {\n  name: \"Layout\",\n  components: {\n    Header,\n    Footer\n  }\n};", "map": {"version": 3, "names": ["Header", "Footer", "name", "components"], "sources": ["src/components/common/Layout.vue"], "sourcesContent": ["<template>\r\n\t<main class=\"page-wrapper\">\r\n\t\t<Header/>\r\n\t\t\r\n\t\t<div class=\"main-content\">\r\n\t\t\t<slot></slot>\r\n\t\t</div>\r\n\t\r\n\t\t<Footer/>\r\n\t</main>\r\n</template>\r\n\r\n<script>\r\nimport Header from \"@/components/common/header/Header\";\r\nimport Footer from \"@/components/common/footer/Footer\";\r\nexport default {\r\n\tname: \"Layout\",\r\n\tcomponents:{Header, Footer}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.main-content{\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AAaA,OAAAA,MAAA;AACA,OAAAC,MAAA;AACA;EACAC,IAAA;EACAC,UAAA;IAAAH,MAAA;IAAAC;EAAA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}