import request from '@/utils/request'

/**
 * 新增下载资源
 * @param data
 */
export function cmsDownloadCreateApi(data) {
    return request({
        url: '/admin/cms/download/save',
        method: 'POST',
        data
    })
}

/**
 * 下载资源更新
 * @param data
 */
export function cmsDownloadUpdateApi(data) {
    return request({
        url: '/admin/cms/download/update',
        method: 'POST',
        data
    })
}

/**
 * 下载资源详情
 * @param id
 */
export function cmsDownloadDetailApi(id) {
    return request({
        url: `/admin/cms/download/info/${id}`,
        method: 'GET'
    })
}

/**
 * 下载资源删除
 * @param ids 要删除的id数组
 */
export function cmsDownloadDeleteApi(ids) {
    return request({
        url: `/admin/cms/download/delete`,
        method: 'POST',
        data: ids
    })
}

/**
 * 下载资源列表
 * @param params
 */
export function cmsDownloadListApi(params) {
    return request({
        url: '/admin/cms/download/list',
        method: 'GET',
        params
    })
} 