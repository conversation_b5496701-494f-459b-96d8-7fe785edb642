package com.crystal.admin.controller;

import com.crystal.common.model.cms.CmsDownloadLogEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsDownloadLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 下载日志 控制器
 * @Author: 陈佳音
 * @date Mon May 14 22:50:31 CST 2024
 * @email <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/download/log")
public class CmsDownloadLogController {
    @Autowired
    private CmsDownloadLogService cmsDownloadLogService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('cmsdownloadlog:list')")
    public CommonResult<CommonPage<CmsDownloadLogEntity>> list(@Validated CmsDownloadLogEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsDownloadLogEntity> page = CommonPage.restPage(cmsDownloadLogService.queryPage(request,pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('cmsdownloadlog:info')")
    public CommonResult<CmsDownloadLogEntity> info(@PathVariable("id") Long id){
        CmsDownloadLogEntity cmsDownloadLog = cmsDownloadLogService.getById(id);
        return CommonResult.success(cmsDownloadLog);
    }
    
    /**
     * 根据下载ID查询日志列表
     */
    @RequestMapping("/download/{cmsDownloadId}")
//    @PreAuthorize("hasAuthority('cmsdownloadlog:list')")
    public CommonResult<List<CmsDownloadLogEntity>> listByDownloadId(@PathVariable("cmsDownloadId") Long cmsDownloadId){
        List<CmsDownloadLogEntity> list = cmsDownloadLogService.queryByDownloadId(cmsDownloadId);
        return CommonResult.success(list);
    }
    
    /**
     * 根据用户ID查询日志列表
     */
    @RequestMapping("/user/{userId}")
//    @PreAuthorize("hasAuthority('cmsdownloadlog:list')")
    public CommonResult<List<CmsDownloadLogEntity>> listByUserId(@PathVariable("userId") Integer userId){
        List<CmsDownloadLogEntity> list = cmsDownloadLogService.queryByUserId(userId);
        return CommonResult.success(list);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('cmsdownloadlog:save')")
    public CommonResult<String> save(@RequestBody CmsDownloadLogEntity cmsDownloadLog){
        cmsDownloadLog.setAddTime(new Date());
        cmsDownloadLogService.save(cmsDownloadLog);
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('cmsdownloadlog:update')")
    public CommonResult<String> update(@RequestBody CmsDownloadLogEntity cmsDownloadLog){
        cmsDownloadLogService.updateById(cmsDownloadLog);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('cmsdownloadlog:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids){
        if (cmsDownloadLogService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 