<template>
  <!-- 基于 Element UI 新增和修改弹窗 -->
  <el-dialog
    :title="!dataForm.id ? '添加-ADD' : '修改-EDITE'"
    :close-on-click-modal="false"
    :visible.sync="visible">
    <!-- 新增和创建表单表单 -->
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataSubmit()" label-width="80px">
    <el-form-item label="题目名称" prop="name">
      <el-input v-model="dataForm.name" placeholder="题目名称"></el-input>
    </el-form-item>
    <el-form-item label="题目类型 0-单选 1-多选 2-填空" prop="type">
      <el-input v-model="dataForm.type" placeholder="题目类型 0-单选 1-多选 2-填空"></el-input>
    </el-form-item>
    <el-form-item label="正确答案Id，type = 2不是id" prop="optionId">
      <el-input v-model="dataForm.optionId" placeholder="正确答案Id，type = 2不是id"></el-input>
    </el-form-item>
    <el-form-item label="选项，A,B,C,D" prop="charOptionId">
      <el-input v-model="dataForm.charOptionId" placeholder="选项，A,B,C,D"></el-input>
    </el-form-item>
    <el-form-item label="" prop="addTime">
      <el-input v-model="dataForm.addTime" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="" prop="updateTime">
      <el-input v-model="dataForm.updateTime" placeholder=""></el-input>
    </el-form-item>
    <el-form-item label="是否删除" prop="isDel">
      <el-input v-model="dataForm.isDel" placeholder="是否删除"></el-input>
    </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import * as api  from '@/api/question'
  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          id: 0,
          name: '' , 
          type: '' , 
          optionId: '' , 
          charOptionId: '' , 
          addTime: '' , 
          updateTime: '' , 
          isDel: '' 
        },
        dataRule: {
          name: [
            { required: true, message: '题目名称  为必填项', trigger: 'blur' }
          ],
          type: [
            { required: true, message: '题目类型 0-单选 1-多选 2-填空  为必填项', trigger: 'blur' }
          ],
          optionId: [
            { required: true, message: '正确答案Id，type = 2不是id  为必填项', trigger: 'blur' }
          ],
          charOptionId: [
            { required: true, message: '选项，A,B,C,D  为必填项', trigger: 'blur' }
          ],
          addTime: [
            { required: true, message: '  为必填项', trigger: 'blur' }
          ],
          updateTime: [
            { required: true, message: '  为必填项', trigger: 'blur' }
          ],
          isDel: [
            { required: true, message: '是否删除  为必填项', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) { // 初始化表单验证规则
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            api.questionDetailApi(id).then(res => {
                this.dataForm = res;
            })
          }
        })
      },
      // 表单数据提交
      dataSubmit () {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
                  api.QuestionCreateApi().then(res =>{
                      // TODO 保存数据
                  });
          }
        })
      }
    }
  }
</script>
