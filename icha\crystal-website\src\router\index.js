// import Vue from 'vue'
// import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
    {
        path:'/',
        redirect:'/index'
    },
    {
        path: '/index',
        name: 'index',
        component: () => import('../views/IndexView.vue')
    },
    {
        path: '/about',
        name: 'about',
        component: () => import('../views/AboutView.vue')
    },
    {
        path: '/workshop',
        name: 'workshop',
        component: () => import('../views/workshop.vue')
    },
    {
        path: '/course',
        name: 'course',
        component: () => import('../views/course.vue')
    },
    {
        path: '/certificate',
        name: 'certificate',
        component: () => import('../views/certificate.vue')
    },
    {
        path: '/download',
        name: 'download',
        component: () => import('../views/download.vue')
    },
    {
        path: '/join',
        name: 'join',
        component: () => import('../views/join.vue')
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('../views/login.vue')
    },
    {
        path: '/register',
        name: 'register',
        component: () => import('../views/register.vue')
    },
    {
        path: '/forgotPassword',
        name: 'forgotPassword',
        component: () => import('../views/ForgotPassword.vue')
    },
    {
        path: '/healers',
        name: 'healers',
        component: () => import('../views/HealersView.vue')
    },
    {
        path: '/healer-detail/:id',
        name: 'healerDetail',
        component: () => import('../views/HealerDetailView.vue')
    },
    {
        path: '/course-detail',
        name: 'courseDetail',
        component: () => import('../views/courseDetail.vue')
    },
    {
        path: '/chakra-test',
        name: 'chakraTest',
        component: () => import('../views/ChakraTest.vue')
    },
    {
        path: '/chakra-test/start',
        name: 'chakraTestStart',
        component: () => import('../views/ChakraTestStart.vue')
    },
    {
        path: '/chakra-test/detail',
        name: 'chakraTestDetail',
        component: () => import('../views/ChakraTestDetail.vue')
    },
    {
        path: '/chakra-test/list',
        name: 'chakraTestList',
        component: () => import('../views/ChakraTestList.vue')
    },
    {
        path: '/chakra-test/intro',
        name: 'chakraTestIntro',
        component: () => import('../views/ChakraTestIntro.vue')
    },
    {
        path: '/chakra-test/balance',
        name: 'chakraTestBalance',
        component: () => import('../views/ChakraTestBalance.vue')
    }
]

const router = new VueRouter({
    routes
})

export default router;
