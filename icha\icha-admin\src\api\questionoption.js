
import request from '@/utils/request'

/**
 * 新增QuestionOption
 * @param pram
 */
export function QuestionOptionCreateApi(data) {
    return request({
        url: 'questionoption/save',
        method: 'POST',
        data
    })
}

/**
 * questionoption更新
 * @param pram
 */
export function questionoptionUpdateApi(data) {
    return request({
        url: 'questionoption/update',
        method: 'POST',
        data
    })
}

/**
 * questionoption详情
 * @param pram
 */
export function questionoptionDetailApi(id) {
    return request({
        url: `questionoption/info/${id}`,
        method: 'GET'
    })
}
export function questionoptionfindByQuestionIdApi(id) {
    return request({
        url: `questionoption/findByQuestionId/${id}`,
        method: 'GET'
    })
}

/**
 * questionoption删除
 * @param pram
 */
export function questionoptionDeleteApi(id) {
    return request({
        url: `questionoption/delete/${id}`,
        method: 'get'
    })
}


/**
 * questionoption列表
 * @param pram
 */
export function questionoptionListApi(params) {
    return request({
        url: 'questionoption/list',
        method: 'GET',
        params
    })
}

