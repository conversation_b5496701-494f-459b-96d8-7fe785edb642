package com.crystal.admin.controller;

import com.crystal.common.model.user.UserRequirement;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.UserRequirementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 用户需求表 控制器
 * | Author: 陈佳音
 * ｜ @date Fri Nov 22 16:43:13 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/userrequirement")
public class UserRequirementController {
    @Autowired
    private UserRequirementService userRequirementService;

    /**
     * 根据用户ID查询需求列表
     */
    @RequestMapping("/findByUserId")
    public CommonResult<List<UserRequirement>> findByUserId(@RequestParam("userId") Integer userId) {
        List<UserRequirement> userRequirements = userRequirementService.findByUserId(userId);
        return CommonResult.success(userRequirements);
    }

    /**
     * 分页列表
     */
    @RequestMapping("/list")
    public CommonResult<CommonPage<UserRequirement>> list(@Validated UserRequirement request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<UserRequirement> page = CommonPage.restPage(userRequirementService.queryPage(request, pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
    public CommonResult<UserRequirement> info(@PathVariable("id") Integer id){
        UserRequirement userRequirement = userRequirementService.getById(id);
        return CommonResult.success(userRequirement);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> save(@RequestBody UserRequirement userRequirement){
        userRequirement.setAddTime(new Date());
        if (userRequirementService.save(userRequirement)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> update(@RequestBody UserRequirement userRequirement){
        if (userRequirementService.updateById(userRequirement)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
    public CommonResult<String> delete(@RequestBody Integer[] ids){
        if (userRequirementService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 