{"ast": null, "code": "//\n//\n//\n//\n//\n\nexport default {\n  watch: {\n    $route() {\n      // 路由切换时，重置body的overflow样式，确保移动端可以正常滚动\n      document.body.style.overflow = '';\n    }\n  }\n};", "map": {"version": 3, "names": ["watch", "$route", "document", "body", "style", "overflow"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\r\n\t<!-- :key=\"$route.fullPath\" 解决了路由前缀相同时跳转不刷新 -->\r\n  <router-view :key=\"$route.fullPath\"/>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  watch: {\r\n    $route() {\r\n      // 路由切换时，重置body的overflow样式，确保移动端可以正常滚动\r\n      document.body.style.overflow = '';\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n"], "mappings": ";;;;;;AAMA;EACAA,KAAA;IACAC,OAAA;MACA;MACAC,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}