# CRMEB 相关配置
crmeb:
  version: crystal-v1.0 # 当前代码版本

# 配置端口
server:
  port: 8026
  servlet:
    context-path: /         # 访问path
  tomcat:
    uri-encoding: UTF-8     # 默认编码格式
    max-threads: 1000       # 最大线程数量 默认200
    min-spare-threads: 30   # 初始化启动线程数量

spring:
  profiles:
    active: dev

debug: true
logging:
  level:
    io.swagger.*: error
    com.zbjk.crmeb: debug
    org.springframework.boot.autoconfigure: ERROR
  config: classpath:logback-spring.xml

# mybatis 配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*/*Mapper.xml #xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  # 配置slq打印日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
#      logic-delete-field: isDel  #全局逻辑删除字段值 3.3.0开始支持，详情看下面。
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)


#swagger 配置
swagger:
  basic:
    enable: true #是否开启
    check: false #是否打开验证
    username: #访问swagger的账号
    password: #访问swagger的密码

# Deepseek大模型配置
deepseek:
  api:
    url: https://api.deepseek.com/v1/chat/completions
    key: ***********************************  # 替换为实际的API密钥
  model: deepseek-chat 
  mock:
    enabled: false  # 启用模拟响应模式 