<template>
  <Layout>
    <div class="chakra-detail-page">
    <!-- 标题栏 -->
    <div class="nav-title">
      <div class="nav-left">
        <van-button 
          class="back-button"
          @click="goBack"
          plain
        >
          <van-icon name="arrow-left" size="18" />
        </van-button>
        <div class="color-bar"></div>
        <div class="title-text">脉轮测试结果</div>
      </div>
      <div class="nav-right">
        <van-button 
          class="intro-button-small"
          @click="goToIntro"
          size="small"
        >
          <van-icon name="info-o" size="14" />
          <span>脉轮简介</span>
        </van-button>
        <van-button 
          class="balance-button-small"
          @click="goToBalance"
          size="small"
          type="primary"
        >
          <van-icon name="balance" size="14" />
          <span>平衡脉轮</span>
        </van-button>
      </div>
    </div>
    
    <!-- 图表容器 -->
    <div class="chart-container" v-if="chartData.length > 0">
      <!-- Canvas图表 -->
      <canvas 
        ref="chartCanvas" 
        class="chart-canvas"
        @touchstart="handleTouch"
      ></canvas>
      
      <!-- 数据列表 -->
      <div class="data-list">
        <div 
          v-for="(item, index) in chartData" 
          :key="index"
          class="data-item"
        >
          <div class="chakra-name">{{ item.label }}({{ item.enLabel }})</div>
          <div class="chakra-value">{{ getValueText(item.value) }}({{ item.value }})</div>
        </div>
      </div>
    </div>
    
    
    <!-- 加载状态 -->
    <van-loading v-if="loading" class="page-loading" />
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/common/Layout.vue'
import Message from '@/utils/message'

export default {
  components: {
    Layout
  },
  name: 'ChakraTestDetail',
  data() {
    return {
      questionUserId: '',
      chartData: [],
      questionEntities: [],
      questionUserEntity: {},
      loading: true,
      animationFrame: 0,
      selectedBar: null
    }
  },
  mounted() {
    this.questionUserId = this.$route.query.questionUserId
    
    if (!this.questionUserId) {
      Message.error('参数错误')
      this.$router.back()
      return
    }
    
    this.getTestResult()
  },
  methods: {
    // 获取测试结果
    getTestResult() {
      this.loading = true
      this.getRequest('/question/finishDetail', { questionUserId: this.questionUserId }).then(res => {
        this.loading = false
        if (res.code == 200) {
          this.questionEntities = res.data.questionEntities || []
          this.questionUserEntity = res.data.questionUserEntity || {}
          this.chartData = [
            { label: "海底轮", enLabel: 'Root', value: this.questionUserEntity.root, color: "#993734" },
            { label: "脐轮", enLabel: 'Sacral', value: this.questionUserEntity.sacral, color: "#be6f2a" },
            { label: "太阳轮", enLabel: 'Solar Plexus', value: this.questionUserEntity.navel, color: "#d7c34a" },
            { label: "心轮", enLabel: 'Heart', value: this.questionUserEntity.heart, color: "#5f9057" },
            { label: "喉轮", enLabel: 'Throat', value: this.questionUserEntity.throat, color: "#5b8aa4" },
            { label: "眉心轮", enLabel: 'Third Eye', value: this.questionUserEntity.thirdEye, color: "#2c3485" },
            { label: "顶轮", enLabel: 'Crown', value: this.questionUserEntity.crown, color: "#7e4997" },
          ]
          this.$nextTick(() => {
            this.drawBarChart()
          })
        } else {
          Message.error(res.message || '获取结果失败')
          this.$router.back()
        }
      }).catch(err => {
        this.loading = false
        Message.error('获取结果失败')
        console.error('获取结果失败:', err)
      })
    },
    
    // 获取数值文本
    getValueText(value) {
      if (value <= 0) {
        return '不活跃'
      } else if (value > 0 && value <= 50) {
        return '已开启'
      } else {
        return '过分活跃'
      }
    },
    
    // 绘制柱状图
    drawBarChart() {
      const canvas = this.$refs.chartCanvas
      if (!canvas) return
      
      const ctx = canvas.getContext('2d')
      const dpr = window.devicePixelRatio || 1
      const canvasWidth = 370
      const canvasHeight = 300
      
      // 设置canvas实际大小
      canvas.width = canvasWidth * dpr
      canvas.height = canvasHeight * dpr
      canvas.style.width = canvasWidth + 'px'
      canvas.style.height = canvasHeight + 'px'
      
      // 缩放绘图上下文
      ctx.scale(dpr, dpr)
      
      const chartHeight = 200
      const barWidth = 25
      const gap = 20
      const baseY = 250
      const maxFrame = 60
      const startX = (canvasWidth - (this.chartData.length * barWidth + (this.chartData.length - 1) * gap)) / 2
      
      const animate = () => {
        // 清空画布
        ctx.clearRect(0, 0, canvasWidth, canvasHeight)
        ctx.fillStyle = "#FFFFFF"
        ctx.fillRect(0, 0, canvasWidth, canvasHeight)
        
        // 绘制x轴基线
        ctx.beginPath()
        ctx.strokeStyle = "#000000"
        ctx.lineWidth = 1
        ctx.moveTo(startX - 10, baseY)
        ctx.lineTo(startX + (this.chartData.length * (barWidth + gap)), baseY)
        ctx.stroke()
        
        // 绘制柱子
        this.chartData.forEach((item, index) => {
          const x = startX + index * (barWidth + gap)
          
          // 将值从[-100, 100]映射到[0, chartHeight]
          const normalizedValue = item.value + 100
          const height = (normalizedValue / 200) * chartHeight
          const currentHeight = (this.animationFrame / maxFrame) * height
          
          // 绘制柱子
          ctx.fillStyle = item.color
          ctx.fillRect(x, baseY - currentHeight, barWidth, currentHeight)
          
          // 绘制标签
          ctx.fillStyle = "#000000"
          ctx.font = "12px Arial"
          ctx.textAlign = "center"
          ctx.fillText(item.label, x + barWidth / 2, baseY + 20)
        })
        
        if (this.animationFrame < maxFrame) {
          this.animationFrame++
          requestAnimationFrame(animate)
        }
      }
      
      this.animationFrame = 0
      animate()
    },
    
    // 处理触摸事件
    handleTouch(e) {
      // 这里可以添加触摸交互逻辑
      console.log('图表被触摸')
    },
    
    // 跳转到脉轮简介
    goToIntro() {
      this.$router.push('/chakra-test/intro')
    },
    
    // 跳转到平衡脉轮
    goToBalance() {
      this.$router.push('/chakra-test/balance')
    },
    
    // 返回上一页
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.chakra-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.nav-title {
  padding: 15px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(86, 70, 128, 0.1);
  border-radius: 0 0 20px 20px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(86, 70, 128, 0.08);
  border: 1px solid rgba(86, 70, 128, 0.2);
  color: #564680;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(86, 70, 128, 0.15);
  border-color: rgba(86, 70, 128, 0.4);
  transform: translateX(-2px);
}

.color-bar {
  width: 6px;
  height: 25px;
  background: linear-gradient(135deg, #564680, #516790, #c9ab79);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(86, 70, 128, 0.3);
}

.title-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  letter-spacing: 0.5px;
}

.intro-button-small {
  background: linear-gradient(135deg, #564680, #516790);
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  color: white;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);
  transition: all 0.3s ease;
}

.intro-button-small:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);
}

.intro-button-small span {
  margin-left: 4px;
}

.balance-button-small {
  background: linear-gradient(135deg, #c9ab79, #b8996a);
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  color: white;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(201, 171, 121, 0.3);
  transition: all 0.3s ease;
}

.balance-button-small:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(201, 171, 121, 0.4);
}

.balance-button-small span {
  margin-left: 4px;
}

.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  margin: 0 25px;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(86, 70, 128, 0.1);
  border: 2px solid transparent;
  position: relative;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #564680, #516790, #c9ab79);
  border-radius: 20px 20px 0 0;
}

.chart-canvas {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(86, 70, 128, 0.08);
  border: 1px solid rgba(86, 70, 128, 0.1);
}

.data-list {
  width: 100%;
  margin-top: 25px;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(86, 70, 128, 0.08);
}

.data-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 20px;
  font-size: 15px;
  border-bottom: 1px solid rgba(86, 70, 128, 0.08);
  transition: all 0.3s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: linear-gradient(135deg, rgba(86, 70, 128, 0.02), rgba(201, 171, 121, 0.02));
    transform: translateX(5px);
  }
}

.chakra-name {
  color: #333;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.chakra-value {
  color: #666;
  font-weight: 500;
}


/* 移动端适配 */
@media (max-width: 768px) {
  .chart-container {
    margin: 0 20px;
    padding: 25px;
    border-radius: 16px;
  }
  
  .data-item {
    height: 45px;
    padding: 0 16px;
    font-size: 14px;
  }
  
  .nav-title {
    padding: 12px 20px;
    height: 55px;
  }
  
  .nav-left {
    gap: 8px;
  }
  
  .nav-right {
    gap: 6px;
  }
  
  .back-button {
    width: 36px;
    height: 36px;
  }
  
  .title-text {
    font-size: 18px;
  }
  
  .intro-button-small,
  .balance-button-small {
    padding: 5px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .nav-title {
    padding: 10px 15px;
    height: 50px;
  }
  
  .nav-left {
    gap: 6px;
  }
  
  .nav-right {
    gap: 4px;
  }
  
  .back-button {
    width: 32px;
    height: 32px;
  }
  
  .color-bar {
    width: 4px;
    height: 20px;
  }
  
  .title-text {
    font-size: 16px;
  }
  
  .intro-button-small,
  .balance-button-small {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .chart-container {
    margin: 0 15px;
    padding: 20px;
  }
  
  .chart-canvas {
    width: 100% !important;
    max-width: 320px;
  }
  
  .data-item {
    height: 42px;
    padding: 0 14px;
    font-size: 13px;
  }
  
  .chakra-name {
    font-size: 13px;
  }
  
  .chakra-value {
    font-size: 12px;
  }
}

.page-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
