package com.crystal.common.enums;

/**
 * 题目类型枚举类
 * 0-单选 1-多选 2-填空
 */
public enum ExamQuestionTypeEnum {
    RADIO(0, "单选"),
    CHECKBOX(1, "多选"),
    FILL(2, "填空");

    private Integer code;
    private String msg;

    ExamQuestionTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
    public static String getValueByCode(int code) {
        for (ExamQuestionTypeEnum typeEnum : ExamQuestionTypeEnum.values()) {
            if (typeEnum.code == code) {
                return typeEnum.msg;
            }
        }
        throw new IllegalArgumentException("No element matches " + code);
    }
    public static Integer getValueByMsg(String code) {
        for (ExamQuestionTypeEnum typeEnum : ExamQuestionTypeEnum.values()) {
            if (typeEnum.msg.equals(code)) {
                return typeEnum.code;
            }
        }
        throw new IllegalArgumentException("No element matches " + code);
    }
}
