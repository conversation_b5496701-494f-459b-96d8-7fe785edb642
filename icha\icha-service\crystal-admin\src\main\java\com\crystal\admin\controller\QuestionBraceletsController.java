package com.crystal.admin.controller;

import com.crystal.common.model.question.QuestionBraceletsEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.QuestionBraceletsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;


/**
 * 用户手串 控制器
 * | Author: 陈佳音
 * ｜ @date Fri Nov 22 16:43:13 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/questionbracelets")
public class QuestionBraceletsController {
    @Autowired
    private QuestionBraceletsService questionBraceletsService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:list')")
    public CommonResult<CommonPage<QuestionBraceletsEntity>> list(@Validated QuestionBraceletsEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<QuestionBraceletsEntity> page = CommonPage.restPage(questionBraceletsService.queryPage(request,pageParamRequest));

        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:info')")
    public CommonResult<QuestionBraceletsEntity> info(@PathVariable("id") Integer id){
		QuestionBraceletsEntity questionBracelets = questionBraceletsService.getById(id);

        return CommonResult.success(questionBracelets);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:save')")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> save(@RequestBody QuestionBraceletsEntity questionBracelets){
        questionBracelets.setAddTime(new Date());
        if (questionBraceletsService.save(questionBracelets)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
    @Transactional(rollbackFor = Exception.class)
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:update')")
    public CommonResult<String> update(@RequestBody QuestionBraceletsEntity questionBracelets){
        questionBracelets.setUpdateTime(new Date());
        if (questionBraceletsService.updateById(questionBracelets)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('braceletsbraceletsitem:delete')")
    public CommonResult<String> delete(@RequestBody Integer[] ids){
        if (questionBraceletsService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
