<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.crystal.service.dao.QuestionBraceletsDao">

	<!-- 根据包名 模块名 以及类名 生成Mapper XML 配置文件 -->
    <resultMap type="com.crystal.common.model.question.QuestionBraceletsEntity" id="questionBraceletsMap">
        <result property="id" column="id"/>
        <result property="addTime" column="add_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="startRoot" column="start_root"/>
        <result property="startSacral" column="start_sacral"/>
        <result property="startNavel" column="start_navel"/>
        <result property="startHeart" column="start_heart"/>
        <result property="startThroat" column="start_throat"/>
        <result property="startThirdEye" column="start_third_eye"/>
        <result property="startCrown" column="start_crown"/>
        <result property="endRoot" column="end_root"/>
        <result property="endSacral" column="end_sacral"/>
        <result property="endNavel" column="end_navel"/>
        <result property="endHeart" column="end_heart"/>
        <result property="endThroat" column="end_throat"/>
        <result property="endThirdEye" column="end_third_eye"/>
        <result property="endCrown" column="end_crown"/>
        <result property="userBraceletsId" column="user_bracelets_id"/>
        <result property="name" column="name"/>
        <result property="brief" column="brief"/>
    </resultMap>


</mapper>
