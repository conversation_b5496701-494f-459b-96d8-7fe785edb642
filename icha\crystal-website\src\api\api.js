// 引入 axios
import request from "./request";



//传送json格式的get请求
export const getRequest=(url,params)=>{
    return request({
        method:'get',
        url:`${url}`,
        params: params,
        headers: { 'appid': 'wx6a7f38e0347e6669' },
    })
}
//传送json格式的get请求
export const getWxRequest=(url,params)=>{
    return request({
        method:'get',
        url:`${url}`,
        params: params,
        headers: { 'appid': 'wx6a7f38e0347e6669' ,
        'wx-client-href':location.href},
    })
}
//传送json格式的get请求
export const postRequest=(url,data)=>{
    return request({
        method:'post',
        url:`${url}`,
        data: data,
        headers: { 'appid': 'wx6a7f38e0347e6669' },

    })
}
export const postRequestParams=(url,data)=>{
    return request({
        method:'post',
        url:`${url}`,
        params: data,
        headers: { 'appid': 'wx6a7f38e0347e6669' },

    })
}

/**
 * 脉轮测试配置
 */
export function mailunConfig() {
  return request({
    method: 'get',
    url: 'mailunConfig',
    headers: { 'appid': 'wx6a7f38e0347e6669' },
  });
}

/**
 * 开始脉轮测试
 */
export function questionStartExam(data) {
  return request({
    method: 'get',
    url: 'question/startExam',
    params: data,
    headers: { 'appid': 'wx6a7f38e0347e6669' },
  });
}

/**
 * 获取脉轮测试详情
 */
export function questionDetail(data) {
  return request({
    method: 'get',
    url: 'question/detail',
    params: data,
    headers: { 'appid': 'wx6a7f38e0347e6669' },
  });
}

/**
 * 提交脉轮测试
 */
export function questionSubmitExam(data) {
  return request({
    method: 'post',
    url: 'question/submitExam',
    data: data,
    headers: { 'appid': 'wx6a7f38e0347e6669' },
  });
}

/**
 * 暂存脉轮测试
 */
export function questionSaveExam(data) {
  return request({
    method: 'post',
    url: 'question/saveExam',
    data: data,
    headers: { 'appid': 'wx6a7f38e0347e6669' },
  });
}

/**
 * 获取脉轮测试结果详情
 */
export function questionFinishDetail(data) {
  return request({
    method: 'get',
    url: 'question/finishDetail',
    params: data,
    headers: { 'appid': 'wx6a7f38e0347e6669' },
  });
}

/**
 * 获取脉轮测试列表
 */
export function questionList(data) {
  return request({
    method: 'get',
    url: 'question/list',
    params: data,
    headers: { 'appid': 'wx6a7f38e0347e6669' },
  });
}

/**
 * 删除脉轮测试记录
 */
export function questionUserDelete(data) {
  return request({
    method: 'get',
    url: 'question/questionUserDelete',
    params: data,
    headers: { 'appid': 'wx6a7f38e0347e6669' },
  });
}
