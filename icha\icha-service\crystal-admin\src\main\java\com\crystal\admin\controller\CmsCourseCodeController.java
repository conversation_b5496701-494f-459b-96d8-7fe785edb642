package com.crystal.admin.controller;

import com.crystal.common.model.cms.CmsCourseCodeEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsCourseCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 课程邀请码 控制器
 * @Author: 陈佳音
 * @date Mon May 14 22:50:31 CST 2024
 * @email <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/course/code")
public class CmsCourseCodeController {
    @Autowired
    private CmsCourseCodeService cmsCourseCodeService;

    /**
     * 生成邀请码
     */
    @RequestMapping("/createCode")
    public CommonResult<String> create(@RequestParam("cmsCourseId") Long cmsCourseId, @RequestParam("number") Integer number) {
        cmsCourseCodeService.createInviteCode(cmsCourseId, number);
        return CommonResult.success();
    }
    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('cmscoursecode:list')")
    public CommonResult<CommonPage<CmsCourseCodeEntity>> list(@Validated CmsCourseCodeEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsCourseCodeEntity> page = CommonPage.restPage(cmsCourseCodeService.queryPage(request,pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('cmscoursecode:info')")
    public CommonResult<CmsCourseCodeEntity> info(@PathVariable("id") Long id){
        CmsCourseCodeEntity cmsCourseCode = cmsCourseCodeService.getById(id);
        return CommonResult.success(cmsCourseCode);
    }
    
    /**
     * 根据课程ID查询邀请码列表
     */
    @RequestMapping("/course/{cmsCourseId}")
//    @PreAuthorize("hasAuthority('cmscoursecode:list')")
    public CommonResult<List<CmsCourseCodeEntity>> listByCourseId(@PathVariable("cmsCourseId") Long cmsCourseId){
        List<CmsCourseCodeEntity> list = cmsCourseCodeService.queryByCourseId(cmsCourseId);
        return CommonResult.success(list);
    }
    
    /**
     * 根据用户ID查询邀请码列表
     */
    @RequestMapping("/user/{userId}")
//    @PreAuthorize("hasAuthority('cmscoursecode:list')")
    public CommonResult<List<CmsCourseCodeEntity>> listByUserId(@PathVariable("userId") Integer userId){
        List<CmsCourseCodeEntity> list = cmsCourseCodeService.queryByUserId(userId);
        return CommonResult.success(list);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('cmscoursecode:save')")
    public CommonResult<String> save(@RequestBody CmsCourseCodeEntity cmsCourseCode){
        cmsCourseCode.setAddTime(new Date());
        cmsCourseCodeService.save(cmsCourseCode);
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('cmscoursecode:update')")
    public CommonResult<String> update(@RequestBody CmsCourseCodeEntity cmsCourseCode){
        cmsCourseCodeService.updateById(cmsCourseCode);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('cmscoursecode:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids){
        if (cmsCourseCodeService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 