<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="dataForm.name" placeholder="姓名"></el-input>
      </el-form-item>
      <el-form-item label="联系方式" prop="phone">
        <el-input v-model="dataForm.phone" placeholder="联系方式"></el-input>
      </el-form-item>
      <!-- <el-form-item label="工作坊" prop="cmsWorkshopId">
        <el-select v-model="dataForm.cmsWorkshopId" placeholder="选择工作坊" filterable>
          <el-option v-for="item in workshopList" :key="item.id" :label="item.title" :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="报名人数" prop="number">
        <el-input-number v-model="dataForm.number" :min="1" :max="100" label="报名人数"></el-input-number>
      </el-form-item>
      <el-form-item label="备注" prop="message">
        <el-input type="textarea" v-model="dataForm.message" placeholder="备注"></el-input>
      </el-form-item>
      <!-- <el-form-item label="用户ID" prop="userId">
        <el-input-number v-model="dataForm.userId" :min="0" placeholder="用户ID"></el-input-number>
      </el-form-item> -->
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { cmsWorkshopAttendCreateApi, cmsWorkshopAttendUpdateApi, cmsWorkshopAttendDetailApi } from '@/api/cmsWorkshopAttend'
import { cmsWorkshopListApi } from '@/api/cmsWorkshop'
export default {
  data() {
    return {
      visible: false,
      workshopList: [],
      dataForm: {
        id: 0,
        name: '',
        phone: '',
        cmsWorkshopId: '',
        number: 1,
        message: '',
        userId: 0
      },
      dataRule: {
        name: [
          { required: true, message: '姓名不能为空', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '联系方式不能为空', trigger: 'blur' }
        ],
        cmsWorkshopId: [
          { required: true, message: '工作坊不能为空', trigger: 'change' }
        ],
        number: [
          { required: true, message: '报名人数不能为空', trigger: 'blur' }
        ],
        userId: [
          { required: true, message: '用户ID不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      // this.getWorkshopList()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          cmsWorkshopAttendDetailApi(this.dataForm.id).then((data) => {
            this.dataForm.name = data.name
            this.dataForm.phone = data.phone
            this.dataForm.cmsWorkshopId = data.cmsWorkshopId
            this.dataForm.number = data.number
            this.dataForm.message = data.message
            this.dataForm.userId = data.userId
          }).catch((res) => {
            this.$message.error(res.message)
          });
        }
      })
    },
    // 获取工作坊列表
    getWorkshopList() {
      cmsWorkshopListApi({ limit: 999 }).then(res => {
        this.workshopList = res.list || [];
      });
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.dataForm.id) {
            cmsWorkshopAttendCreateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          } else {
            cmsWorkshopAttendUpdateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          }
        }
      })
    }
  }
}
</script>