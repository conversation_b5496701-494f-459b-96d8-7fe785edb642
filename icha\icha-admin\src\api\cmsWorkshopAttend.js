import request from '@/utils/request'

/**
 * 新增工作坊报名记录
 * @param data
 */
export function cmsWorkshopAttendCreateApi(data) {
    return request({
        url: '/admin/cms/workshop/attend/save',
        method: 'POST',
        data
    })
}

/**
 * 工作坊报名记录更新
 * @param data
 */
export function cmsWorkshopAttendUpdateApi(data) {
    return request({
        url: '/admin/cms/workshop/attend/update',
        method: 'POST',
        data
    })
}

/**
 * 工作坊报名记录详情
 * @param id
 */
export function cmsWorkshopAttendDetailApi(id) {
    return request({
        url: `/admin/cms/workshop/attend/info/${id}`,
        method: 'GET'
    })
}

/**
 * 工作坊报名记录删除
 * @param ids 要删除的id数组
 */
export function cmsWorkshopAttendDeleteApi(ids) {
    return request({
        url: `/admin/cms/workshop/attend/delete`,
        method: 'POST',
        data: ids
    })
}

/**
 * 工作坊报名记录列表
 * @param params
 */
export function cmsWorkshopAttendListApi(params) {
    return request({
        url: '/admin/cms/workshop/attend/list',
        method: 'GET',
        params
    })
} 