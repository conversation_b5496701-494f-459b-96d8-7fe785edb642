/* 页面顶部背景区域通用样式 */
.hero-header-section {
	height: 300px;
	background: linear-gradient(45deg, #7b4397, #dc2430);
	background-size: cover;
	background-position: center;
	background-blend-mode: overlay;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	text-align: center;
	margin-bottom: 0;
	width: 100%;
}

.hero-header-section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(55, 30, 94, 0.7);
}

.hero-content {
	position: relative;
	z-index: 2;
	max-width: 800px;
	padding: 0 20px;
}

.hero-title {
	font-size: 42px;
	font-weight: 700;
	margin-bottom: 20px;
	text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
	animation: fadeInDown 1s ease-out;
}

.hero-subtitle {
	font-size: 18px;
	font-weight: 400;
	margin-bottom: 30px;
	text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
	animation: fadeInUp 1s ease-out;
}

/* 动画效果 */
@keyframes fadeInDown {
	from {
		opacity: 0;
		transform: translateY(-30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.fa-spin-pulse {
	animation: spin-pulse 2s infinite alternate;
}

@keyframes spin-pulse {
	0% {
		transform: scale(1) rotate(0);
	}
	100% {
		transform: scale(1.1) rotate(15deg);
	}
}

/* 移动端适配 */
@media (max-width: 768px) {
	.hero-header-section {
		height: 250px;
	}
	
	.hero-title {
		font-size: 32px;
	}
	
	.hero-subtitle {
		font-size: 16px;
	}
} 