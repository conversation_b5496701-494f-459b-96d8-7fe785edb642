<template>
  <div class="course-code-container">
    <!-- 搜索栏 -->
    <el-card class="search-container">
      <el-form :inline="true" :model="queryParams" ref="queryForm" size="small">
        <el-form-item label="课程ID" prop="cmsCourseId">
          <el-input v-model="queryParams.cmsCourseId" placeholder="请输入课程ID" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="邀请码" prop="inviteCode">
          <el-input v-model="queryParams.inviteCode" placeholder="请输入邀请码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="使用状态" prop="isUse">
          <el-select v-model="queryParams.isUse" placeholder="请选择使用状态" clearable>
            <el-option :value="0" label="未使用"></el-option>
            <el-option :value="1" label="已使用"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="table-container">
      <div class="table-header">
        <!-- <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button> -->
        <el-button type="success" icon="el-icon-s-promotion" size="small" @click="handleBatchCreate">批量生成</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple"
          @click="handleDelete">删除</el-button>
          <el-button type="primary" style="float: right;" size="small" @click="$router.go(-1)">返回上一页</el-button>
      </div>

      <!-- 邀请码表格 -->
      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="邀请码" align="center" prop="inviteCode" />
        <el-table-column label="使用状态" align="center" prop="isUse">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isUse === 1 ? 'success' : 'info'">
              {{ scope.row.isUse === 1 ? '已使用' : '未使用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="使用人" align="center" prop="userName" width="160" />
        <el-table-column label="使用人联系方式" align="center" prop="mobile" width="160" />
        <el-table-column label="使用时间" align="center" prop="useTime" width="160" />
        <el-table-column label="添加时间" align="center" prop="addTime" width="160" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button> -->
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            <!-- <el-button size="mini" type="text" @click="handleStatus(scope.row)">
              {{ scope.row.isUse === 1 ? '设为未使用' : '设为已使用' }}
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="block">
        <el-pagination :page-sizes="[20, 40, 60, 80]" :page-size="queryParams.limit"
          :current-page="queryParams.page" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="pageChange" />
      </div>
    </el-card>

    <!-- 新增/修改弹窗 -->
    <course-code-add-or-update ref="addOrUpdate" @refreshDataList="getList"></course-code-add-or-update>

    <!-- 批量生成邀请码弹窗 -->
    <el-dialog title="批量生成邀请码" :visible.sync="batchCreateVisible" width="500px" append-to-body>
      <el-form :model="batchForm" ref="batchForm" :rules="batchFormRules" label-width="100px">
        <el-form-item label="邀请码数量" prop="number">
          <el-input-number v-model="batchForm.number" :min="1" :max="100" controls-position="right"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchCreateVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBatchCreate" :loading="batchCreateLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  cmsCourseCodeListApi,
  cmsCourseCodeDeleteApi,
  cmsCourseCodeUpdateStatusApi,
  cmsCourseCodeCreateCodeApi
} from '@/api/cmsCourseCode'
import CourseCodeAddOrUpdate from './course-code-add-or-update'

export default {
  name: 'CourseCode',
  components: {
    CourseCodeAddOrUpdate
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 邀请码列表
      dataList: [],
      // 查询参数
      queryParams: {
        page: 1,
        limit: 20,
        cmsCourseId: undefined,
        userId: undefined,
        inviteCode: undefined,
        isUse: undefined
      },
      // 批量生成相关数据
      batchCreateVisible: false,
      batchCreateLoading: false,
      batchForm: {
        cmsCourseId: '',
        number: 1
      },
      batchFormRules: {
        cmsCourseId: [{ required: true, message: '课程ID不能为空', trigger: 'blur' }],
        number: [{ required: true, message: '请输入邀请码数量', trigger: 'blur' }]
      }
    }
  },
  created() {
    // 如果是从课程详情页跳转而来，自动填充课程ID
      this.queryParams.cmsCourseId = this.$route.query.id
    this.getList()
  },
  methods: {
    /** 查询邀请码列表 */
    getList() {
      this.loading = true
      cmsCourseCodeListApi(this.queryParams).then(res => {
        this.dataList = res.list || [];
        this.total = parseInt(res.total);
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      // 保留课程ID（如果是从课程详情页跳转而来）
      if (this.$route.query.cmsCourseId) {
        this.queryParams.cmsCourseId = this.$route.query.cmsCourseId
      }
      this.handleQuery()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.addOrUpdate.init(undefined, this.queryParams.cmsCourseId)
    },
    /** 批量生成邀请码操作 */
    handleBatchCreate() {
      this.batchCreateVisible = true
      this.batchForm.cmsCourseId = this.queryParams.cmsCourseId;
      this.batchForm.number = 50;
    },
    /** 提交批量生成邀请码 */
    submitBatchCreate() {
      this.$refs['batchForm'].validate(valid => {
        if (valid) {
          this.batchCreateLoading = true
          cmsCourseCodeCreateCodeApi({
            cmsCourseId: this.batchForm.cmsCourseId,
            number: this.batchForm.number
          }).then(res => {
            this.batchCreateLoading = false
            this.$message({
              message: `成功生成${this.batchForm.number}个邀请码`,
              type: 'success'
            })
            this.batchCreateVisible = false
            this.getList()
          }).catch(() => {
            this.batchCreateLoading = false
          })
        }
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.addOrUpdate.init(row.id)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除选中的数据?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return cmsCourseCodeDeleteApi([].concat(ids))
      }).then(() => {
        this.getList()
        this.$message.success('删除成功')
      }).catch(() => { })
    },
    /** 状态切换操作 */
    handleStatus(row) {
      const newStatus = row.isUse === 1 ? 0 : 1
      cmsCourseCodeUpdateStatusApi({ id: row.id, isUse: newStatus }).then(() => {
        this.getList()
        this.$message.success('状态更新成功')
      }).catch((res) => {
        this.$message.error(res.message)
      })
    },
    /** 页码改变 */
    pageChange(page) {
      this.queryParams.page = page
      this.getList()
    },
    /** 每页条数改变 */
    handleSizeChange(val) {
      this.queryParams.limit = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.course-code-container {
  padding: 20px;

  .search-container {
    margin-bottom: 20px;
  }

  .table-container {
    .table-header {
      margin-bottom: 20px;
    }
  }
}
</style> 