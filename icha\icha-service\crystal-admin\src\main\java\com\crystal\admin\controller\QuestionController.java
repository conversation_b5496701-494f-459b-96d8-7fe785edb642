package com.crystal.admin.controller;

import com.crystal.common.enums.ExamQuestionTypeEnum;
import com.crystal.common.model.question.QuestionEntity;
import com.crystal.common.model.question.QuestionOptionEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.QuestionOptionService;
import com.crystal.service.service.QuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 用户手串 控制器
 * | Author: 陈佳音
 * ｜ @date Fri Nov 22 16:43:13 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/question")
public class QuestionController {
    @Autowired
    private QuestionService questionService;
    @Autowired
    private QuestionOptionService questionOptionService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('userbraceletsitem:list')")
    public CommonResult<CommonPage<QuestionEntity>> list(@Validated QuestionEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<QuestionEntity> page = CommonPage.restPage(questionService.queryPage(request,pageParamRequest));

        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('userbraceletsitem:info')")
    public CommonResult<QuestionEntity> info(@PathVariable("id") Integer id){
		QuestionEntity question = questionService.getById(id);

        return CommonResult.success(question);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('userbraceletsitem:save')")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> save(@RequestBody QuestionEntity question){
        question.setAddTime(new Date());
        questionService.save(question);
        if (!question.getType().equals(ExamQuestionTypeEnum.FILL.getCode())) {
            // 如果不是填空
            List<QuestionOptionEntity> examQuestionOptionEntities = question.getQuestionOptionEntities();
            examQuestionOptionEntities.forEach(e -> {
                e.setQuestionId(question.getId());
            });
            questionOptionService.saveBatch(examQuestionOptionEntities);
            // 单选和多选变成塞id
            updateOptionId(question, examQuestionOptionEntities);
        } else {
            question.setOptionId(question.getCharOptionId());
        }
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
    @Transactional(rollbackFor = Exception.class)
//    @PreAuthorize("hasAuthority('userbraceletsitem:update')")
    public CommonResult<String> update(@RequestBody QuestionEntity examQuestion){

        examQuestion.setUpdateTime(new Date());
        examQuestion.setOptionId(examQuestion.getCharOptionId());
        questionService.updateById(examQuestion);
        if (!examQuestion.getType().equals(ExamQuestionTypeEnum.FILL.getCode())) {
            // 如果不是填空
            List<QuestionOptionEntity> examQuestionOptionEntities = examQuestion.getQuestionOptionEntities();
            List<QuestionOptionEntity> optionSave = new ArrayList<>();
            List<QuestionOptionEntity> optionUpdate = new ArrayList<>();
            examQuestionOptionEntities.forEach(e -> {
                if (e.getId() != null) {
                    optionUpdate.add(e);
                } else {
                    e.setQuestionId(examQuestion.getId());
                    optionSave.add(e);
                }
            });
            if (!CollectionUtils.isEmpty(optionSave)) {
                questionOptionService.saveBatch(optionSave);
            }
            if (!CollectionUtils.isEmpty(optionUpdate)) {
                questionOptionService.updateBatchById(optionUpdate);
            }
            // 单选和多选变成塞id
            optionSave.addAll(optionUpdate);
            updateOptionId(examQuestion, optionSave);

        }
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('userbraceletsitem:delete')")
    public CommonResult<String> delete(@RequestBody Integer[] ids){
        if (questionService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    private void updateOptionId(QuestionEntity examQuestion, List<QuestionOptionEntity> examQuestionOptionEntities) {
        // 单选和多选变成塞id
        if (examQuestion.getType().equals(ExamQuestionTypeEnum.RADIO.getCode())) {
            examQuestionOptionEntities.forEach(f -> {
                if (f.getOptionId().equals(examQuestion.getCharOptionId())) {
                    examQuestion.setOptionId(f.getId().toString());
                }
            });
            questionService.updateById(examQuestion);
        }
        if (examQuestion.getType().equals(ExamQuestionTypeEnum.CHECKBOX.getCode())) {
            String[] optionIds = examQuestion.getCharOptionId().split(",");
            List<Long> ids = new ArrayList<>();
            examQuestionOptionEntities.forEach(f -> {
                for (String s : optionIds) {
                    if (s.equals(f.getOptionId())) {
                        ids.add(f.getId());
                    }
                }
            });
            examQuestion.setOptionId(ids.stream().sorted().map(String::valueOf).collect(Collectors.joining(",")));
            examQuestion.setCharOptionId(Arrays.stream(optionIds).sorted().map(String::valueOf).collect(Collectors.joining(",")));
            questionService.updateById(examQuestion);
        }
    }

}
