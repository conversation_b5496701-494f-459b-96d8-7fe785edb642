{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport axios from \"axios\";\nimport Message from \"@/utils/message\";\nimport router from \"@/router\";\nlet base = \"api/front\";\nconst service = axios.create({\n  baseURL: base,\n  timeout: 60000 // 过期时间\n});\n\n// request interceptor\nservice.interceptors.request.use(config => {\n  // 发送请求之前做的\n  const token = localStorage.getItem(\"token\");\n  if (token) {\n    config.headers[\"Authori-zation\"] = token;\n  }\n  if (/get/i.test(config.method)) {\n    config.params = config.params || {};\n    config.params.temp = Date.parse(new Date()) / 1000;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// response interceptor\nservice.interceptors.response.use(response => {\n  const res = response.data;\n  // if the custom code is not 20000, it is judged as an error.\n  if (res.code === 401) {\n    // to re-login\n    Message.error(\"无效的会话，或者登录已过期，请重新登录。\");\n    // \n    router.push(\"/login?redirect=\" + encodeURIComponent(window.location.href));\n  } else if (res.code === 403) {\n    Message.error(\"没有权限访问。\");\n  }\n  if (res.code != 200 && res.code != 401) {\n    Message.error(res.message || \"Error\");\n    return res;\n  } else {\n    return res;\n  }\n}, error => {\n  Message.error(error.message);\n  return Promise.reject(error);\n});\nexport default service;", "map": {"version": 3, "names": ["axios", "Message", "router", "base", "service", "create", "baseURL", "timeout", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "test", "method", "params", "temp", "Date", "parse", "error", "Promise", "reject", "response", "res", "data", "code", "push", "encodeURIComponent", "window", "location", "href", "message"], "sources": ["C:/Users/<USER>/Desktop/code/crystal-mall/icha/crystal-website/src/api/request.js"], "sourcesContent": ["import axios from \"axios\";\r\nimport Message from \"@/utils/message\";\r\nimport router from \"@/router\";\r\nlet base = \"api/front\";\r\nconst service = axios.create({\r\n  baseURL: base,\r\n  timeout: 60000, // 过期时间\r\n});\r\n\r\n// request interceptor\r\nservice.interceptors.request.use(\r\n  (config) => {\r\n    // 发送请求之前做的\r\n    const token = localStorage.getItem(\"token\");\r\n    if (token) {\r\n      config.headers[\"Authori-zation\"] = token;\r\n    }\r\n    if (/get/i.test(config.method)) {\r\n      config.params = config.params || {};\r\n      config.params.temp = Date.parse(new Date()) / 1000;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// response interceptor\r\nservice.interceptors.response.use(\r\n  (response) => {\r\n      const res = response.data;\r\n    // if the custom code is not 20000, it is judged as an error.\r\n    if (res.code === 401) {\r\n      // to re-login\r\n      Message.error(\"无效的会话，或者登录已过期，请重新登录。\");\r\n      // \r\n      router.push(\"/login?redirect=\" + encodeURIComponent(window.location.href));\r\n    } else if (res.code === 403) {\r\n      Message.error(\"没有权限访问。\");\r\n    }\r\n    if (res.code != 200 && res.code != 401) {\r\n      Message.error(res.message || \"Error\");\r\n      return res;\r\n    } else {\r\n      return res;\r\n    }\r\n  },\r\n  (error) => {\r\n    Message.error(error.message);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default service;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,MAAM,MAAM,UAAU;AAC7B,IAAIC,IAAI,GAAG,WAAW;AACtB,MAAMC,OAAO,GAAGJ,KAAK,CAACK,MAAM,CAAC;EAC3BC,OAAO,EAAEH,IAAI;EACbI,OAAO,EAAE,KAAK,CAAE;AAClB,CAAC,CAAC;;AAEF;AACAH,OAAO,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC7BC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAAC,gBAAgB,CAAC,GAAGH,KAAK;EAC1C;EACA,IAAI,MAAM,CAACI,IAAI,CAACL,MAAM,CAACM,MAAM,CAAC,EAAE;IAC9BN,MAAM,CAACO,MAAM,GAAGP,MAAM,CAACO,MAAM,IAAI,CAAC,CAAC;IACnCP,MAAM,CAACO,MAAM,CAACC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;EACpD;EACA,OAAOT,MAAM;AACf,CAAC,EACAW,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAlB,OAAO,CAACI,YAAY,CAACiB,QAAQ,CAACf,GAAG,CAC9Be,QAAQ,IAAK;EACV,MAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;EAC3B;EACA,IAAID,GAAG,CAACE,IAAI,KAAK,GAAG,EAAE;IACpB;IACA3B,OAAO,CAACqB,KAAK,CAAC,sBAAsB,CAAC;IACrC;IACApB,MAAM,CAAC2B,IAAI,CAAC,kBAAkB,GAAGC,kBAAkB,CAACC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAAC;EAC5E,CAAC,MAAM,IAAIP,GAAG,CAACE,IAAI,KAAK,GAAG,EAAE;IAC3B3B,OAAO,CAACqB,KAAK,CAAC,SAAS,CAAC;EAC1B;EACA,IAAII,GAAG,CAACE,IAAI,IAAI,GAAG,IAAIF,GAAG,CAACE,IAAI,IAAI,GAAG,EAAE;IACtC3B,OAAO,CAACqB,KAAK,CAACI,GAAG,CAACQ,OAAO,IAAI,OAAO,CAAC;IACrC,OAAOR,GAAG;EACZ,CAAC,MAAM;IACL,OAAOA,GAAG;EACZ;AACF,CAAC,EACAJ,KAAK,IAAK;EACTrB,OAAO,CAACqB,KAAK,CAACA,KAAK,CAACY,OAAO,CAAC;EAC5B,OAAOX,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAelB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}