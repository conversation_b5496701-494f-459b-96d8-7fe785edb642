{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone } from \"@/utils/index\";\nimport Message from \"@/utils/message\";\nexport default {\n  name: \"ForgotPasswordView\",\n  data() {\n    return {\n      isMobilePhone: isMobilePhone(),\n      loading: false,\n      currentStep: 1,\n      counting: 0,\n      timer: null,\n      forgotForm: {\n        username: '',\n        phone: '',\n        verificationCode: '',\n        newPassword: '',\n        confirmPassword: ''\n      }\n    };\n  },\n  components: {\n    Layout\n  },\n  mounted() {\n    this.$wxShare();\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    // 验证两次密码是否一致\n    validateConfirmPassword() {\n      return this.forgotForm.newPassword == this.forgotForm.confirmPassword;\n    },\n    // 提交用户名\n    submitUsername() {\n      this.loading = true;\n      // 验证用户名是否存在\n      this.postRequest(\"/web/user/check-username\", {\n        username: this.forgotForm.username\n      }).then(resp => {\n        this.loading = false;\n        if (resp && resp.code == 200) {\n          // 用户存在，获取用户手机号（隐藏部分数字）\n          this.forgotForm.phone = resp.data.phone || '';\n          this.currentStep = 2;\n        } else {\n          Message.error(resp.message || \"未找到该用户，请检查用户名\");\n        }\n      });\n    },\n    // 发送验证码\n    sendVerificationCode() {\n      if (this.counting > 0) return;\n      this.postRequest(\"/web/user/send-verification\", {\n        username: this.forgotForm.username,\n        phone: this.forgotForm.phone\n      }).then(resp => {\n        if (resp && resp.code == 200) {\n          Message.success(\"验证码已发送\");\n          this.startCounting();\n        } else {\n          Message.error(resp.message || \"验证码发送失败\");\n        }\n      });\n    },\n    // 开始倒计时\n    startCounting() {\n      this.counting = 60;\n      this.timer = setInterval(() => {\n        if (this.counting > 0) {\n          this.counting--;\n        } else {\n          clearInterval(this.timer);\n        }\n      }, 1000);\n    },\n    // 提交验证码\n    submitVerification() {\n      this.loading = true;\n      this.postRequest(\"/web/user/verify-code\", {\n        username: this.forgotForm.username,\n        phone: this.forgotForm.phone,\n        code: this.forgotForm.verificationCode\n      }).then(resp => {\n        this.loading = false;\n        if (resp && resp.code == 200) {\n          this.currentStep = 3;\n        } else {\n          Message.error(resp.message || \"验证码错误或已过期\");\n        }\n      });\n    },\n    // 提交重置密码\n    submitReset() {\n      if (this.forgotForm.newPassword !== this.forgotForm.confirmPassword) {\n        Message.error(\"两次输入密码不一致\");\n        return;\n      }\n      this.loading = true;\n      this.postRequest(\"/web/user/reset-password\", {\n        username: this.forgotForm.username,\n        phone: this.forgotForm.phone,\n        code: this.forgotForm.verificationCode,\n        newPassword: this.forgotForm.newPassword\n      }).then(resp => {\n        this.loading = false;\n        if (resp && resp.code == 200) {\n          Message.success(\"密码重置成功\");\n          this.currentStep = 4;\n        } else {\n          Message.error(resp.message || \"密码重置失败\");\n        }\n      });\n    },\n    // 返回登录页\n    goToLogin() {\n      this.$router.push(\"/login\");\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "Message", "name", "data", "loading", "currentStep", "counting", "timer", "forgotForm", "username", "phone", "verificationCode", "newPassword", "confirmPassword", "components", "mounted", "$wxShare", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "validateConfirmPassword", "submitUsername", "postRequest", "then", "resp", "code", "error", "message", "sendVerificationCode", "success", "startCounting", "setInterval", "submitVerification", "submitReset", "goToLogin", "$router", "push"], "sources": ["src/views/ForgotPassword.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t<div class=\"forgot-page\">\r\n\t\t<div class=\"forgot-container\">\r\n\t\t\t<div class=\"forgot-left\">\r\n\t\t\t\t<div class=\"forgot-logo\">\r\n\t\t\t\t\t<img src=\"@/assets/images/logo.png\" alt=\"国际水晶疗愈协会\" class=\"logo-image\" />\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"forgot-welcome\">\r\n\t\t\t\t\t<h2>找回密码</h2>\r\n\t\t\t\t\t<h1>国际水晶疗愈协会</h1>\r\n\t\t\t\t\t<p>我们将协助您找回账户密码</p>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"forgot-decoration\">\r\n\t\t\t\t\t<div class=\"crystal-1\"></div>\r\n\t\t\t\t\t<div class=\"crystal-2\"></div>\r\n\t\t\t\t\t<div class=\"crystal-3\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<div class=\"forgot-right\">\r\n\t\t\t\t<div class=\"forgot-form-container\">\r\n\t\t\t\t\t<h2 class=\"forgot-title\">找回密码</h2>\r\n\t\t\t\t\t<p class=\"forgot-subtitle\">请按照步骤操作找回您的密码</p>\r\n\r\n\t\t\t\t\t<!-- 第一步：输入账号 -->\r\n\t\t\t\t\t<div v-if=\"currentStep == 1\">\r\n\t\t\t\t\t\t<van-form @submit=\"submitUsername\">\r\n\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<label for=\"username\">用户名/手机号</label>\r\n\t\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\t\tv-model=\"forgotForm.username\"\r\n\t\t\t\t\t\t\t\t\tname=\"username\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"请输入用户名或手机号\"\r\n\t\t\t\t\t\t\t\t\t:rules=\"[{ required: true, message: '请输入用户名/手机号' }]\"\r\n\t\t\t\t\t\t\t\t\tclass=\"forgot-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t\t<van-button round block type=\"primary\" native-type=\"submit\" :loading=\"loading\">\r\n\t\t\t\t\t\t\t\t\t下一步\r\n\t\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</van-form>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<!-- 第二步：验证身份 -->\r\n\t\t\t\t\t<div v-if=\"currentStep == 2\">\r\n\t\t\t\t\t\t<van-form @submit=\"submitVerification\">\r\n\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<label for=\"phone\">手机号</label>\r\n\t\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\t\tv-model=\"forgotForm.phone\"\r\n\t\t\t\t\t\t\t\t\tname=\"phone\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"请输入手机号\"\r\n\t\t\t\t\t\t\t\t\t:rules=\"[{ required: true, message: '请输入手机号' }]\"\r\n\t\t\t\t\t\t\t\t\tclass=\"forgot-input\"\r\n\t\t\t\t\t\t\t\t\t:disabled=\"forgotForm.phone !== ''\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<label for=\"verificationCode\">验证码</label>\r\n\t\t\t\t\t\t\t\t<div class=\"verification-code-container\">\r\n\t\t\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\t\t\tv-model=\"forgotForm.verificationCode\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"verificationCode\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入验证码\"\r\n\t\t\t\t\t\t\t\t\t\t:rules=\"[{ required: true, message: '请输入验证码' }]\"\r\n\t\t\t\t\t\t\t\t\t\tclass=\"forgot-input verification-input\"\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t<van-button \r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\" \r\n\t\t\t\t\t\t\t\t\t\ttype=\"primary\" \r\n\t\t\t\t\t\t\t\t\t\tclass=\"verification-btn\"\r\n\t\t\t\t\t\t\t\t\t\t:disabled=\"counting > 0\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"sendVerificationCode\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{{ counting > 0 ? `${counting}秒后重试` : '获取验证码' }}\r\n\t\t\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t\t<van-button round block type=\"primary\" native-type=\"submit\" :loading=\"loading\">\r\n\t\t\t\t\t\t\t\t\t下一步\r\n\t\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<div class=\"form-actions\">\r\n\t\t\t\t\t\t\t\t<a @click=\"currentStep = 1\" class=\"back-link\">返回上一步</a>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</van-form>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<!-- 第三步：重置密码 -->\r\n\t\t\t\t\t<div v-if=\"currentStep == 3\">\r\n\t\t\t\t\t\t<van-form @submit=\"submitReset\">\r\n\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<label for=\"newPassword\">新密码</label>\r\n\t\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\t\tv-model=\"forgotForm.newPassword\"\r\n\t\t\t\t\t\t\t\t\ttype=\"password\"\r\n\t\t\t\t\t\t\t\t\tname=\"newPassword\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"请输入新密码\"\r\n\t\t\t\t\t\t\t\t\t:rules=\"[\r\n\t\t\t\t\t\t\t\t\t\t{ required: true, message: '请输入新密码' },\r\n\t\t\t\t\t\t\t\t\t\t{ pattern: /^.{8,20}$/, message: '密码长度为8-20个字符' }\r\n\t\t\t\t\t\t\t\t\t]\"\r\n\t\t\t\t\t\t\t\t\tclass=\"forgot-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t<label for=\"confirmPassword\">确认密码</label>\r\n\t\t\t\t\t\t\t\t<van-field\r\n\t\t\t\t\t\t\t\t\tv-model=\"forgotForm.confirmPassword\"\r\n\t\t\t\t\t\t\t\t\ttype=\"password\"\r\n\t\t\t\t\t\t\t\t\tname=\"confirmPassword\"\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"请再次输入新密码\"\r\n\t\t\t\t\t\t\t\t\t:rules=\"[\r\n\t\t\t\t\t\t\t\t\t\t{ required: true, message: '请确认新密码' },\r\n\t\t\t\t\t\t\t\t\t\t{ validator: validateConfirmPassword, message: '两次输入密码不一致' }\r\n\t\t\t\t\t\t\t\t\t]\"\r\n\t\t\t\t\t\t\t\t\tclass=\"forgot-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t\t<van-button round block type=\"primary\" native-type=\"submit\" :loading=\"loading\">\r\n\t\t\t\t\t\t\t\t\t重置密码\r\n\t\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<div class=\"form-actions\">\r\n\t\t\t\t\t\t\t\t<a @click=\"currentStep = 2\" class=\"back-link\">返回上一步</a>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</van-form>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<!-- 重置成功 -->\r\n\t\t\t\t\t<div v-if=\"currentStep == 4\" class=\"reset-success\">\r\n\t\t\t\t\t\t<van-icon name=\"success\" size=\"64\" color=\"#5e258f\" />\r\n\t\t\t\t\t\t<h3>密码重置成功</h3>\r\n\t\t\t\t\t\t<p>您已成功重置密码，现在可以使用新密码登录</p>\r\n\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t<van-button round block type=\"primary\" @click=\"goToLogin\">\r\n\t\t\t\t\t\t\t\t返回登录\r\n\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div class=\"login-link\">\r\n\t\t\t\t\t\t<span>已经记起密码？</span>\r\n\t\t\t\t\t\t<router-link to=\"/login\">立即登录</router-link>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport Message from \"@/utils/message\";\r\n\r\nexport default {\r\n\tname: \"ForgotPasswordView\",\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tloading: false,\r\n\t\t\tcurrentStep: 1,\r\n\t\t\tcounting: 0,\r\n\t\t\ttimer: null,\r\n\t\t\tforgotForm: {\r\n\t\t\t\tusername: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tverificationCode: '',\r\n\t\t\t\tnewPassword: '',\r\n\t\t\t\tconfirmPassword: ''\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tcomponents: {\r\n\t\tLayout\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$wxShare();\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\tif (this.timer) {\r\n\t\t\tclearInterval(this.timer);\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 验证两次密码是否一致\r\n\t\tvalidateConfirmPassword() {\r\n\t\t\treturn this.forgotForm.newPassword == this.forgotForm.confirmPassword;\r\n\t\t},\r\n\t\t\r\n\t\t// 提交用户名\r\n\t\tsubmitUsername() {\r\n\t\t\tthis.loading = true;\r\n\t\t\t// 验证用户名是否存在\r\n\t\t\tthis.postRequest(\"/web/user/check-username\", { username: this.forgotForm.username })\r\n\t\t\t\t.then(resp => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\t\t// 用户存在，获取用户手机号（隐藏部分数字）\r\n\t\t\t\t\t\tthis.forgotForm.phone = resp.data.phone || '';\r\n\t\t\t\t\t\tthis.currentStep = 2;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tMessage.error(resp.message || \"未找到该用户，请检查用户名\");\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 发送验证码\r\n\t\tsendVerificationCode() {\r\n\t\t\tif (this.counting > 0) return;\r\n\t\t\t\r\n\t\t\tthis.postRequest(\"/web/user/send-verification\", { \r\n\t\t\t\tusername: this.forgotForm.username,\r\n\t\t\t\tphone: this.forgotForm.phone \r\n\t\t\t})\r\n\t\t\t\t.then(resp => {\r\n\t\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\t\tMessage.success(\"验证码已发送\");\r\n\t\t\t\t\t\tthis.startCounting();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tMessage.error(resp.message || \"验证码发送失败\");\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 开始倒计时\r\n\t\tstartCounting() {\r\n\t\t\tthis.counting = 60;\r\n\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\tif (this.counting > 0) {\r\n\t\t\t\t\tthis.counting--;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tclearInterval(this.timer);\r\n\t\t\t\t}\r\n\t\t\t}, 1000);\r\n\t\t},\r\n\t\t\r\n\t\t// 提交验证码\r\n\t\tsubmitVerification() {\r\n\t\t\tthis.loading = true;\r\n\t\t\tthis.postRequest(\"/web/user/verify-code\", {\r\n\t\t\t\tusername: this.forgotForm.username,\r\n\t\t\t\tphone: this.forgotForm.phone,\r\n\t\t\t\tcode: this.forgotForm.verificationCode\r\n\t\t\t})\r\n\t\t\t\t.then(resp => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\t\tthis.currentStep = 3;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tMessage.error(resp.message || \"验证码错误或已过期\");\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 提交重置密码\r\n\t\tsubmitReset() {\r\n\t\t\tif (this.forgotForm.newPassword !== this.forgotForm.confirmPassword) {\r\n\t\t\t\tMessage.error(\"两次输入密码不一致\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.loading = true;\r\n\t\t\tthis.postRequest(\"/web/user/reset-password\", {\r\n\t\t\t\tusername: this.forgotForm.username,\r\n\t\t\t\tphone: this.forgotForm.phone,\r\n\t\t\t\tcode: this.forgotForm.verificationCode,\r\n\t\t\t\tnewPassword: this.forgotForm.newPassword\r\n\t\t\t})\r\n\t\t\t\t.then(resp => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\t\tMessage.success(\"密码重置成功\");\r\n\t\t\t\t\t\tthis.currentStep = 4;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tMessage.error(resp.message || \"密码重置失败\");\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 返回登录页\r\n\t\tgoToLogin() {\r\n\t\t\tthis.$router.push(\"/login\");\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.forgot-page {\r\n\tmin-height: 100vh;\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground-color: #f8f9fa;\r\n\tbackground-image: url('@/assets/images/pattern-dark.png');\r\n\tbackground-repeat: repeat;\r\n\tpadding: 40px 20px;\r\n}\r\n\r\n.forgot-container {\r\n\tdisplay: flex;\r\n\twidth: 1200px;\r\n\tmin-height: 680px;\r\n\tborder-radius: 20px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.forgot-left {\r\n\tflex: 1;\r\n\tbackground: linear-gradient(135deg, #5e258f, #8647ad);\r\n\tbackground-image: linear-gradient(135deg, rgba(94, 37, 143, 0.95), rgba(134, 71, 173, 0.9)), url('https://images.unsplash.com/photo-1521320226546-87b106cd055d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');\r\n\tbackground-size: cover;\r\n\tbackground-position: center;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tposition: relative;\r\n\tcolor: #fff;\r\n\tpadding: 50px;\r\n\toverflow: hidden;\r\n}\r\n\r\n.forgot-logo {\r\n\tmargin-bottom: 40px;\r\n\ttext-align: center;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.logo-image {\r\n\twidth: 180px;\r\n\theight: auto;\r\n}\r\n\r\n.forgot-welcome {\r\n\ttext-align: center;\r\n\tmargin-bottom: 40px;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.forgot-welcome h2 {\r\n\tfont-size: 24px;\r\n\tfont-weight: 400;\r\n\tmargin-bottom: 20px;\r\n\tcolor: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.forgot-welcome h1 {\r\n\tfont-size: 38px;\r\n\tfont-weight: 700;\r\n\tmargin-bottom: 20px;\r\n\ttext-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.forgot-welcome p {\r\n\tfont-size: 16px;\r\n\tline-height: 1.6;\r\n\tmax-width: 400px;\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.forgot-decoration {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.crystal-1, .crystal-2, .crystal-3 {\r\n\tposition: absolute;\r\n\tbackground: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));\r\n\tbackdrop-filter: blur(5px);\r\n\tborder: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.crystal-1 {\r\n\twidth: 300px;\r\n\theight: 300px;\r\n\ttop: -100px;\r\n\tleft: -150px;\r\n}\r\n\r\n.crystal-2 {\r\n\twidth: 200px;\r\n\theight: 200px;\r\n\tbottom: 50px;\r\n\tright: -50px;\r\n}\r\n\r\n.crystal-3 {\r\n\twidth: 150px;\r\n\theight: 150px;\r\n\tbottom: -50px;\r\n\tleft: 100px;\r\n}\r\n\r\n.forgot-right {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 60px;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.forgot-form-container {\r\n\twidth: 100%;\r\n\tmax-width: 400px;\r\n}\r\n\r\n.forgot-title {\r\n\tfont-size: 32px;\r\n\tfont-weight: 700;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10px;\r\n\ttext-align: center;\r\n}\r\n\r\n.forgot-subtitle {\r\n\tfont-size: 16px;\r\n\tcolor: #666;\r\n\tmargin-bottom: 40px;\r\n\ttext-align: center;\r\n}\r\n\r\n.form-item {\r\n\tmargin-bottom: 25px;\r\n}\r\n\r\n.form-item label {\r\n\tdisplay: block;\r\n\tmargin-bottom: 8px;\r\n\tfont-size: 14px;\r\n\tcolor: #555;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.forgot-input {\r\n\t/deep/ .van-field__control {\r\n\t\theight: 48px;\r\n\t\tpadding: 0 15px;\r\n\t\tfont-size: 15px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder: 1px solid #e8eaee;\r\n\t\tborder-radius: 8px;\r\n\t}\r\n\r\n\t/deep/ .van-field__control:focus {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-color: #5e258f;\r\n\t\tbox-shadow: 0 0 0 3px rgba(94, 37, 143, 0.1);\r\n\t}\r\n}\r\n\r\n.verification-code-container {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.verification-input {\r\n\tflex: 1;\r\n\tmargin-right: 10px;\r\n}\r\n\r\n.verification-btn {\r\n\theight: 48px;\r\n\tbackground: linear-gradient(45deg, #5e258f, #8647ad);\r\n\tborder-color: #5e258f;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.form-submit {\r\n\tmargin-bottom: 25px;\r\n\t\r\n\t.van-button {\r\n\t\theight: 50px;\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 500;\r\n\t\tbackground: linear-gradient(45deg, #5e258f, #8647ad);\r\n\t\tborder-color: #5e258f;\r\n\t\ttransition: all 0.3s ease;\r\n\t\t\r\n\t\t&:hover {\r\n\t\t\tbackground: linear-gradient(45deg, #4b1e73, #733c94);\r\n\t\t\tborder-color: #4b1e73;\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 6px 15px rgba(94, 37, 143, 0.25);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.form-actions {\r\n\ttext-align: center;\r\n\tmargin-bottom: 20px;\r\n\t\r\n\t.back-link {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 14px;\r\n\t\ttext-decoration: none;\r\n\t\tcursor: pointer;\r\n\t\t\r\n\t\t&:hover {\r\n\t\t\tcolor: #5e258f;\r\n\t\t\ttext-decoration: underline;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.login-link {\r\n\ttext-align: center;\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n\t\r\n\ta {\r\n\t\tcolor: #5e258f;\r\n\t\tfont-weight: 500;\r\n\t\ttext-decoration: none;\r\n\t\tmargin-left: 5px;\r\n\t\t\r\n\t\t&:hover {\r\n\t\t\ttext-decoration: underline;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.reset-success {\r\n\ttext-align: center;\r\n\tpadding: 20px 0 40px;\r\n\t\r\n\th3 {\r\n\t\tfont-size: 24px;\r\n\t\tfont-weight: 600;\r\n\t\tmargin: 20px 0 10px;\r\n\t\tcolor: #333;\r\n\t}\r\n\t\r\n\tp {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n\t.forgot-container {\r\n\t\twidth: 95%;\r\n\t\tflex-direction: column;\r\n\t\tmin-height: auto;\r\n\t}\r\n\t\r\n\t.forgot-left {\r\n\t\tpadding: 40px 20px;\r\n\t}\r\n\t\r\n\t.forgot-right {\r\n\t\tpadding: 40px 20px;\r\n\t}\r\n\t\r\n\t.forgot-welcome h1 {\r\n\t\tfont-size: 32px;\r\n\t}\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\t.forgot-page {\r\n\t\tpadding: 20px 10px;\r\n\t}\r\n\t\r\n\t.forgot-container {\r\n\t\twidth: 100%;\r\n\t\tborder-radius: 10px;\r\n\t}\r\n\t\r\n\t.forgot-left {\r\n\t\tpadding: 30px 15px;\r\n\t}\r\n\t\r\n\t.forgot-right {\r\n\t\tpadding: 30px 15px;\r\n\t}\r\n\t\r\n\t.forgot-logo .logo-image {\r\n\t\twidth: 150px;\r\n\t}\r\n\t\r\n\t.forgot-welcome h1 {\r\n\t\tfont-size: 28px;\r\n\t}\r\n\t\r\n\t.forgot-welcome p {\r\n\t\tfont-size: 14px;\r\n\t}\r\n\t\r\n\t.forgot-title {\r\n\t\tfont-size: 28px;\r\n\t}\r\n\t\r\n\t.forgot-subtitle {\r\n\t\tfont-size: 14px;\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAH,aAAA,EAAAA,aAAA;MACAI,OAAA;MACAC,WAAA;MACAC,QAAA;MACAC,KAAA;MACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,eAAA;MACA;IACA;EACA;EACAC,UAAA;IACAf;EACA;EACAgB,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,cAAA;IACA,SAAAV,KAAA;MACAW,aAAA,MAAAX,KAAA;IACA;EACA;EACAY,OAAA;IACA;IACAC,wBAAA;MACA,YAAAZ,UAAA,CAAAI,WAAA,SAAAJ,UAAA,CAAAK,eAAA;IACA;IAEA;IACAQ,eAAA;MACA,KAAAjB,OAAA;MACA;MACA,KAAAkB,WAAA;QAAAb,QAAA,OAAAD,UAAA,CAAAC;MAAA,GACAc,IAAA,CAAAC,IAAA;QACA,KAAApB,OAAA;QACA,IAAAoB,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA;UACA,KAAAjB,UAAA,CAAAE,KAAA,GAAAc,IAAA,CAAArB,IAAA,CAAAO,KAAA;UACA,KAAAL,WAAA;QACA;UACAJ,OAAA,CAAAyB,KAAA,CAAAF,IAAA,CAAAG,OAAA;QACA;MACA;IACA;IAEA;IACAC,qBAAA;MACA,SAAAtB,QAAA;MAEA,KAAAgB,WAAA;QACAb,QAAA,OAAAD,UAAA,CAAAC,QAAA;QACAC,KAAA,OAAAF,UAAA,CAAAE;MACA,GACAa,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACAxB,OAAA,CAAA4B,OAAA;UACA,KAAAC,aAAA;QACA;UACA7B,OAAA,CAAAyB,KAAA,CAAAF,IAAA,CAAAG,OAAA;QACA;MACA;IACA;IAEA;IACAG,cAAA;MACA,KAAAxB,QAAA;MACA,KAAAC,KAAA,GAAAwB,WAAA;QACA,SAAAzB,QAAA;UACA,KAAAA,QAAA;QACA;UACAY,aAAA,MAAAX,KAAA;QACA;MACA;IACA;IAEA;IACAyB,mBAAA;MACA,KAAA5B,OAAA;MACA,KAAAkB,WAAA;QACAb,QAAA,OAAAD,UAAA,CAAAC,QAAA;QACAC,KAAA,OAAAF,UAAA,CAAAE,KAAA;QACAe,IAAA,OAAAjB,UAAA,CAAAG;MACA,GACAY,IAAA,CAAAC,IAAA;QACA,KAAApB,OAAA;QACA,IAAAoB,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAApB,WAAA;QACA;UACAJ,OAAA,CAAAyB,KAAA,CAAAF,IAAA,CAAAG,OAAA;QACA;MACA;IACA;IAEA;IACAM,YAAA;MACA,SAAAzB,UAAA,CAAAI,WAAA,UAAAJ,UAAA,CAAAK,eAAA;QACAZ,OAAA,CAAAyB,KAAA;QACA;MACA;MAEA,KAAAtB,OAAA;MACA,KAAAkB,WAAA;QACAb,QAAA,OAAAD,UAAA,CAAAC,QAAA;QACAC,KAAA,OAAAF,UAAA,CAAAE,KAAA;QACAe,IAAA,OAAAjB,UAAA,CAAAG,gBAAA;QACAC,WAAA,OAAAJ,UAAA,CAAAI;MACA,GACAW,IAAA,CAAAC,IAAA;QACA,KAAApB,OAAA;QACA,IAAAoB,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACAxB,OAAA,CAAA4B,OAAA;UACA,KAAAxB,WAAA;QACA;UACAJ,OAAA,CAAAyB,KAAA,CAAAF,IAAA,CAAAG,OAAA;QACA;MACA;IACA;IAEA;IACAO,UAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}