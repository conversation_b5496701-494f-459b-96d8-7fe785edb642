import request from '@/utils/request'

/**
 * 获取课程报名列表
 * @param {Object} params 查询参数
 */
export function cmsCourseAttendListApi(params) {
    return request({
        url: '/admin/cms/course/attend/list',
        method: 'get',
        params
    })
}

/**
 * 获取课程报名详情
 * @param {Number} id 报名ID
 */
export function cmsCourseAttendInfoApi(id) {
    return request({
        url: `/admin/cms/course/attend/info/${id}`,
        method: 'get'
    })
}

/**
 * 新增课程报名
 * @param {Object} data 报名信息
 */
export function cmsCourseAttendAddApi(data) {
    return request({
        url: '/admin/cms/course/attend/save',
        method: 'post',
        data
    })
}

/**
 * 更新课程报名
 * @param {Object} data 报名信息
 */
export function cmsCourseAttendUpdateApi(data) {
    return request({
        url: '/admin/cms/course/attend/update',
        method: 'post',
        data
    })
}

/**
 * 删除课程报名
 * @param {Array} ids 报名ID数组
 */
export function cmsCourseAttendDeleteApi(ids) {
    return request({
        url: '/admin/cms/course/attend/delete',
        method: 'post',
        data: ids
    })
}

/**
 * 更新课程报名状态
 * @param {Object} data 包含id和状态信息
 */
export function cmsCourseAttendUpdateStatusApi(data) {
    return request({
        url: '/admin/cms/course/attend/status',
        method: 'post',
        data
    })
}
