import request from '@/utils/request'

/**
 * 获取课程视频列表
 * @param {Object} params 查询参数
 */
export function cmsCourseVideoListApi(params) {
    return request({
        url: '/admin/cms/course/video/list',
        method: 'get',
        params
    })
}

/**
 * 获取课程视频详情
 * @param {Number} id 视频ID
 */
export function cmsCourseVideoInfoApi(id) {
    return request({
        url: `/admin/cms/course/video/info/${id}`,
        method: 'get'
    })
}

/**
 * 新增课程视频
 * @param {Object} data 视频信息
 */
export function cmsCourseVideoAddApi(data) {
    return request({
        url: '/admin/cms/course/video/save',
        method: 'post',
        data
    })
}

/**
 * 更新课程视频
 * @param {Object} data 视频信息
 */
export function cmsCourseVideoUpdateApi(data) {
    return request({
        url: '/admin/cms/course/video/update',
        method: 'post',
        data
    })
}

/**
 * 删除课程视频
 * @param {Array} ids 视频ID数组
 */
export function cmsCourseVideoDeleteApi(ids) {
    return request({
        url: '/admin/cms/course/video/delete',
        method: 'post',
        data: ids
    })
}

/**
 * 更新课程视频状态
 * @param {Object} data 包含id和状态信息
 */
export function cmsCourseVideoUpdateStatusApi(data) {
    return request({
        url: '/admin/cms/course/video/status',
        method: 'post',
        data
    })
} 