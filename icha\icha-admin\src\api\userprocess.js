
import request from '@/utils/request'

/**
 * 新增userprocess
 * @param pram
 */
export function userprocessCreateApi(data) {
    return request({
        url: 'userprocess/save',
        method: 'POST',
        data
    })
}

/**
 * userprocess更新
 * @param pram
 */
export function userprocessUpdateApi(data) {
    return request({
        url: 'userprocess/update',
        method: 'POST',
        data
    })
}

/**
 * userprocess详情
 * @param pram
 */
export function userprocessDetailApi(id) {
    return request({
        url: `userprocess/info/${id}`,
        method: 'GET'
    })
}

/**
 * userprocess删除
 * @param pram
 */
export function userprocessDeleteApi(id) {
    return request({
        url: `userprocess/delete/${id}`,
        method: 'get'
    })
}


/**
 * userprocess列表
 * @param pram
 */
export function userprocessListApi(params) {
    return request({
        url: 'userprocess/list',
        method: 'GET',
        params
    })
}

export function userprocessFindByUserId(params) {
    return request({
        url: 'userprocess/findByUserId',
        method: 'GET',
        params
    })
}

