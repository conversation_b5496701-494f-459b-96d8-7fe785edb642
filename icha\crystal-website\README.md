# city-font-a0

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

# Message 消息提示工具类

这是一个基于Vant UI的消息提示工具类，提供了统一的消息提示接口，方便在Vue组件中使用。

## 使用方法

### 在JS文件中使用

```js
import Message from '@/utils/message'

// 显示普通消息
Message.info('这是一条提示信息')

// 显示成功消息
Message.success('操作成功')

// 显示警告消息
Message.warning('警告信息')

// 显示错误消息
Message.error('错误信息')

// 显示加载中
const loading = Message.loading('加载中...')
// 手动关闭加载提示
setTimeout(() => {
  Message.clear()
}, 2000)

// 显示确认对话框
Message.confirm('标题', '确认要执行此操作吗？', () => {
  // 点击确认按钮的回调
  console.log('用户点击了确认')
}, () => {
  // 点击取消按钮的回调
  console.log('用户点击了取消')
})

// 显示提示对话框
Message.alert('标题', '操作成功！', () => {
  console.log('用户关闭了对话框')
})
```

### 在Vue组件中使用

```vue
<template>
  <div>
    <button @click="showMessage">显示消息</button>
  </div>
</template>

<script>
export default {
  methods: {
    showMessage() {
      // 通过Vue原型方法调用
      this.$message.success('操作成功')
      
      // 显示确认对话框
      this.$message.confirm('提示', '确认删除？', () => {
        // 确认回调
      })
    }
  }
}
</script>
```

## API

### Message.info(message, duration)
- message: 消息内容
- duration: 显示时长，默认3000ms

### Message.success(message, duration)
- message: 消息内容
- duration: 显示时长，默认3000ms

### Message.warning(message, duration)
- message: 消息内容
- duration: 显示时长，默认3000ms

### Message.error(message, duration)
- message: 消息内容
- duration: 显示时长，默认3000ms

### Message.loading(message, forbidClick)
- message: 消息内容，默认为"加载中..."
- forbidClick: 是否禁止背景点击，默认为true

### Message.clear()
关闭所有消息提示

### Message.confirm(title, message, callback, cancelCallback)
- title: 标题
- message: 内容
- callback: 确认回调函数
- cancelCallback: 取消回调函数

### Message.alert(title, message, callback)
- title: 标题
- message: 内容
- callback: 确认回调函数
