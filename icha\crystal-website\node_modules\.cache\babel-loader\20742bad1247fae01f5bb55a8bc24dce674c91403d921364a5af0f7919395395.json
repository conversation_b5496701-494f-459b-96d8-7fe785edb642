{"ast": null, "code": "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone } from \"@/utils/index\";\nimport '../assets/css/common-headers.css'; // 导入头部共用样式\n\nexport default {\n  name: \"CertificateView\",\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      isMobilePhone: isMobilePhone(),\n      searchForm: {\n        name: ''\n      },\n      searchStatus: 'init',\n      // init, success, empty, error\n      certificates: [],\n      // 改为数组存储多个证书\n      submitting: false,\n      certificateType: [{\n        key: 0,\n        value: \"水晶疗愈高级认证\"\n      }]\n    };\n  },\n  mounted() {\n    this.$wxShare();\n  },\n  methods: {\n    searchCertificate() {\n      // 验证表单\n      if (!this.searchForm.name) {\n        this.$message.warning('请至少输入证书编号或持证人姓名');\n        return;\n      }\n      this.submitting = true;\n\n      // 实际项目中使用真实API\n      this.getRequest(\"/cms/certificate/query\", this.searchForm).then(resp => {\n        this.submitting = false;\n        if (resp && resp.code == 200) {\n          if (resp.data && resp.data.length > 0) {\n            // 如果返回数组，直接使用\n            this.certificates = resp.data;\n            this.searchStatus = 'success';\n          } else {\n            this.certificates = [];\n            this.searchStatus = 'empty';\n          }\n        } else {\n          this.certificates = [];\n          this.searchStatus = 'error';\n        }\n      });\n    },\n    resetForm() {\n      this.searchForm = {\n        name: ''\n      };\n      this.searchStatus = 'init';\n      this.certificates = [];\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "name", "components", "data", "searchForm", "searchStatus", "certificates", "submitting", "certificateType", "key", "value", "mounted", "$wxShare", "methods", "searchCertificate", "$message", "warning", "getRequest", "then", "resp", "code", "length", "resetForm"], "sources": ["src/views/certificate.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t<div class=\"layout-container\" style=\"width: 100%\">\r\n\t\t\t<!-- 美化后的页面头部 -->\r\n\t\t\t<div class=\"hero-header-section certificate-header\">\r\n\t\t\t\t<div class=\"hero-content\">\r\n\t\t\t\t\t<h1 class=\"hero-title\"><i class=\"fa fa-certificate fa-spin-pulse\"></i> 证书查询</h1>\r\n\t\t\t\t\t<p class=\"hero-subtitle\">专业认证体系，权威资质背书</p>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<div class=\"section\">\r\n\t\t\t\t<div class=\"certificate-container\">\r\n\t\t\t\t\t<div class=\"section--header text-center\">\r\n\t\t\t\t\t\t<!-- <h2 class=\"section--title-nocolor\" :style=\"isMobilePhone ? 'font-size: 20px' : ''\">证书验证查询系统</h2> -->\r\n\t\t\t\t\t\t<p class=\"section--description\">\r\n\t\t\t\t\t\t\t请输入证书编号或持证人姓名进行查询，验证证书的真实性和有效性。\r\n\t\t\t\t\t\t</p>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div class=\"certificate-main\">\r\n\t\t\t\t\t\t<!-- 左侧查询表单 -->\r\n\t\t\t\t\t\t<div class=\"query-form-container\">\r\n\t\t\t\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t\t\t\t<label>证书编号/持证人姓名</label>\r\n\t\t\t\t\t\t\t\t<input type=\"text\" v-model=\"searchForm.name\" placeholder=\"请输入证书编号/持证人姓名\" class=\"form-control\" />\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<div class=\"form-actions\">\r\n\t\t\t\t\t\t\t\t<button class=\"query-btn\" @click=\"searchCertificate\" :disabled=\"submitting\">\r\n\t\t\t\t\t\t\t\t\t<span v-if=\"!submitting\">查询</span>\r\n\t\t\t\t\t\t\t\t\t<span v-else><i class=\"fa fa-spinner fa-spin\"></i> 查询中</span>\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t<button class=\"reset-btn\" @click=\"resetForm\">重置</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 右侧查询结果 -->\r\n\t\t\t\t\t\t<div class=\"query-result-container\" v-if=\"searchStatus !== 'init'\">\r\n\t\t\t\t\t\t\t<!-- 查询成功 - 修改为多证书循环显示 -->\r\n\t\t\t\t\t\t\t<div v-if=\"searchStatus == 'success' && certificates.length > 0\">\r\n\t\t\t\t\t\t\t\t<div v-for=\"(certificate, index) in certificates\" :key=\"index\" class=\"certificate-info\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"certificate-header\">\r\n\t\t\t\t\t\t\t\t\t\t<img class=\"certificate-icon\" src=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0OCIgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDQ4IDQ4IiBmaWxsPSJub25lIj48cGF0aCBkPSJNMzQgMTBIOEMyLjcgMTAgMi4zIDExIDIuMyAxMS4zVjM2LjdDMi4zIDM3LjMgMyAzOCA1IDM4SDM3QzM5IDM4IDM5LjcgMzcuMyAzOS43IDM2LjdWMTRDMzkuNyAxMiAzOCAxMCAzNCAxMFoiIGZpbGw9IiMyZDZjYTIiPjwvcGF0aD48cGF0aCBkPSJNMTMgMjRDMTMgMjMuNiAxMy4yIDIzLjQgMTMuOCAyMy40SDE4QzE4LjIgMjMuNCAxOC40IDIzLjYgMTguNCAyNEMxOC40IDI0LjQgMTguMiAyNC42IDE4IDI0LjZIMTRDMTMuNCAyNC42IDEzIDI0LjQgMTMgMjRaTTIwIDI2QzIwIDI1LjYgMjAuMiAyNS40IDIwLjggMjUuNEgzMkMzMi4yIDI1LjQgMzIuNCAyNS42IDMyLjQgMjZDMzIuNCAyNi40IDMyLjIgMjYuNiAzMiAyNi42SDIxQzIwLjQgMjYuNiAyMCAyNi40IDIwIDI2Wk0xMyAyOEMxMyAyNy42IDEzLjIgMjcuNCAxMy44IDI3LjRIMThDMTguMiAyNy40IDE4LjQgMjcuNiAxOC40IDI4QzE4LjQgMjguNCAxOC4yIDI4LjYgMTggMjguNkgxNEMxMy40IDI4LjYgMTMgMjguNCAxMyAyOFpNMjAgMzBDMjAgMjkuNiAyMC4yIDI5LjQgMjAuOCAyOS40SDI2QzI2LjIgMjkuNCAyNi40IDI5LjYgMjYuNCAzMEMyNi40IDMwLjQgMjYuMiAzMC42IDI2IDMwLjZIMjFDMjAuNCAzMC42IDIwIDMwLjQgMjAgMzBaTTIwIDIyQzIwIDIxLjYgMjAuMiAyMS40IDIwLjggMjEuNEgzMkMzMi4yIDIxLjQgMzIuNCAyMS42IDMyLjQgMjJDMzIuNCAyMi40IDMyLjIgMjIuNiAzMiAyMi42SDIxQzIwLjQgMjIuNiAyMCAyMi40IDIwIDIyWk0xMyAyMEMxMyAxOS42IDEzLjIgMTkuNCAxMy44IDE5LjRIMThDMTguMiAxOS40IDE4LjQgMTkuNiAxOC40IDIwQzE4LjQgMjAuNCAxOC4yIDIwLjYgMTggMjAuNkgxNEMxMy40IDIwLjYgMTMgMjAuNCAxMyAyMFoiIGZpbGw9IiNmZmYiPjwvcGF0aD48L3N2Zz4=\" alt=\"证书\" />\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"certificate-status\">有效证书</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<!-- 证书图片 -->\r\n\t\t\t\t\t\t\t\t\t<div class=\"certificate-image\">\r\n\t\t\t\t\t\t\t\t\t\t<img :src=\"certificate.image\" alt=\"证书\" />\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"detail-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-label\">证书编号：</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-value\">{{certificate.number}}</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"detail-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-label\">持证人：</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-value\">{{certificate.name}}</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"detail-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-label\">证书类型：</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-value\">{{(certificate.type === null || certificate.type === '' || certificate.type === undefined) ? '' : certificateType[certificate.type].value}}</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"detail-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-label\">发证日期：</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-value\">{{certificate.startTime}}</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<!-- <div class=\"detail-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-label\">有效期至：</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-value\">{{certificate.expireDate}}</div>\r\n\t\t\t\t\t\t\t\t\t</div> -->\r\n\t\t\t\t\t\t\t\t\t<div class=\"detail-item\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-label\">发证机构：</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"detail-value\">{{certificate.issuer}}</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"certificate-count\" v-if=\"certificates.length > 1\">\r\n\t\t\t\t\t\t\t\t\t共找到 {{certificates.length}} 条证书记录\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- 无结果 -->\r\n\t\t\t\t\t\t\t<div class=\"no-data-result\" v-else-if=\"searchStatus == 'empty'\">\r\n\t\t\t\t\t\t\t\t<i class=\"fa fa-exclamation-circle\"></i>\r\n\t\t\t\t\t\t\t\t<p>未找到符合条件的证书，请核对证书编号或持证人姓名后重试。</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- 查询出错 -->\r\n\t\t\t\t\t\t\t<div class=\"error-result\" v-else-if=\"searchStatus == 'error'\">\r\n\t\t\t\t\t\t\t\t<i class=\"fa fa-times-circle\"></i>\r\n\t\t\t\t\t\t\t\t<p>查询过程中发生错误，请稍后重试。</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 当没有查询结果时显示的占位内容 -->\r\n\t\t\t\t\t\t<div class=\"query-result-placeholder\" v-if=\"searchStatus == 'init'\">\r\n\t\t\t\t\t\t\t<div class=\"placeholder-content\">\r\n\t\t\t\t\t\t\t\t<img src=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI5NiIgaGVpZ2h0PSI5NiIgdmlld0JveD0iMCAwIDk2IDk2IiBmaWxsPSJub25lIj48cGF0aCBvcGFjaXR5PSIwLjIiIGQ9Ik00OCA5NkMyMS40OTAzIDk2IDAgNzQuNTA5NyAwIDQ4QzAgMjEuNDkwMyAyMS40OTAzIDAgNDggMEM3NC41MDk3IDAgOTYgMjEuNDkwMyA5NiA0OEM5NiA3NC41MDk3IDc0LjUwOTcgOTYgNDggOTZaIiBmaWxsPSIjNTE2NzkwIj48L3BhdGg+PHBhdGggZD0iTTM0LjUgNDhDMzQuNSA0MC4yMiA0MC43MiAzNCA0OC41IDM0QzU2LjI4IDM0IDYyLjUgNDAuMjIgNjIuNSA0OEM2Mi41IDU1Ljc4IDU2LjI4IDYyIDQ4LjUgNjJDNDAuNzIgNjIgMzQuNSA1NS43OCAzNC41IDQ4Wk01MiA0M0M1MiA0MC4yNCA0OS43NiAzOCA0NyAzOEM0NC4yNCAzOCA0MiA0MC4yNCA0MiA0M0M0MiA0NS43NiA0NC4yNCA0OCA0NyA0OEg1MlY0M1oiIGZpbGw9IiM1MTY3OTAiPjwvcGF0aD48cGF0aCBkPSJNNDIgNzBDNDIgNjQuNDggNDYuNDggNjAgNTIgNjBINzBWNzBINTJDNDYuNDggNzAgNDIgNjUuNTIgNDIgNjBWNDVDNDIgMzkuNDggNDYuNDggMzUgNTIgMzVINjBWNDVINTJDNDYuNDggNDUgNDIgNDAuNTIgNDIgMzVWMzBIMzJ2NTBDNDI2IDgwIDQyIDc1LjUyIDQyIDcwWiIgZmlsbD0iIzUxNjc5MCI+PC9wYXRoPjwvc3ZnPg==\" alt=\"查询提示\" class=\"placeholder-icon\">\r\n\t\t\t\t\t\t\t\t<p>输入证书编号或持证人姓名后点击查询</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport '../assets/css/common-headers.css'; // 导入头部共用样式\r\n\r\nexport default {\r\n\tname: \"CertificateView\",\r\n\tcomponents: { Layout },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tsearchForm: {\r\n\t\t\t\tname: ''\r\n\t\t\t},\r\n\t\t\tsearchStatus: 'init', // init, success, empty, error\r\n\t\t\tcertificates: [], // 改为数组存储多个证书\r\n\t\t\tsubmitting: false,\r\n\t\t\tcertificateType: [\r\n\t\t\t\t{\r\n\t\t\t\t\tkey: 0,\r\n\t\t\t\t\tvalue: \"水晶疗愈高级认证\"\r\n\t\t\t\t},\r\n]\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$wxShare();\r\n\t},\r\n\tmethods: {\r\n\t\tsearchCertificate() {\r\n\t\t\t// 验证表单\r\n\t\t\tif (!this.searchForm.name) {\r\n\t\t\t\tthis.$message.warning('请至少输入证书编号或持证人姓名');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.submitting = true;\r\n\t\t\t\r\n\t\t\t// 实际项目中使用真实API\r\n\t\t\tthis.getRequest(\"/cms/certificate/query\", this.searchForm)\r\n\t\t\t  .then(resp => {\r\n\t\t\t    this.submitting = false;\r\n\t\t\t    if (resp && resp.code == 200) {\r\n\t\t\t      if (resp.data && resp.data.length > 0) {\r\n\t\t\t          // 如果返回数组，直接使用\r\n\t\t\t          this.certificates = resp.data;\r\n\t\t\t        this.searchStatus = 'success';\r\n\t\t\t      } else {\r\n\t\t\t        this.certificates = [];\r\n\t\t\t        this.searchStatus = 'empty';\r\n\t\t\t      }\r\n\t\t\t    } else {\r\n\t\t\t      this.certificates = [];\r\n\t\t\t      this.searchStatus = 'error';\r\n\t\t\t    }\r\n\t\t\t  });\r\n\t\t\t\r\n\t\t},\r\n\t\tresetForm() {\r\n\t\t\tthis.searchForm = {\r\n\t\t\t\tname: ''\r\n\t\t\t};\r\n\t\t\tthis.searchStatus = 'init';\r\n\t\t\tthis.certificates = [];\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 美化后的页面头部样式 */\r\n.certificate-header {\r\n\tbackground-image: url('https://img.freepik.com/free-photo/watercolor-pastel-texture-background_53876-98173.jpg') !important;\r\n}\r\n\r\n.text-center {\r\n\ttext-align: center;\r\n}\r\n\r\n.section--header {\r\n\tmargin-bottom: 40px;\r\n}\r\n\r\n.section--title-nocolor {\r\n\tfont-size: 28px;\r\n\tmargin-bottom: 15px;\r\n\tcolor: #3a2c58;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.section--description {\r\n\tmax-width: 800px;\r\n\tmargin: 0 auto;\r\n\tcolor: #666;\r\n\tline-height: 1.6;\r\n\tfont-size: 16px;\r\n}\r\n\r\n.certificate-container {\r\n\tmax-width: 1000px;\r\n\tmargin: 0 auto;\r\n\tpadding: 0 20px;\r\n}\r\n\r\n.certificate-main {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 30px;\r\n\tmargin-bottom: 50px;\r\n\tjustify-content: center;\r\n}\r\n\r\n.certificate-image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.certificate-image img {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.query-form-container {\r\n\tflex: 1;\r\n\tmin-width: 300px;\r\n\tmax-width: 450px;\r\n\tbackground: #fff;\r\n\tborder-radius: 8px;\r\n\tpadding: 35px;\r\n\tbox-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\r\n\ttransition: all 0.3s ease;\r\n\tborder: 1px solid #f0f0f0;\r\n}\r\n\r\n.query-result-container,\r\n.query-result-placeholder {\r\n\tflex: 1;\r\n\tmin-width: 300px;\r\n\tmax-width: 450px;\r\n\tbackground: #fff;\r\n\tborder-radius: 8px;\r\n\tpadding: 35px;\r\n\tbox-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\r\n\ttransition: all 0.3s ease;\r\n\tborder: 1px solid #f0f0f0;\r\n}\r\n\r\n.query-form-container:hover,\r\n.query-result-container:hover {\r\n\tbox-shadow: 0 15px 40px rgba(81, 103, 144, 0.1);\r\n}\r\n\r\n/* 表单元素样式 */\r\n.form-group {\r\n\tmargin-bottom: 25px;\r\n}\r\n\r\n.form-group label {\r\n\tdisplay: block;\r\n\tmargin-bottom: 10px;\r\n\tfont-size: 15px;\r\n\tcolor: #444;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.form-control {\r\n\twidth: 100%;\r\n\theight: 45px;\r\n\tpadding: 0 15px;\r\n\tborder: 1px solid #e0e0e0;\r\n\tborder-radius: 6px;\r\n\tfont-size: 15px;\r\n\tbox-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.form-control:focus {\r\n\tborder-color: #516790;\r\n\tbox-shadow: 0 0 0 3px rgba(81, 103, 144, 0.15);\r\n\toutline: none;\r\n}\r\n\r\n.form-actions {\r\n\tdisplay: flex;\r\n\tgap: 15px;\r\n\tmargin-top: 30px;\r\n}\r\n\r\n.query-btn, .reset-btn {\r\n\tpadding: 10px 25px;\r\n\tborder-radius: 6px;\r\n\tfont-size: 15px;\r\n\tcursor: pointer;\r\n\tborder: none;\r\n\tfont-weight: 500;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.query-btn {\r\n\tbackground: linear-gradient(135deg, #516790, #3a4f6e);\r\n\tcolor: #fff;\r\n\tflex: 1.5;\r\n}\r\n\r\n.query-btn:hover:not(:disabled) {\r\n\tbackground: linear-gradient(135deg, #3a4f6e, #2a3a50);\r\n\ttransform: translateY(-2px);\r\n\tbox-shadow: 0 4px 10px rgba(58, 79, 110, 0.3);\r\n}\r\n\r\n.reset-btn {\r\n\tbackground-color: #f5f5f5;\r\n\tcolor: #555;\r\n\tborder: 1px solid #e0e0e0;\r\n\tflex: 1;\r\n}\r\n\r\n.reset-btn:hover {\r\n\tbackground-color: #efefef;\r\n\tcolor: #333;\r\n}\r\n\r\n.query-btn:disabled {\r\n\tbackground: #a0aec0;\r\n\tcursor: not-allowed;\r\n\ttransform: none;\r\n\tbox-shadow: none;\r\n}\r\n\r\n/* 证书详情样式 */\r\n.certificate-info {\r\n\tpadding: 5px 0;\r\n\tmargin-bottom: 25px;\r\n\tborder-bottom: 1px dashed #e0e0e0;\r\n}\r\n\r\n.certificate-info:last-child {\r\n\tmargin-bottom: 10px;\r\n\tborder-bottom: none;\r\n}\r\n\r\n.certificate-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 25px;\r\n\tpadding-bottom: 15px;\r\n\tborder-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.certificate-icon {\r\n\twidth: 48px;\r\n\theight: 48px;\r\n\tmargin-right: 15px;\r\n}\r\n\r\n.certificate-status {\r\n\tbackground-color: #e6f7e6;\r\n\tcolor: #2c8a3e;\r\n\tpadding: 6px 15px;\r\n\tborder-radius: 20px;\r\n\tfont-size: 14px;\r\n\tfont-weight: 500;\r\n\tbox-shadow: 0 2px 5px rgba(44, 138, 62, 0.15);\r\n}\r\n\r\n.detail-item {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 18px;\r\n\tborder-bottom: 1px solid #f5f5f5;\r\n\tpadding-bottom: 18px;\r\n}\r\n\r\n.detail-item:last-child {\r\n\tborder-bottom: none;\r\n\tmargin-bottom: 0;\r\n\tpadding-bottom: 0;\r\n}\r\n\r\n.detail-label {\r\n\twidth: 100px;\r\n\tcolor: #666;\r\n\tfont-size: 15px;\r\n}\r\n\r\n.detail-value {\r\n\tflex: 1;\r\n\tcolor: #333;\r\n\tfont-size: 15px;\r\n\tfont-weight: 500;\r\n}\r\n\r\n/* 证书计数样式 */\r\n.certificate-count {\r\n\ttext-align: center;\r\n\tmargin-top: 20px;\r\n\tpadding: 10px;\r\n\tbackground-color: #f8f9fa;\r\n\tborder-radius: 6px;\r\n\tcolor: #516790;\r\n\tfont-weight: 500;\r\n}\r\n\r\n/* 无数据和错误提示 */\r\n.no-data-result,\r\n.error-result {\r\n\ttext-align: center;\r\n\tpadding: 40px 0;\r\n}\r\n\r\n.no-data-result i,\r\n.error-result i {\r\n\tfont-size: 48px;\r\n\tmargin-bottom: 15px;\r\n}\r\n\r\n.no-data-result i {\r\n\tcolor: #f59e0b;\r\n}\r\n\r\n.error-result i {\r\n\tcolor: #e53e3e;\r\n}\r\n\r\n.no-data-result p,\r\n.error-result p {\r\n\tcolor: #666;\r\n\tfont-size: 15px;\r\n\tline-height: 1.5;\r\n}\r\n\r\n/* 占位符样式 */\r\n.placeholder-content {\r\n\ttext-align: center;\r\n\tpadding: 60px 20px;\r\n}\r\n\r\n.placeholder-icon {\r\n\twidth: 96px;\r\n\theight: 96px;\r\n\tmargin-bottom: 20px;\r\n\topacity: 0.8;\r\n}\r\n\r\n.placeholder-content p {\r\n\tcolor: #888;\r\n\tfont-size: 15px;\r\n}\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 990px) {\r\n\t.certificate-main {\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.query-form-container,\r\n\t.query-result-container,\r\n\t.query-result-placeholder {\r\n\t\twidth: 100%;\r\n\t\tmax-width: 100%;\r\n\t}\r\n}\r\n\r\n@media (max-width: 768px) {\r\n\t.certificate-container {\r\n\t\tpadding: 0 15px;\r\n\t}\r\n\t\r\n\t.query-form-container,\r\n\t.query-result-container,\r\n\t.query-result-placeholder {\r\n\t\tpadding: 25px;\r\n\t}\r\n\t\r\n\t.form-actions {\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n\t.query-btn, .reset-btn {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 10px;\r\n\t}\r\n\t\r\n\t.detail-item {\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n\t.detail-label {\r\n\t\twidth: 100%;\r\n\t\tmargin-bottom: 5px;\r\n\t}\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4GA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAH;EAAA;EACAI,KAAA;IACA;MACAH,aAAA,EAAAA,aAAA;MACAI,UAAA;QACAH,IAAA;MACA;MACAI,YAAA;MAAA;MACAC,YAAA;MAAA;MACAC,UAAA;MACAC,eAAA,GACA;QACAC,GAAA;QACAC,KAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAC,kBAAA;MACA;MACA,UAAAV,UAAA,CAAAH,IAAA;QACA,KAAAc,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,KAAAT,UAAA;;MAEA;MACA,KAAAU,UAAA,gCAAAb,UAAA,EACAc,IAAA,CAAAC,IAAA;QACA,KAAAZ,UAAA;QACA,IAAAY,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,IAAAD,IAAA,CAAAhB,IAAA,IAAAgB,IAAA,CAAAhB,IAAA,CAAAkB,MAAA;YACA;YACA,KAAAf,YAAA,GAAAa,IAAA,CAAAhB,IAAA;YACA,KAAAE,YAAA;UACA;YACA,KAAAC,YAAA;YACA,KAAAD,YAAA;UACA;QACA;UACA,KAAAC,YAAA;UACA,KAAAD,YAAA;QACA;MACA;IAEA;IACAiB,UAAA;MACA,KAAAlB,UAAA;QACAH,IAAA;MACA;MACA,KAAAI,YAAA;MACA,KAAAC,YAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}