{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone } from \"@/utils/index\";\nimport '../assets/css/common-headers.css';\nexport default {\n  name: \"DownloadView\",\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      isMobilePhone: isMobilePhone(),\n      downloadList: [],\n      searchKeyword: '',\n      pageIndex: 1,\n      pageSize: 10,\n      total: 0,\n      totalPage: 1\n    };\n  },\n  mounted() {\n    this.$wxShare();\n    this.getDownloadList();\n  },\n  methods: {\n    getDownloadList() {\n      // 模拟数据，实际项目中应替换为真实接口\n      this.getRequest(\"/cms/download/list\", {\n        'page': this.pageIndex,\n        'limit': this.pageSize,\n        'title': this.searchKeyword\n      }).then(resp => {\n        if (resp && resp.code == 200) {\n          this.downloadList = resp.data.list || [];\n          this.total = resp.data.total || 0;\n          this.totalPage = resp.data.totalPage || 1;\n        } else {\n          this.downloadList = [];\n          this.total = 0;\n          this.totalPage = 1;\n        }\n      });\n    },\n    searchCourses() {\n      this.pageIndex = 1; // 搜索时重置为第一页\n      this.getDownloadList();\n    },\n    changeIndex(p) {\n      if (p < 1) {\n        this.pageIndex = 1;\n      } else if (p > this.totalPage) {\n        this.pageIndex = this.totalPage;\n      } else {\n        this.pageIndex = p;\n        this.getDownloadList();\n      }\n    },\n    getFileIcon(fileType) {\n      const iconMap = {\n        'pdf': 'am-icon-file-pdf-o',\n        'doc': 'am-icon-file-word-o',\n        'docx': 'am-icon-file-word-o',\n        'xls': 'am-icon-file-excel-o',\n        'xlsx': 'am-icon-file-excel-o',\n        'ppt': 'am-icon-file-powerpoint-o',\n        'pptx': 'am-icon-file-powerpoint-o',\n        'zip': 'am-icon-file-archive-o',\n        'rar': 'am-icon-file-archive-o',\n        'jpg': 'am-icon-file-image-o',\n        'jpeg': 'am-icon-file-image-o',\n        'png': 'am-icon-file-image-o',\n        'gif': 'am-icon-file-image-o',\n        'mp3': 'am-icon-file-audio-o',\n        'mp4': 'am-icon-file-video-o',\n        'txt': 'am-icon-file-text-o'\n      };\n      return iconMap[fileType.toLowerCase()] || 'am-icon-file-o';\n    },\n    downloadFile(item) {\n      // 判断是否登录\n      const userInfoStr = localStorage.getItem(\"userInfo\") || '{}';\n      const userInfo = JSON.parse(userInfoStr);\n      if (!userInfo.uid) {\n        // 弹窗提示\n        this.$message.error('请先登录');\n        // 跳转登录\n        this.$router.push('/login');\n        return;\n      }\n      this.getRequest(\"/cmsAttend/download/count\", {\n        'id': item.id,\n        'uid': userInfo.uid\n      }).then(resp => {\n        if (resp && resp.code == 200) {\n          this.$message.success(`正在下载: ${item.title}`);\n          window.open(item.fileUrl, '_blank');\n          this.getDownloadList();\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "name", "components", "data", "downloadList", "searchKeyword", "pageIndex", "pageSize", "total", "totalPage", "mounted", "$wxShare", "getDownloadList", "methods", "getRequest", "then", "resp", "code", "list", "searchCourses", "changeIndex", "p", "getFileIcon", "fileType", "iconMap", "toLowerCase", "downloadFile", "item", "userInfoStr", "localStorage", "getItem", "userInfo", "JSON", "parse", "uid", "$message", "error", "$router", "push", "id", "success", "title", "window", "open", "fileUrl"], "sources": ["src/views/download.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t<div class=\"layout-container\" style=\"width: 100%\">\r\n\t\t\t<!-- 美化后的页面头部 -->\r\n\t\t\t<div class=\"hero-header-section download-header\">\r\n\t\t\t\t<div class=\"hero-content\">\r\n\t\t\t\t\t<h1 class=\"hero-title\"><i class=\"am-icon-download fa-spin-pulse\"></i> 资料下载</h1>\r\n\t\t\t\t\t<p class=\"hero-subtitle\">探索水晶的神奇力量，获取专业学习资源</p>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<div class=\"section\">\r\n\t\t\t\t<div class=\"am-container\">\r\n\t\t\t\t\t<div class=\"section--header\">\r\n\t\t\t\t\t\t<h2 class=\"section--title\" :style=\"isMobilePhone ? 'font-size: 20px' : ''\">学习资料下载</h2>\r\n\t\t\t\t\t\t<p class=\"section--description\">\r\n\t\t\t\t\t\t\t这里提供水晶疗愈相关的学习资料，包括教程、手册、图表等，帮助您更好地学习和应用水晶疗愈知识。\r\n\t\t\t\t\t\t</p>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<div class=\"download-container\">\r\n\t\t\t\t\t\t<div class=\"download-list\">\r\n\t\t\t\t\t\t\t<div v-for=\"(item, index) in downloadList\" :key=\"index\" class=\"download-item\">\r\n\t\t\t\t\t\t\t\t<div class=\"download-info\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"download-icon\">\r\n\t\t\t\t\t\t\t\t\t\t<i :class=\"getFileIcon(item.fileType)\"></i>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"download-content\">\r\n\t\t\t\t\t\t\t\t\t\t<h3 class=\"download-title\">{{ item.title }}</h3>\r\n\t\t\t\t\t\t\t\t\t\t<p class=\"download-desc\">{{ item.description }}</p>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"download-meta\">\r\n\t\t\t\t\t\t\t\t\t\t\t<span class=\"meta-item\"><i class=\"am-icon-calendar\"></i> {{ item.uploadDate }}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<span class=\"meta-item\"><i class=\"am-icon-file-o\"></i> {{ item.fileSize }}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<span class=\"meta-item\"><i class=\"am-icon-download\"></i> {{ item.downloads }} 次下载</span>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"download-action\">\r\n\t\t\t\t\t\t\t\t\t<button class=\"download-btn\" @click=\"downloadFile(item)\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"am-icon-download\"></i> 下载\r\n\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- 无数据显示 -->\r\n\t\t\t\t\t\t\t<div v-if=\"downloadList.length == 0\" class=\"no-data\">\r\n\t\t\t\t\t\t\t\t<i class=\"am-icon-exclamation-circle\"></i>\r\n\t\t\t\t\t\t\t\t<p>暂无可下载资料</p>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t<!-- 分页 -->\r\n\t\t\t\t\t\t\t<ul class=\"am-pagination\" style=\"text-align: center;\" v-if=\"total > 0\">\r\n\t\t\t\t\t\t\t\t<li :class=\"pageIndex == 1 ? 'am-disabled':''\" @click=\"changeIndex(pageIndex - 1)\">\r\n\t\t\t\t\t\t\t\t\t<a href=\"javascript:void(0);\">&laquo;</a>\r\n\t\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<li v-for=\"p in totalPage\" :key=\"p\" @click=\"changeIndex(p)\" :class=\"pageIndex == p ? 'am-active':''\">\r\n\t\t\t\t\t\t\t\t\t<a href=\"javascript:void(0);\">{{p}}</a>\r\n\t\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<li :class=\"pageIndex == totalPage ? 'am-disabled':''\" @click=\"changeIndex(pageIndex + 1)\">\r\n\t\t\t\t\t\t\t\t\t<a href=\"javascript:void(0);\">&raquo;</a>\r\n\t\t\t\t\t\t\t\t</li>\r\n\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport '../assets/css/common-headers.css';\r\n\r\nexport default {\r\n\tname: \"DownloadView\",\r\n\tcomponents: { Layout },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tdownloadList: [],\r\n\t\t\tsearchKeyword: '',\r\n\t\t\tpageIndex: 1,\r\n\t\t\tpageSize: 10,\r\n\t\t\ttotal: 0,\r\n\t\t\ttotalPage: 1\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$wxShare();\r\n\t\tthis.getDownloadList();\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\tgetDownloadList() {\r\n\t\t\t// 模拟数据，实际项目中应替换为真实接口\r\n\t\t\tthis.getRequest(\"/cms/download/list\", {\r\n\t\t\t\t'page': this.pageIndex,\r\n\t\t\t\t'limit': this.pageSize,\r\n\t\t\t\t'title': this.searchKeyword\r\n\t\t\t}).then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.downloadList = resp.data.list || [];\r\n\t\t\t\t\tthis.total = resp.data.total || 0;\r\n\t\t\t\t\tthis.totalPage = resp.data.totalPage || 1;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.downloadList = [];\r\n\t\t\t\t\tthis.total = 0;\r\n\t\t\t\t\tthis.totalPage = 1;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tsearchCourses() {\r\n\t\t\tthis.pageIndex = 1; // 搜索时重置为第一页\r\n\t\t\tthis.getDownloadList();\r\n\t\t},\r\n\t\tchangeIndex(p) {\r\n\t\t\tif (p < 1) {\r\n\t\t\t\tthis.pageIndex = 1;\r\n\t\t\t} else if (p > this.totalPage) {\r\n\t\t\t\tthis.pageIndex = this.totalPage;\r\n\t\t\t} else {\r\n\t\t\t\tthis.pageIndex = p;\r\n\t\t\t\tthis.getDownloadList();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tgetFileIcon(fileType) {\r\n\t\t\tconst iconMap = {\r\n\t\t\t\t'pdf': 'am-icon-file-pdf-o',\r\n\t\t\t\t'doc': 'am-icon-file-word-o',\r\n\t\t\t\t'docx': 'am-icon-file-word-o',\r\n\t\t\t\t'xls': 'am-icon-file-excel-o',\r\n\t\t\t\t'xlsx': 'am-icon-file-excel-o',\r\n\t\t\t\t'ppt': 'am-icon-file-powerpoint-o',\r\n\t\t\t\t'pptx': 'am-icon-file-powerpoint-o',\r\n\t\t\t\t'zip': 'am-icon-file-archive-o',\r\n\t\t\t\t'rar': 'am-icon-file-archive-o',\r\n\t\t\t\t'jpg': 'am-icon-file-image-o',\r\n\t\t\t\t'jpeg': 'am-icon-file-image-o',\r\n\t\t\t\t'png': 'am-icon-file-image-o',\r\n\t\t\t\t'gif': 'am-icon-file-image-o',\r\n\t\t\t\t'mp3': 'am-icon-file-audio-o',\r\n\t\t\t\t'mp4': 'am-icon-file-video-o',\r\n\t\t\t\t'txt': 'am-icon-file-text-o'\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\treturn iconMap[fileType.toLowerCase()] || 'am-icon-file-o';\r\n\t\t},\r\n\t\t\r\n\t\tdownloadFile(item) {\r\n\t\t\t// 判断是否登录\r\n\t\t\tconst userInfoStr = localStorage.getItem(\"userInfo\") || '{}';\r\n\t\t\tconst userInfo = JSON.parse(userInfoStr);\r\n\t\t\tif (!userInfo.uid) {\r\n\t\t\t\t// 弹窗提示\r\n\t\t\t\tthis.$message.error('请先登录');\r\n\t\t\t\t// 跳转登录\r\n\t\t\t\tthis.$router.push('/login');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.getRequest(\"/cmsAttend/download/count\", {\r\n\t\t\t\t'id': item.id,\r\n\t\t\t\t'uid': userInfo.uid\r\n\t\t\t}).then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.$message.success(`正在下载: ${item.title}`);\r\n\t\t\t\t\twindow.open(item.fileUrl, '_blank');\r\n\t\t\t\t\tthis.getDownloadList();\r\n\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 美化后的页面头部样式 */\r\n.download-header {\r\n\tbackground-image: url('https://img.freepik.com/free-photo/brown-rocks-sea-water-close-up_23-2148220625.jpg') !important;\r\n}\r\n\r\n.section {\r\n\tpadding: 50px 0;\r\n}\r\n\r\n.section--header {\r\n\tmargin-bottom: 30px;\r\n\ttext-align: center;\r\n}\r\n\r\n.section--title {\r\n\tfont-size: 28px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 15px;\r\n}\r\n\r\n.section--description {\r\n\tfont-size: 16px;\r\n\tcolor: #666;\r\n\tmax-width: 800px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n.download-container {\r\n\tmax-width: 1000px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n.download-list {\r\n\tmargin-top: 30px;\r\n}\r\n\r\n.download-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n\tpadding: 20px;\r\n\tmargin-bottom: 20px;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.download-item:hover {\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);\r\n\ttransform: translateY(-2px);\r\n}\r\n\r\n.download-info {\r\n\tdisplay: flex;\r\n\tflex: 1;\r\n}\r\n\r\n.download-icon {\r\n\tmargin-right: 20px;\r\n\tfont-size: 36px;\r\n\tcolor: #2d6ca2;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\twidth: 60px;\r\n}\r\n\r\n.download-content {\r\n\tflex: 1;\r\n}\r\n\r\n.download-title {\r\n\tfont-size: 18px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 5px;\r\n}\r\n\r\n.download-desc {\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n\tmargin-bottom: 10px;\r\n\tline-height: 1.5;\r\n}\r\n\r\n.download-meta {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tfont-size: 12px;\r\n\tcolor: #999;\r\n}\r\n\r\n.meta-item {\r\n\tmargin-right: 15px;\r\n}\r\n\r\n.meta-item i {\r\n\tmargin-right: 5px;\r\n}\r\n\r\n.download-action {\r\n\tmargin-left: 20px;\r\n}\r\n\r\n.download-btn {\r\n\tbackground-color: #2d6ca2;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tborder-radius: 4px;\r\n\tpadding: 8px 15px;\r\n\tfont-size: 14px;\r\n\tcursor: pointer;\r\n\ttransition: background-color 0.3s ease;\r\n}\r\n\r\n.download-btn:hover {\r\n\tbackground-color: #245a8b;\r\n}\r\n\r\n.download-btn i {\r\n\tmargin-right: 5px;\r\n}\r\n\r\n.no-data {\r\n\ttext-align: center;\r\n\tpadding: 50px 0;\r\n\tcolor: #999;\r\n}\r\n\r\n.no-data i {\r\n\tfont-size: 48px;\r\n\tmargin-bottom: 15px;\r\n\tdisplay: block;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n\t.download-item {\r\n\t\tflex-direction: column;\r\n\t\talign-items: flex-start;\r\n\t}\r\n\t\r\n\t.download-info {\r\n\t\tmargin-bottom: 15px;\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.download-action {\r\n\t\tmargin-left: 0;\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.download-btn {\r\n\t\twidth: 100%;\r\n\t\tpadding: 10px;\r\n\t}\r\n\t\r\n\t.download-icon {\r\n\t\twidth: 40px;\r\n\t\tfont-size: 24px;\r\n\t\tmargin-right: 15px;\r\n\t}\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAH;EAAA;EACAI,KAAA;IACA;MACAH,aAAA,EAAAA,aAAA;MACAI,YAAA;MACAC,aAAA;MACAC,SAAA;MACAC,QAAA;MACAC,KAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IAEAD,gBAAA;MACA;MACA,KAAAE,UAAA;QACA,aAAAR,SAAA;QACA,cAAAC,QAAA;QACA,cAAAF;MACA,GAAAU,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAb,YAAA,GAAAY,IAAA,CAAAb,IAAA,CAAAe,IAAA;UACA,KAAAV,KAAA,GAAAQ,IAAA,CAAAb,IAAA,CAAAK,KAAA;UACA,KAAAC,SAAA,GAAAO,IAAA,CAAAb,IAAA,CAAAM,SAAA;QACA;UACA,KAAAL,YAAA;UACA,KAAAI,KAAA;UACA,KAAAC,SAAA;QACA;MACA;IACA;IACAU,cAAA;MACA,KAAAb,SAAA;MACA,KAAAM,eAAA;IACA;IACAQ,YAAAC,CAAA;MACA,IAAAA,CAAA;QACA,KAAAf,SAAA;MACA,WAAAe,CAAA,QAAAZ,SAAA;QACA,KAAAH,SAAA,QAAAG,SAAA;MACA;QACA,KAAAH,SAAA,GAAAe,CAAA;QACA,KAAAT,eAAA;MACA;IACA;IAEAU,YAAAC,QAAA;MACA,MAAAC,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,OAAAA,OAAA,CAAAD,QAAA,CAAAE,WAAA;IACA;IAEAC,aAAAC,IAAA;MACA;MACA,MAAAC,WAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,MAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,WAAA;MACA,KAAAG,QAAA,CAAAG,GAAA;QACA;QACA,KAAAC,QAAA,CAAAC,KAAA;QACA;QACA,KAAAC,OAAA,CAAAC,IAAA;QACA;MACA;MACA,KAAAxB,UAAA;QACA,MAAAa,IAAA,CAAAY,EAAA;QACA,OAAAR,QAAA,CAAAG;MACA,GAAAnB,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAkB,QAAA,CAAAK,OAAA,UAAAb,IAAA,CAAAc,KAAA;UACAC,MAAA,CAAAC,IAAA,CAAAhB,IAAA,CAAAiB,OAAA;UACA,KAAAhC,eAAA;QAEA;MACA;IAEA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}