<template>
  <div class="workshop-container">
    <el-card class="search-container">
      <el-form :inline="true" :model="dataForm">
        <el-form-item>
          <el-input v-model="dataForm.title" placeholder="名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getDataList()">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery()">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container">
      <div class="table-header">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="addOrUpdateHandle()">新增</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple"
          @click="batchDelete()">删除</el-button>
      </div>

      <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
        style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50">
        </el-table-column>
        <el-table-column prop="title" header-align="center" align="center" label="名称">
        </el-table-column>
        <el-table-column prop="brief" header-align="center" align="center" label="简介">
        </el-table-column>
        <el-table-column prop="avatar" header-align="center" align="center" label="图片">
          <template slot-scope="scope">
            <img v-if="scope.row.avatar" :src="scope.row.avatar" alt="图片" style="max-width: 50px; max-height: 50px;">
          </template>
        </el-table-column>
        <el-table-column prop="tags" header-align="center" align="center" label="标签">
        </el-table-column>
        <el-table-column prop="location" header-align="center" align="center" label="地址">
        </el-table-column>
        <el-table-column prop="isShow" header-align="center" align="center" label="是否展示">
          <template slot-scope="scope">
            <el-tag type="primary" :class="'tag-color tag-color-' + (scope.row.isShow)">{{
              yesOrNo[scope.row.isShow].value }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isIndex" header-align="center" align="center" label="是否首页">
          <template slot-scope="scope">
            <el-tag type="success" :class="'tag-color tag-color-' + (scope.row.isIndex)">{{
              yesOrNo[scope.row.isIndex].value }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paixu" header-align="center" align="center" label="排序">
        </el-table-column>
        <el-table-column prop="number" header-align="center" align="center" label="人数限制">
        </el-table-column>
        <el-table-column prop="readyNumber" header-align="center" align="center" label="已报名人数">
        </el-table-column>
        <el-table-column prop="addTime" show-overflow-tooltip="" header-align="center" align="center" label="添加时间">
        </el-table-column>
        <el-table-column fixed="right" header-align="center" align="center" width="200" label="操作">
          <template slot-scope="scope">
            <el-button type="text" style="color: #409EFF;font-weight: bold;" size="small" @click="attendList(scope.row.id)">报名情况</el-button>
            <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
            <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="block">
        <el-pagination :page-sizes="[20, 40, 60, 80]" :page-size="dataForm.limit" :current-page="dataForm.page"
          layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
          @current-change="pageChange" />
      </div>
    </el-card>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import { cmsWorkshopCreateApi, cmsWorkshopUpdateApi, cmsWorkshopDetailApi, cmsWorkshopDeleteApi, cmsWorkshopListApi } from '@/api/cmsWorkshop'
import { yesOrNo } from '@/data/common'
import AddOrUpdate from './workshop-add-or-update'

export default {
  data() {
    return {
      yesOrNo,
      dataForm: {
        title: '',
        page: 1,
        limit: 20,
      },
      total: 0,
      dataList: [],
      dataListLoading: false,
      dataListSelections: [],
      addOrUpdateVisible: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true
    }
  },
  components: {
    AddOrUpdate
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    attendList(id) {
      this.$router.push({
        path: '/cms/workshopAttend',
        query: { id }
      })
    },
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      cmsWorkshopListApi(this.dataForm).then(res => {
        this.dataList = res.list || [];
        this.total = parseInt(res.total);
        this.dataListLoading = false
      }).catch(() => {
        this.dataList = []
        this.total = 0
        this.dataListLoading = false
      })
    },

    resetQuery() {
      this.dataForm.title = ''
      this.getDataList()
    },

    pageChange(page) {
      this.dataForm.page = page
      this.getDataList()
    },
    handleSizeChange(val) {
      this.dataForm.limit = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
      this.ids = val.map(item => item.id)
      this.single = val.length !== 1
      this.multiple = !val.length
    },
    // 批量删除
    batchDelete() {
      if (this.dataListSelections.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      this.deleteHandle()
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      const ids = id || this.ids
      if (!ids) {
        return
      }
      this.$confirm(`确定删除选中的数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cmsWorkshopDeleteApi([].concat(ids)).then(() => {
          this.$message.success("删除成功");
          this.getDataList();
        }).catch((res) => {
          this.$message.error(res.message)
        });
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.workshop-container {
  padding: 20px;

  .search-container {
    margin-bottom: 20px;
  }

  .table-container {
    .table-header {
      margin-bottom: 20px;
    }
  }
}
</style>