<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.crystal.service.dao.UserProcessDao">

	<!-- 根据包名 模块名 以及类名 生成Mapper XML 配置文件 -->
    <resultMap type="com.crystal.common.model.user.UserProcess" id="userProcessMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="addTime" column="add_time"/>
        <result property="name" column="name"/>
        <result property="isDepressed" column="is_depressed"/>
        <result property="remarks" column="remarks"/>
    </resultMap>


</mapper>
