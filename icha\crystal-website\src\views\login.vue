<template>
	<Layout>
	<div class="login-page">
		<div class="login-container">
			<div class="login-left">
				<div class="login-logo">
					<img src="@/assets/images/logo.png" alt="国际水晶疗愈协会" class="logo-image" />
				</div>
				<div class="login-welcome">
					<h2>欢迎来到</h2>
					<h1>国际水晶疗愈协会</h1>
					<p>登录后享受专业的水晶疗愈服务和丰富的学习资源</p>
				</div>
				<div class="login-decoration">
					<div class="crystal-1"></div>
					<div class="crystal-2"></div>
					<div class="crystal-3"></div>
				</div>
			</div>

			<div class="login-right">
				<div class="login-form-container" v-if="!isLoggedIn">
					<h2 class="login-title">用户登录</h2>
					<p class="login-subtitle">欢迎回来，请登录您的账号</p>
                    
                    <div class="login-tabs">
                        <div 
                            :class="['tab-item', { active: loginType === 'code' }]" 
                            @click="switchLoginType('code')"
                        >
                            验证码登录
                        </div>
                        <div 
                            :class="['tab-item', { active: loginType === 'password' }]" 
                            @click="switchLoginType('password')"
                        >
                            账号密码登录
                        </div>
                    </div>

					<van-form @submit="onSubmit" v-if="loginType === 'password'">
						<div class="form-item">
							<label for="username">用户名/手机号</label>
							<van-field
								v-model="loginForm.username"
								name="username"
								placeholder="请输入用户名或手机号"
								:rules="[{ required: true, message: '请输入用户名/手机号' }]"
								class="login-input"
							/>
						</div>

						<div class="form-item">
							<label for="password">密码</label>
							<van-field
								v-model="loginForm.password"
								type="password"
								name="password"
								placeholder="请输入密码"
								:rules="[{ required: true, message: '请输入密码' }]"
								class="login-input"
							/>
						</div>

						<div class="login-options">
							<div class="remember-me">
								<van-checkbox v-model="rememberMe">记住我</van-checkbox>
							</div>
							<!-- <router-link to="/forgotPassword" class="forgot-password">忘记密码？</router-link> -->
						</div>

						<div class="form-submit">
							<van-button round block type="primary" native-type="submit" :loading="loading">
								登录
							</van-button>
						</div>
					</van-form>

                    <van-form @submit="onCodeSubmit" v-else>
						<div class="form-item">
							<label for="phone">手机号</label>
							<van-field
								v-model="codeForm.phone"
								name="phone"
								placeholder="请输入手机号"
								:rules="[
                                    { required: true, message: '请输入手机号' },
                                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                                ]"
								class="login-input"
							/>
						</div>

						<div class="form-item">
							<label for="code">验证码</label>
                            <div class="code-field">
                                <van-field
                                    v-model="codeForm.code"
                                    name="code"
                                    placeholder="请输入验证码"
                                    :rules="[{ required: true, message: '请输入验证码' }]"
                                    class="login-input code-input"
                                />
                                <van-button 
                                    size="small" 
                                    type="primary" 
                                    class="code-button" 
                                    :disabled="codeSending || cooldown > 0"
                                    @click.prevent="getVerificationCode"
                                >
                                    {{ cooldown > 0 ? `${cooldown}秒后重新获取` : '获取验证码' }}
                                </van-button>
                            </div>
						</div>

						<div class="form-submit">
							<van-button round block type="primary" native-type="submit" :loading="loading">
								登录
							</van-button>
						</div>
					</van-form>

					<div class="register-link">
						<span>还没有账号？</span>
						<router-link to="/register">立即注册</router-link>
					</div>
				</div>

				<div class="logged-in-container" v-else>
					<div class="user-info">
						<img :src="userInfo.avatar || require('@/assets/images/pattern-dark.png')" alt="头像" class="user-avatar">
						<h3 class="user-name">{{ userInfo.nickname || userInfo.nickName || userInfo.username }}</h3>
						<p class="user-role">{{ userInfo.phone || '暂无手机号' }}</p>
					</div>
					<div class="action-buttons">
						<!-- <van-button round block type="primary" @click="goToUserCenter">
							进入个人中心
						</van-button> -->
						<van-button round block plain @click="logout" class="logout-btn">
							退出登录
						</van-button>
					</div>
				</div>
			</div>
		</div>
	</div>
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import Message from "@/utils/message";

export default {
	name: "LoginView",
	data() {
		return {
			redirect: '',
			isMobilePhone: isMobilePhone(),
			loading: false,
			isLoggedIn: false,
			userInfo: {},
			rememberMe: false,
            loginType: 'code', // 'password' 或 'code'
			loginForm: {
				username: '',
				password: ''
			},
            codeForm: {
                phone: '',
                code: ''
            },
            codeSending: false,
            cooldown: 0,
            timer: null
		}
	},
	components: {
		Layout
	},
	mounted() {
		this.redirect = decodeURIComponent(this.$route.query.redirect || '');
		this.$wxShare();
		this.checkLoginStatus();
	},
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
        }
    },
	methods: {
        switchLoginType(type) {
            this.loginType = type;
        },
		checkLoginStatus() {
			const token = localStorage.getItem("token");
			if (token) {
				try {
					const userInfoStr = localStorage.getItem("userInfo") ;
					if (userInfoStr) {
						this.userInfo = JSON.parse(userInfoStr);
						this.isLoggedIn = true;
					} else {
						// 有token但没有用户信息，尝试获取用户信息
						this.getUserInfo();
					}
				} catch (error) {
					console.error("解析用户信息失败", error);
					this.clearLoginInfo();
				}
			}
		},
		getUserInfo() {
			this.getRequest("/user").then(resp => {
				if (resp && resp.code == 200) {
					this.userInfo = resp.data;
					this.isLoggedIn = true;
					// 更新存储的用户信息
					sessionStorage.setItem("userInfo", JSON.stringify(resp.data));
					localStorage.setItem("userInfo", JSON.stringify(resp.data));
				} else {
					this.clearLoginInfo();
				}
			});
		},
		onSubmit() {
			this.loading = true;
			// 登录请求
			this.postRequest("/loginCms", {
				account: this.loginForm.username,
				password: this.loginForm.password
			})
				.then(resp => {
					this.loading = false;
					if (resp && resp.code == 200) {
						// 根据记住我选项决定存储位置
						// const storage = this.rememberMe ? localStorage : sessionStorage;
						// 保存token
						localStorage.setItem("token", resp.data.token);
						// 保存用户信息
						localStorage.setItem("userInfo", JSON.stringify(resp.data.userInfo));
						
						this.userInfo = resp.data.userInfo;
						this.isLoggedIn = true;
						
						Message.success("登录成功");
						
						// 检查是否有重定向URL
						if (this.redirect) {
							location.href = this.redirect;
						} else {
							// 跳转到首页
							this.$router.push("/");
						}
					} else {
						Message.error(resp.message || "登录失败，请检查用户名和密码");
					}
				});
		},
        onCodeSubmit() {
            if (!this.codeForm.phone || !this.codeForm.code) {
                Message.error("请输入手机号和验证码");
                return;
            }

            this.loading = true;
            // 验证码登录请求
            this.postRequest("/login/mobile", {
                phone: this.codeForm.phone,
                captcha: this.codeForm.code
            })
            .then(resp => {
                this.loading = false;
                if (resp && resp.code == 200) {
                    // 保存token
                    localStorage.setItem("token", resp.data.token);
                    // 保存用户信息
                    localStorage.setItem("userInfo", JSON.stringify(resp.data.userInfo));
                    
                    this.userInfo = resp.data.userInfo;
                    this.isLoggedIn = true;
                    
                    Message.success("登录成功");
                    
                    // 检查是否有重定向URL
                    if (this.redirect) {
						location.href = this.redirect;
                    } else {
                        // 跳转到首页
                        this.$router.push("/");
                    }
                } else {
                    Message.error(resp.message || "登录失败，请检查手机号和验证码");
                }
            });
        },
        getVerificationCode() {
            if (this.codeSending || this.cooldown > 0) return;
            
            const phone = this.codeForm.phone;
            if (!phone) {
                Message.error("请输入手机号");
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                Message.error("请输入正确的手机号码");
                return;
            }
            
            this.codeSending = true;
            
            // 发送验证码请求
            this.postRequestParams("/sendCode", {
                phone: phone,
            })
            .then(resp => {
                this.codeSending = false;
                if (resp && resp.code == 200) {
                    Message.success("验证码已发送，请注意查收");
                    // 开始倒计时
                    this.cooldown = 60;
                    this.timer = setInterval(() => {
                        this.cooldown--;
                        if (this.cooldown <= 0) {
                            clearInterval(this.timer);
                        }
                    }, 1000);
                } else {
                    Message.error(resp.message || "验证码发送失败，请稍后重试");
                }
            })
            .catch(() => {
                this.codeSending = false;
                Message.error("验证码发送失败，请稍后重试");
            });
        },
		goToUserCenter() {
			this.$router.push("/user");
		},
		logout() {
			// 退出登录请求
			this.postRequest("/logout").finally(() => {
				this.clearLoginInfo();
				Message.success("已退出登录");
				// 刷新页面
				this.isLoggedIn = false;
				this.userInfo = {};
				location.reload();
			});
		},
		clearLoginInfo() {
			// 清除所有存储的登录信息
			sessionStorage.removeItem("token");
			sessionStorage.removeItem("userInfo");
			localStorage.removeItem("token");
			localStorage.removeItem("userInfo");
			this.isLoggedIn = false;
			this.userInfo = {};
		}
	}
}
</script>

<style lang="less" scoped>
.login-page {
	min-height: 100vh;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f9fa;
	background-image: url('@/assets/images/pattern-dark.png');
	background-repeat: repeat;
	padding: 40px 20px;
}

.login-container {
	display: flex;
	width: 1200px;
	min-height: 680px;
	border-radius: 20px;
	overflow: hidden;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
	background-color: #fff;
}

.login-left {
	flex: 1;
	background: linear-gradient(135deg, #5e258f, #8647ad);
	background-image: linear-gradient(135deg, rgba(94, 37, 143, 0.95), rgba(134, 71, 173, 0.9)), url('https://images.unsplash.com/photo-1521320226546-87b106cd055d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
	background-size: cover;
	background-position: center;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	color: #fff;
	padding: 50px;
	overflow: hidden;
}

.login-logo {
	margin-bottom: 40px;
	text-align: center;
	position: relative;
	z-index: 2;
}

.logo-image {
	width: 180px;
	height: auto;
}

.login-welcome {
	text-align: center;
	margin-bottom: 40px;
	position: relative;
	z-index: 2;
}

.login-welcome h2 {
	font-size: 24px;
	font-weight: 400;
	margin-bottom: 20px;
	color: rgba(255, 255, 255, 0.9);
}

.login-welcome h1 {
	font-size: 38px;
	font-weight: 700;
	margin-bottom: 20px;
	text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.login-welcome p {
	font-size: 16px;
	line-height: 1.6;
	max-width: 400px;
	color: rgba(255, 255, 255, 0.8);
}

.login-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.crystal-1, .crystal-2, .crystal-3 {
	position: absolute;
	background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
	backdrop-filter: blur(5px);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.crystal-1 {
	width: 300px;
	height: 300px;
	top: -100px;
	left: -150px;
}

.crystal-2 {
	width: 200px;
	height: 200px;
	bottom: 50px;
	right: -50px;
}

.crystal-3 {
	width: 150px;
	height: 150px;
	bottom: -50px;
	left: 100px;
}

.login-right {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px;
	background-color: #fff;
}

.login-form-container, .logged-in-container {
	width: 100%;
	max-width: 400px;
}

.login-title {
	font-size: 32px;
	font-weight: 700;
	color: #333;
	margin-bottom: 10px;
	text-align: center;
}

.login-subtitle {
	font-size: 16px;
	color: #666;
	margin-bottom: 40px;
	text-align: center;
}

.login-tabs {
    display: flex;
    margin-bottom: 30px;
    border-bottom: 1px solid #e8eaee;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 15px 0;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.tab-item.active {
    color: #5e258f;
    font-weight: 500;
}

.tab-item.active:after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 20%;
    width: 60%;
    height: 3px;
    background: #5e258f;
    border-radius: 3px 3px 0 0;
}

.form-item {
	margin-bottom: 25px;
}

.form-item label {
	display: block;
	margin-bottom: 8px;
	font-size: 14px;
	color: #555;
	font-weight: 500;
}

.login-input {
	/deep/ .van-field__control {
		height: 48px;
		padding: 0 15px;
		font-size: 15px;
		background-color: #f5f7fa;
		border: 1px solid #e8eaee;
		border-radius: 8px;
	}

	/deep/ .van-field__control:focus {
		background-color: #fff;
		border-color: #5e258f;
		box-shadow: 0 0 0 3px rgba(94, 37, 143, 0.1);
	}
}

.code-field {
    display: flex;
    align-items: center;
}

.code-input {
    flex: 1;
    margin-right: 10px;
}

.code-button {
    white-space: nowrap;
    height: 48px;
    background: linear-gradient(45deg, #5e258f, #8647ad);
    border-color: #5e258f;
    
    &:disabled {
        background: #cccccc;
        border-color: #bbbbbb;
        color: #ffffff;
    }
}

.login-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30px;
}

.remember-me {
	/deep/ .van-checkbox__label {
		color: #666;
		font-size: 14px;
	}

	/deep/ .van-checkbox__icon--checked {
		background-color: #5e258f;
		border-color: #5e258f;
	}
}

.forgot-password {
	color: #5e258f;
	font-size: 14px;
	text-decoration: none;
	transition: color 0.2s;
	
	&:hover {
		color: #8647ad;
		text-decoration: underline;
	}
}

.form-submit {
	margin-bottom: 30px;
	
	.van-button {
		height: 50px;
		font-size: 16px;
		font-weight: 500;
		background: linear-gradient(45deg, #5e258f, #8647ad);
		border-color: #5e258f;
		transition: all 0.3s ease;
		
		&:hover {
			background: linear-gradient(45deg, #4b1e73, #733c94);
			border-color: #4b1e73;
			transform: translateY(-2px);
			box-shadow: 0 6px 15px rgba(94, 37, 143, 0.25);
		}
	}
}

.register-link {
	text-align: center;
	font-size: 14px;
	color: #666;
	
	a {
		color: #5e258f;
		font-weight: 500;
		text-decoration: none;
		margin-left: 5px;
		
		&:hover {
			text-decoration: underline;
		}
	}
}

.logged-in-container {
	text-align: center;
	padding: 20px 0;
	
	.user-info {
		margin-bottom: 40px;
		
		.user-avatar {
			width: 110px;
			height: 110px;
			border-radius: 50%;
			object-fit: cover;
			border: 4px solid rgba(94, 37, 143, 0.2);
			margin-bottom: 20px;
		}
		
		.user-name {
			font-size: 24px;
			font-weight: 600;
			margin-bottom: 8px;
			color: #333;
		}
		
		.user-role {
			font-size: 16px;
			color: #666;
		}
	}
	
	.action-buttons {
		.van-button {
			margin-bottom: 16px;
			height: 50px;
			font-size: 16px;
			
			&--primary {
				background: linear-gradient(45deg, #5e258f, #8647ad);
				border-color: #5e258f;
				
				&:hover {
					background: linear-gradient(45deg, #4b1e73, #733c94);
					border-color: #4b1e73;
				}
			}
		}
		
		.logout-btn {
			border-color: #f56c6c;
			color: #f56c6c;
			
			&:hover {
				background-color: rgba(245, 108, 108, 0.05);
			}
		}
	}
}

@media (max-width: 1200px) {
	.login-container {
		width: 95%;
		flex-direction: column;
		min-height: auto;
	}
	
	.login-left {
		padding: 40px 20px;
	}
	
	.login-right {
		padding: 40px 20px;
	}
	
	.login-welcome h1 {
		font-size: 32px;
	}
}

@media (max-width: 767px) {
	.login-page {
		padding: 20px 10px;
	}
	
	.login-container {
		width: 100%;
		border-radius: 10px;
	}
	
	.login-left {
		padding: 30px 15px;
	}
	
	.login-right {
		padding: 30px 15px;
	}
	
	.login-logo .logo-image {
		width: 150px;
	}
	
	.login-welcome h1 {
		font-size: 28px;
	}
	
	.login-welcome p {
		font-size: 14px;
	}
	
	.login-title {
		font-size: 28px;
	}
	
	.login-subtitle {
		font-size: 14px;
		margin-bottom: 30px;
	}
}
</style>
