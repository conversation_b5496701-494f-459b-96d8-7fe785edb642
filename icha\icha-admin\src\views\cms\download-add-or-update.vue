<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="dataForm.title" placeholder="标题"></el-input>
      </el-form-item>
      <el-form-item label="简介" prop="brief">
        <el-input v-model="dataForm.brief" placeholder="简介"></el-input>
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-select v-model="dataForm.fileType" placeholder="选择文件类型">
          <el-option v-for="item in fileTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-input v-model="dataForm.tags" placeholder="标签，多个用逗号分隔"></el-input>
      </el-form-item>
      <el-form-item label="文件" prop="fileUrl">
        <upload-file v-model="dataForm.fileUrl" :multiple="false"></upload-file>
      </el-form-item>
      <el-form-item label="文件大小" prop="fileSize">
        <el-input v-model="dataForm.fileSize" placeholder="文件大小，例如: 2MB"></el-input>
      </el-form-item>
      <el-form-item label="下载量" prop="downloads">
        <el-input-number v-model="dataForm.downloads" :min="0" placeholder="下载量"></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import UploadFile from '@/components/Upload/uploadFile.vue'
import { cmsDownloadCreateApi, cmsDownloadUpdateApi, cmsDownloadDetailApi } from '@/api/cmsDownload'
export default {
  components: {
    UploadFile
  },
  data() {
    return {
      visible: false,
      uploadFileUrl: process.env.VUE_APP_BASE_API + '/upload', // 上传文件接口
      fileList: [],
      fileTypes: [
        { value: 'pdf', label: 'PDF文档' },
        { value: 'doc', label: 'Word文档' },
        { value: 'xls', label: 'Excel表格' },
        { value: 'zip', label: '压缩文件' },
        { value: 'other', label: '其他文件' }
      ],
      dataForm: {
        id: 0,
        title: '',
        brief: '',
        fileType: '',
        tags: '',
        fileUrl: '',
        fileSize: '',
        downloads: 0
      },
      dataRule: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        fileUrl: [
          { required: true, message: '文件不能为空', trigger: 'blur' }
        ],
        fileType: [
          { required: true, message: '文件类型不能为空', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.fileList = []
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          cmsDownloadDetailApi(this.dataForm.id).then((data) => {
            this.dataForm.title = data.title
            this.dataForm.brief = data.brief
            this.dataForm.fileType = data.fileType
            this.dataForm.tags = data.tags
            this.dataForm.fileUrl = data.fileUrl
            this.dataForm.fileSize = data.fileSize
            this.dataForm.downloads = data.downloads

            // 如果已有文件
            if (data.fileUrl) {
              this.fileList = [{
                name: data.title + '文件',
                url: data.fileUrl
              }]
            }
          }).catch((res) => {
            this.$message.error(res.message)
          });
        }
      })
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.dataForm.id) {
            cmsDownloadCreateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          } else {
            cmsDownloadUpdateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.upLoadPicBox {
  width: 100px;
  height: 100px;
  position: relative;
  cursor: pointer;
}

.upLoadPicBox .pictrue {
  width: 100%;
  height: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.upLoadPicBox .pictrue img {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.upLoadPicBox .upLoad {
  width: 100%;
  height: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upLoadPicBox .upLoad:hover {
  border-color: #409EFF;
}

.upLoadPicBox .upLoad .cameraIconfont {
  font-size: 28px;
  color: #8c939d;
}

.upload-demo {
  margin-bottom: 15px;
}
</style>