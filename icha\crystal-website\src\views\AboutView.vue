<template>
	<Layout>
		<div class="about-container">
			<!-- 美化后的页面头部 -->
			<div class="hero-header-section about-header">
				<div class="hero-content">
					<h1 class="hero-title"><i class="fas fa-info-circle fa-spin-pulse"></i> {{aboutTitle}}</h1>
					<p class="hero-subtitle">{{aboutDesc}}</p>
				</div>
			</div>

			<!-- 协会简介 -->
			<section class="about-section intro-section">
				<div class="content-wrapper">
					<h2 class="section-title">国际水晶疗愈协会</h2>
					<div class="intro-content">
						<div class="intro-text">
							<p v-for="item in aboutText" :key="item">{{item}}</p>
						</div>
						<div v-if="!isMobilePhone" class="intro-decoration">
							<img :src="aboutLogo" alt="协会logo" class="crystal-shape">
						</div>
					</div>
				</div>
			</section>

			<!-- 我们的使命 -->
			<section class="about-section mission-section">
				<div class="content-wrapper">
					<h2 class="section-title">我们的使命</h2>
					<div class="mission-cards">
						<div class="mission-card" v-for="(item, index) in aboutHexin" :key="index">
							<div class="card-icon">
								<i :class="aboutLshiminglogo[index]"></i>
							</div>
							<div class="card-content">
								<p>{{item}}</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			<!-- 核心服务 -->
			<section class="about-section services-section">
				<div class="content-wrapper">
					<h2 class="section-title">核心服务</h2>
					<div class="services-grid">
						<div class="service-item">
							<div class="service-icon service-icon-1">
								<i class="fas fa-certificate" style="font-size: 36px; color: white;"></i>
							</div>
							<h3>疗愈师认证体系</h3>
							<p>从初阶到高级，系统培训水晶疗愈技能，颁发国际认可证书</p>
						</div>
						<div class="service-item">
							<div class="service-icon service-icon-2">
								<i class="fas fa-globe-asia" style="font-size: 36px; color: white;"></i>
							</div>
							<h3>全球讲师网络</h3>
							<p>邀请国际知名水晶疗愈导师授课，提供多语言、多文化的教学支持</p>
						</div>
						<div class="service-item">
							<div class="service-icon service-icon-3">
								<i class="fas fa-gem" style="font-size: 36px; color: white;"></i>
							</div>
							<h3>疗愈工具开发</h3>
							<p>联合各地工作坊开发疗愈水晶阵、冥想水晶套组等专业工具</p>
						</div>
						<div class="service-item">
							<div class="service-icon service-icon-4">
								<i class="fas fa-users" style="font-size: 36px; color: white;"></i>
							</div>
							<h3>全球社群连接</h3>
							<p>举办线上线下疗愈论坛、静心营、年度水晶大会等活动</p>
						</div>
						<div class="service-item">
							<div class="service-icon service-icon-5">
								<i class="fas fa-book-open" style="font-size: 36px; color: white;"></i>
							</div>
							<h3>研究与出版</h3>
							<p>推动水晶能量疗愈在心理学、能量医学、整合医学领域的交叉研究</p>
						</div>
					</div>
				</div>
			</section>

			<!-- 协会愿景 -->
			<section class="about-section vision-section">
				<div class="content-wrapper">
					<h2 class="section-title">协会愿景</h2>
					<div class="vision-content">
						<div class="vision-statement">
							<p>{{aboutYuanjing}}</p>
						</div>
						<div class="vision-beliefs">
							<h3>我们相信：</h3>
							<div class="beliefs-list">
								<div class="belief-item">
									<span class="crystal-icon"><i class="fas fa-gem"></i></span>
									<p>每一块水晶都是地球的智慧化身</p>
								</div>
								<div class="belief-item">
									<span class="crystal-icon"><i class="fas fa-spa"></i></span>
									<p>每一次疗愈，都是向内走的觉醒</p>
								</div>
								<div class="belief-item">
									<span class="crystal-icon"><i class="fas fa-hands-helping"></i></span>
									<p>每一个人，都值得被温柔对待</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>

			<!-- 组织宣言 -->
			<section class="about-section manifesto-section">
				<div class="content-wrapper">
					<h2 class="section-title">组织宣言</h2>
					<div class="manifesto-content">
						<div class="manifesto-quote">
							<p v-for="item in aboutXuanyan" :key="item">{{item}}</p>
						</div>
					</div>
				</div>
			</section>

			<!-- 组织架构 -->
			<section class="about-section structure-section">
				<div class="content-wrapper">
					<h2 class="section-title">组织架构</h2>
					<div class="structure-content">
						<div class="structure-item">
							<div class="structure-header">
								<div class="structure-number">
									<i class="fas fa-sitemap"></i>
								</div>
								<h3>理事会（Council）</h3>
							</div>
							<div class="structure-body">
								<p>职能：协会最高决策机构，负责制定战略方向、全球发展、品牌维护与伦理审查。</p>
								<p>成员：</p>
								<ul>
									<li>名誉理事长（Honorary Chairperson）</li>
									<li>会长（President）</li>
									<li>副会长（Vice Presidents：国际事务、教育事务、资源事务）</li>
									<li>创始理事（Founding Council Members）</li>
									<li>荣誉顾问团（Spiritual/Energy Healing Advisors）</li>
								</ul>
							</div>
						</div>

						<div class="structure-item">
							<div class="structure-header">
								<div class="structure-number">
									<i class="fas fa-building"></i>
								</div>
								<h3>执行团队（Executive Office）</h3>
							</div>
							<div class="structure-body">
								<p>负责日常运营与项目落地，设以下部门：</p>
								
								<div class="sub-structure">
									<h4>2.1 教育认证部（Education & Accreditation）</h4>
									<ul>
										<li>制定疗愈师培训体系、课程标准</li>
										<li>认证全球疗愈师、讲师与合作机构</li>
										<li>维护ICHA水晶疗愈师等级制度（初级/中级/高级/导师）</li>
									</ul>
								</div>
								
								<div class="sub-structure">
									<h4>2.2 全球事务部（International Development）</h4>
									<ul>
										<li>拓展各国分部与授权学院</li>
										<li>建立地区性合作网络（如东亚分部、欧洲分部等）</li>
										<li>跨文化课程翻译与适配</li>
									</ul>
								</div>
								
								<div class="sub-structure">
									<h4>2.3 项目策划与活动部（Programs & Events）</h4>
									<ul>
										<li>策划疗愈论坛、水晶大会、能量节、静修营等</li>
										<li>安排导师巡回讲座与跨国合作</li>
										<li>线上冥想/疗愈仪式/工作坊统筹</li>
									</ul>
								</div>
								
								<div class="sub-structure">
									<h4>2.4 品牌与内容部（Brand & Media）</h4>
									<ul>
										<li>网站、社群媒体、出版物、教学资料制作</li>
										<li>负责视觉设计、品牌形象维护</li>
										<li>与自媒体、疗愈KOL合作传播</li>
									</ul>
								</div>
								
								<div class="sub-structure">
									<h4>2.5 产品与工具研发部（Healing Tools & Product Lab）</h4>
									<ul>
										<li>开发水晶疗愈套装、水晶证书、冥想音频等辅助工具</li>
										<li>研究不同文化中的水晶疗愈方法并商品化整合</li>
									</ul>
								</div>
								
								<div class="sub-structure">
									<h4>2.6 秘书与行政部（Secretariat & Finance）</h4>
									<ul>
										<li>负责成员管理、财务报表、合规与合同</li>
										<li>会员系统维护、组织章程更新</li>
									</ul>
								</div>
							</div>
						</div>

						<div class="structure-item">
							<div class="structure-header">
								<div class="structure-number">
									<i class="fas fa-map-marked-alt"></i>
								</div>
								<h3>地区代表处（Regional Chapters）</h3>
							</div>
							<div class="structure-body">
								<ul>
									<li>每个大洲/国家/城市设地区代表或认证合作方</li>
									<li>定期组织线下沙龙、疗愈会、师资培训班</li>
									<li>支持全球语言与文化本地化</li>
								</ul>
							</div>
						</div>

						<div class="structure-item">
							<div class="structure-header">
								<div class="structure-number">
									<i class="fas fa-users-cog"></i>
								</div>
								<h3>顾问团（Advisory Board）</h3>
							</div>
							<div class="structure-body">
								<p>由以下专业背景人员组成，提供战略建议与专业支持：</p>
								<ul>
									<li>能量疗愈导师</li>
									<li>天然矿物学专家</li>
									<li>身心灵教育顾问</li>
									<li>心理学或整合医学专家</li>
									<li>可持续采矿伦理专家</li>
								</ul>
							</div>
						</div>

						<div class="structure-item">
							<div class="structure-header">
								<div class="structure-number">
									<i class="fas fa-user-friends"></i>
								</div>
								<h3>成员体系（Membership）</h3>
							</div>
							<div class="structure-body">
								<ul>
									<li>疗愈师成员（Certified Crystal Healers）</li>
									<li>导师成员（Accredited Instructors）</li>
									<li>行业成员（水晶供应商、疗愈产品开发者）</li>
									<li>终身会员/赞助会员（提供资金或平台支持者）</li>
									<li>学习会员（正在参与认证课程的学员）</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import '../assets/css/common-headers.css'; // 导入头部共用样式f

export default {
	name: "AboutView",
	components: { Layout },
	data() {
		return {
			aboutLshiminglogo: ['fas fa-heart', 'fas fa-certificate', 'fas fa-globe-asia', 'fas fa-microscope', 'fas fa-leaf'],
			isMobilePhone: isMobilePhone(),
			companyInfo: {},
			designers: [],
			aboutXuanyan: '',
			aboutYuanjing: '',
			aboutHexin: [],
			aboutLshiming: [],
			aboutLogo: '',
			aboutText: '',
			aboutDesc: '',
			aboutTitle: '',
		}
	},
	mounted() {
		// this.getCompanyInfo()
		this.$wxShare();
		this.getAboutConfig();
		// this.getDesigners()
	},
	methods: {
		getAboutConfig() {
			this.getRequest("/cms/config/about").then(resp => {
				this.aboutXuanyan = resp.data.aboutXuanyan
				this.aboutYuanjing = resp.data.aboutYuanjing
				this.aboutHexin = resp.data.aboutHexin
				this.aboutLshiming = resp.data.aboutLshiming
				this.aboutLogo = resp.data.aboutLogo
				this.aboutText = resp.data.aboutText
				this.aboutDesc = resp.data.aboutDesc
				this.aboutTitle = resp.data.aboutTitle
			})
		}
	}
}
</script>

<style scoped>
/* 美化后的页面头部样式 */
.about-header {
	background-image: url('https://img.freepik.com/free-photo/watercolor-pastel-texture-background_53876-98173.jpg') !important;
}

/* 全局页面样式 */
.about-container {
	width: 100%;
	color: #333;
	font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
}

.content-wrapper {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.about-section {
	padding: 70px 0;
	position: relative;
}

.section-title {
	text-align: center;
	font-size: 36px;
	margin-bottom: 50px;
	color: #333;
	font-weight: 300;
	position: relative;
}

.section-title:after {
	content: "";
	display: block;
	width: 60px;
	height: 3px;
	background: linear-gradient(135deg, #7b68ee, #b19cd9);
	margin: 15px auto 0;
}

/* 页面标题区域 */
.page-banner {
	height: 280px;
	background: linear-gradient(rgba(108, 95, 187, 0.9), rgba(171, 151, 216, 0.8)), url('https://images.unsplash.com/photo-1507652313519-d4e9174996dd?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') center/cover;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	text-align: center;
}

.page-banner-content h1 {
	font-size: 48px;
	letter-spacing: 2px;
	font-weight: 300;
	margin: 0;
}

.banner-decoration {
	width: 100px;
	height: 2px;
	background-color: rgba(255, 255, 255, 0.8);
	margin: 20px auto 0;
}

/* 简介部分 */
.intro-section {
	background-color: #fff;
}

.intro-content {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}

.intro-text {
	flex: 1;
	min-width: 300px;
	padding-right: 50px;
}

.intro-text p {
	line-height: 1.8;
	font-size: 16px;
	margin-bottom: 20px;
	color: #555;
}

.intro-decoration {
	flex: 0 0 200px;
	display: flex;
	justify-content: center;
	margin: 20px 0;
}

.crystal-shape {
	width: 180px;
	height: 180px;
	border-radius: 50%;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 使命部分 */
.mission-section {
	background-color: #f8f9fa;
}

.mission-cards {
	display: flex;
	flex-direction: column;
	gap: 20px;
	max-width: 800px;
	margin: 0 auto;
}

.mission-card {
	display: flex;
	background-color: white;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
	transition: transform 0.3s ease;
}

.mission-card:hover {
	transform: translateY(-5px);
}

.card-icon {
	width: 60px;
	min-width: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(to bottom, #7b68ee, #b19cd9);
	color: white;
	font-size: 24px;
}

.card-content {
	padding: 20px;
}

.card-content p {
	margin: 0;
	line-height: 1.6;
	color: #555;
}

/* 核心服务部分 */
.services-section {
	background-color: #fff;
}

.services-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
	gap: 30px;
}

.service-item {
	background-color: white;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	text-align: center;
	padding: 30px 20px;
}

.service-item:hover {
	transform: translateY(-10px);
	box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-icon {
	width: 80px;
	height: 80px;
	margin: 0 auto 20px;
	border-radius: 50%;
	background-color: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.service-item h3 {
	font-size: 20px;
	margin-bottom: 15px;
	color: #444;
}

.service-item p {
	color: #666;
	line-height: 1.6;
}

/* 服务图标 - 不同颜色和背景 */
.service-icon-1 {
	background: linear-gradient(45deg, #a18cd1, #fbc2eb);
}

.service-icon-2 {
	background: linear-gradient(45deg, #84fab0, #8fd3f4);
}

.service-icon-3 {
	background: linear-gradient(45deg, #ff9a9e, #fad0c4);
}

.service-icon-4 {
	background: linear-gradient(45deg, #ffecd2, #fcb69f);
}

.service-icon-5 {
	background: linear-gradient(45deg, #a1c4fd, #c2e9fb);
}

/* 愿景部分 */
.vision-section {
	background-color: #f8f9fa;
	text-align: center;
}

.vision-statement {
	max-width: 800px;
	margin: 0 auto 50px;
	padding: 30px;
	background-color: white;
	border-radius: 8px;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.vision-statement p {
	font-size: 20px;
	line-height: 1.6;
	color: #555;
	font-style: italic;
}

.vision-beliefs h3 {
	font-size: 22px;
	margin-bottom: 30px;
	color: #444;
}

.beliefs-list {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 30px;
}

.belief-item {
	flex: 1;
	min-width: 250px;
	max-width: 300px;
	padding: 20px;
	background-color: white;
	border-radius: 8px;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.crystal-icon {
	font-size: 28px;
	display: block;
	margin-bottom: 15px;
	color: #7b68ee;
}

.crystal-icon i {
	font-size: 32px;
}

.belief-item p {
	color: #555;
	line-height: 1.6;
}

/* 宣言部分 */
.manifesto-section {
	background: linear-gradient(rgba(108, 95, 187, 0.9), rgba(171, 151, 216, 0.9)), url('https://images.unsplash.com/photo-1519431940815-70facd7b434e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') center/cover;
	color: white;
}

.manifesto-section .section-title {
	color: white;
}

.manifesto-section .section-title:after {
	background-color: rgba(255, 255, 255, 0.6);
}

.manifesto-content {
	max-width: 800px;
	margin: 0 auto;
	text-align: center;
	line-height: 2;
}

.manifesto-quote {
	font-size: 18px;
	line-height: 1.8;
}

.manifesto-quote p {
	margin-bottom: 25px;
}

/* 组织架构 */
.structure-section {
	background-color: #fff;
}

.structure-content {
	display: flex;
	flex-direction: column;
	gap: 30px;
}

.structure-item {
	background-color: #fff;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.structure-header {
	display: flex;
	align-items: center;
	padding: 20px;
	background: linear-gradient(to right, #f0f0f0, #fff);
	border-bottom: 1px solid #eee;
}

.structure-number {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: linear-gradient(135deg, #7b68ee, #b19cd9);
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	margin-right: 15px;
	font-size: 20px;
}

.structure-header h3 {
	margin: 0;
	font-size: 20px;
	color: #444;
}

.structure-body {
	padding: 25px;
}

.structure-body p {
	margin-bottom: 15px;
	line-height: 1.6;
	color: #555;
}

.structure-body ul {
	list-style-type: none;
	padding: 0;
	margin: 0;
}

.structure-body li {
	position: relative;
	padding-left: 20px;
	margin-bottom: 10px;
	line-height: 1.6;
	color: #555;
}

.structure-body li:before {
	content: "•";
	color: #7b68ee;
	position: absolute;
	left: 0;
	font-size: 20px;
	line-height: 1;
}

.sub-structure {
	margin-top: 25px;
}

.sub-structure h4 {
	color: #7b68ee;
	margin-bottom: 15px;
	font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.page-banner {
		height: 200px;
	}
	
	.page-banner-content h1 {
		font-size: 36px;
	}
	
	.about-section {
		padding: 50px 0;
	}
	
	.section-title {
		font-size: 28px;
		margin-bottom: 30px;
	}
	
	.intro-text {
		padding-right: 0;
	}
	
	.belief-item {
		min-width: 100%;
	}
	
	.manifesto-quote {
		font-size: 16px;
	}
}
</style>
