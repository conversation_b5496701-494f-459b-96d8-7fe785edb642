<template>
	<Layout>
		<div class="layout-container" style="width: 100%">
			<!-- 美化后的页面头部 -->
			<div class="hero-header-section certificate-header">
				<div class="hero-content">
					<h1 class="hero-title"><i class="fa fa-certificate fa-spin-pulse"></i> 证书查询</h1>
					<p class="hero-subtitle">专业认证体系，权威资质背书</p>
				</div>
			</div>

			<div class="section">
				<div class="certificate-container">
					<div class="section--header text-center">
						<!-- <h2 class="section--title-nocolor" :style="isMobilePhone ? 'font-size: 20px' : ''">证书验证查询系统</h2> -->
						<p class="section--description">
							请输入证书编号或持证人姓名进行查询，验证证书的真实性和有效性。
						</p>
					</div>

					<div class="certificate-main">
						<!-- 左侧查询表单 -->
						<div class="query-form-container">
							<div class="form-group">
								<label>证书编号/持证人姓名</label>
								<input type="text" v-model="searchForm.name" placeholder="请输入证书编号/持证人姓名" class="form-control" />
							</div>
							
							<div class="form-actions">
								<button class="query-btn" @click="searchCertificate" :disabled="submitting">
									<span v-if="!submitting">查询</span>
									<span v-else><i class="fa fa-spinner fa-spin"></i> 查询中</span>
								</button>
								<button class="reset-btn" @click="resetForm">重置</button>
							</div>
						</div>
						
						<!-- 右侧查询结果 -->
						<div class="query-result-container" v-if="searchStatus !== 'init'">
							<!-- 查询成功 - 修改为多证书循环显示 -->
							<div v-if="searchStatus == 'success' && certificates.length > 0">
								<div v-for="(certificate, index) in certificates" :key="index" class="certificate-info">
									<div class="certificate-header">
										<img class="certificate-icon" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0OCIgaGVpZ2h0PSI0OCIgdmlld0JveD0iMCAwIDQ4IDQ4IiBmaWxsPSJub25lIj48cGF0aCBkPSJNMzQgMTBIOEMyLjcgMTAgMi4zIDExIDIuMyAxMS4zVjM2LjdDMi4zIDM3LjMgMyAzOCA1IDM4SDM3QzM5IDM4IDM5LjcgMzcuMyAzOS43IDM2LjdWMTRDMzkuNyAxMiAzOCAxMCAzNCAxMFoiIGZpbGw9IiMyZDZjYTIiPjwvcGF0aD48cGF0aCBkPSJNMTMgMjRDMTMgMjMuNiAxMy4yIDIzLjQgMTMuOCAyMy40SDE4QzE4LjIgMjMuNCAxOC40IDIzLjYgMTguNCAyNEMxOC40IDI0LjQgMTguMiAyNC42IDE4IDI0LjZIMTRDMTMuNCAyNC42IDEzIDI0LjQgMTMgMjRaTTIwIDI2QzIwIDI1LjYgMjAuMiAyNS40IDIwLjggMjUuNEgzMkMzMi4yIDI1LjQgMzIuNCAyNS42IDMyLjQgMjZDMzIuNCAyNi40IDMyLjIgMjYuNiAzMiAyNi42SDIxQzIwLjQgMjYuNiAyMCAyNi40IDIwIDI2Wk0xMyAyOEMxMyAyNy42IDEzLjIgMjcuNCAxMy44IDI3LjRIMThDMTguMiAyNy40IDE4LjQgMjcuNiAxOC40IDI4QzE4LjQgMjguNCAxOC4yIDI4LjYgMTggMjguNkgxNEMxMy40IDI4LjYgMTMgMjguNCAxMyAyOFpNMjAgMzBDMjAgMjkuNiAyMC4yIDI5LjQgMjAuOCAyOS40SDI2QzI2LjIgMjkuNCAyNi40IDI5LjYgMjYuNCAzMEMyNi40IDMwLjQgMjYuMiAzMC42IDI2IDMwLjZIMjFDMjAuNCAzMC42IDIwIDMwLjQgMjAgMzBaTTIwIDIyQzIwIDIxLjYgMjAuMiAyMS40IDIwLjggMjEuNEgzMkMzMi4yIDIxLjQgMzIuNCAyMS42IDMyLjQgMjJDMzIuNCAyMi40IDMyLjIgMjIuNiAzMiAyMi42SDIxQzIwLjQgMjIuNiAyMCAyMi40IDIwIDIyWk0xMyAyMEMxMyAxOS42IDEzLjIgMTkuNCAxMy44IDE5LjRIMThDMTguMiAxOS40IDE4LjQgMTkuNiAxOC40IDIwQzE4LjQgMjAuNCAxOC4yIDIwLjYgMTggMjAuNkgxNEMxMy40IDIwLjYgMTMgMjAuNCAxMyAyMFoiIGZpbGw9IiNmZmYiPjwvcGF0aD48L3N2Zz4=" alt="证书" />
										<div class="certificate-status">有效证书</div>
									</div>
									<!-- 证书图片 -->
									<div class="certificate-image">
										<img :src="certificate.image" alt="证书" />
									</div>
									<div class="detail-item">
										<div class="detail-label">证书编号：</div>
										<div class="detail-value">{{certificate.number}}</div>
									</div>
									<div class="detail-item">
										<div class="detail-label">持证人：</div>
										<div class="detail-value">{{certificate.name}}</div>
									</div>
									<div class="detail-item">
										<div class="detail-label">证书类型：</div>
										<div class="detail-value">{{(certificate.type === null || certificate.type === '' || certificate.type === undefined) ? '' : certificateType[certificate.type].value}}</div>
									</div>
									<div class="detail-item">
										<div class="detail-label">发证日期：</div>
										<div class="detail-value">{{certificate.startTime}}</div>
									</div>
									<!-- <div class="detail-item">
										<div class="detail-label">有效期至：</div>
										<div class="detail-value">{{certificate.expireDate}}</div>
									</div> -->
									<div class="detail-item">
										<div class="detail-label">发证机构：</div>
										<div class="detail-value">{{certificate.issuer}}</div>
									</div>
								</div>
								<div class="certificate-count" v-if="certificates.length > 1">
									共找到 {{certificates.length}} 条证书记录
								</div>
							</div>
							
							<!-- 无结果 -->
							<div class="no-data-result" v-else-if="searchStatus == 'empty'">
								<i class="fa fa-exclamation-circle"></i>
								<p>未找到符合条件的证书，请核对证书编号或持证人姓名后重试。</p>
							</div>
							
							<!-- 查询出错 -->
							<div class="error-result" v-else-if="searchStatus == 'error'">
								<i class="fa fa-times-circle"></i>
								<p>查询过程中发生错误，请稍后重试。</p>
							</div>
						</div>
						
						<!-- 当没有查询结果时显示的占位内容 -->
						<div class="query-result-placeholder" v-if="searchStatus == 'init'">
							<div class="placeholder-content">
								<img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI5NiIgaGVpZ2h0PSI5NiIgdmlld0JveD0iMCAwIDk2IDk2IiBmaWxsPSJub25lIj48cGF0aCBvcGFjaXR5PSIwLjIiIGQ9Ik00OCA5NkMyMS40OTAzIDk2IDAgNzQuNTA5NyAwIDQ4QzAgMjEuNDkwMyAyMS40OTAzIDAgNDggMEM3NC41MDk3IDAgOTYgMjEuNDkwMyA5NiA0OEM5NiA3NC41MDk3IDc0LjUwOTcgOTYgNDggOTZaIiBmaWxsPSIjNTE2NzkwIj48L3BhdGg+PHBhdGggZD0iTTM0LjUgNDhDMzQuNSA0MC4yMiA0MC43MiAzNCA0OC41IDM0QzU2LjI4IDM0IDYyLjUgNDAuMjIgNjIuNSA0OEM2Mi41IDU1Ljc4IDU2LjI4IDYyIDQ4LjUgNjJDNDAuNzIgNjIgMzQuNSA1NS43OCAzNC41IDQ4Wk01MiA0M0M1MiA0MC4yNCA0OS43NiAzOCA0NyAzOEM0NC4yNCAzOCA0MiA0MC4yNCA0MiA0M0M0MiA0NS43NiA0NC4yNCA0OCA0NyA0OEg1MlY0M1oiIGZpbGw9IiM1MTY3OTAiPjwvcGF0aD48cGF0aCBkPSJNNDIgNzBDNDIgNjQuNDggNDYuNDggNjAgNTIgNjBINzBWNzBINTJDNDYuNDggNzAgNDIgNjUuNTIgNDIgNjBWNDVDNDIgMzkuNDggNDYuNDggMzUgNTIgMzVINjBWNDVINTJDNDYuNDggNDUgNDIgNDAuNTIgNDIgMzVWMzBIMzJ2NTBDNDI2IDgwIDQyIDc1LjUyIDQyIDcwWiIgZmlsbD0iIzUxNjc5MCI+PC9wYXRoPjwvc3ZnPg==" alt="查询提示" class="placeholder-icon">
								<p>输入证书编号或持证人姓名后点击查询</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import '../assets/css/common-headers.css'; // 导入头部共用样式

export default {
	name: "CertificateView",
	components: { Layout },
	data() {
		return {
			isMobilePhone: isMobilePhone(),
			searchForm: {
				name: ''
			},
			searchStatus: 'init', // init, success, empty, error
			certificates: [], // 改为数组存储多个证书
			submitting: false,
			certificateType: [
				{
					key: 0,
					value: "水晶疗愈高级认证"
				},
]
		}
	},
	mounted() {
		this.$wxShare();
	},
	methods: {
		searchCertificate() {
			// 验证表单
			if (!this.searchForm.name) {
				this.$message.warning('请至少输入证书编号或持证人姓名');
				return;
			}
			
			this.submitting = true;
			
			// 实际项目中使用真实API
			this.getRequest("/cms/certificate/query", this.searchForm)
			  .then(resp => {
			    this.submitting = false;
			    if (resp && resp.code == 200) {
			      if (resp.data && resp.data.length > 0) {
			          // 如果返回数组，直接使用
			          this.certificates = resp.data;
			        this.searchStatus = 'success';
			      } else {
			        this.certificates = [];
			        this.searchStatus = 'empty';
			      }
			    } else {
			      this.certificates = [];
			      this.searchStatus = 'error';
			    }
			  });
			
		},
		resetForm() {
			this.searchForm = {
				name: ''
			};
			this.searchStatus = 'init';
			this.certificates = [];
		}
	}
}
</script>

<style scoped>
/* 美化后的页面头部样式 */
.certificate-header {
	background-image: url('https://img.freepik.com/free-photo/watercolor-pastel-texture-background_53876-98173.jpg') !important;
}

.text-center {
	text-align: center;
}

.section--header {
	margin-bottom: 40px;
}

.section--title-nocolor {
	font-size: 28px;
	margin-bottom: 15px;
	color: #3a2c58;
	font-weight: 600;
}

.section--description {
	max-width: 800px;
	margin: 0 auto;
	color: #666;
	line-height: 1.6;
	font-size: 16px;
}

.certificate-container {
	max-width: 1000px;
	margin: 0 auto;
	padding: 0 20px;
}

.certificate-main {
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
	margin-bottom: 50px;
	justify-content: center;
}

.certificate-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.certificate-image img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.query-form-container {
	flex: 1;
	min-width: 300px;
	max-width: 450px;
	background: #fff;
	border-radius: 8px;
	padding: 35px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	border: 1px solid #f0f0f0;
}

.query-result-container,
.query-result-placeholder {
	flex: 1;
	min-width: 300px;
	max-width: 450px;
	background: #fff;
	border-radius: 8px;
	padding: 35px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	border: 1px solid #f0f0f0;
}

.query-form-container:hover,
.query-result-container:hover {
	box-shadow: 0 15px 40px rgba(81, 103, 144, 0.1);
}

/* 表单元素样式 */
.form-group {
	margin-bottom: 25px;
}

.form-group label {
	display: block;
	margin-bottom: 10px;
	font-size: 15px;
	color: #444;
	font-weight: 500;
}

.form-control {
	width: 100%;
	height: 45px;
	padding: 0 15px;
	border: 1px solid #e0e0e0;
	border-radius: 6px;
	font-size: 15px;
	box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
	transition: all 0.3s;
}

.form-control:focus {
	border-color: #516790;
	box-shadow: 0 0 0 3px rgba(81, 103, 144, 0.15);
	outline: none;
}

.form-actions {
	display: flex;
	gap: 15px;
	margin-top: 30px;
}

.query-btn, .reset-btn {
	padding: 10px 25px;
	border-radius: 6px;
	font-size: 15px;
	cursor: pointer;
	border: none;
	font-weight: 500;
	transition: all 0.3s;
}

.query-btn {
	background: linear-gradient(135deg, #516790, #3a4f6e);
	color: #fff;
	flex: 1.5;
}

.query-btn:hover:not(:disabled) {
	background: linear-gradient(135deg, #3a4f6e, #2a3a50);
	transform: translateY(-2px);
	box-shadow: 0 4px 10px rgba(58, 79, 110, 0.3);
}

.reset-btn {
	background-color: #f5f5f5;
	color: #555;
	border: 1px solid #e0e0e0;
	flex: 1;
}

.reset-btn:hover {
	background-color: #efefef;
	color: #333;
}

.query-btn:disabled {
	background: #a0aec0;
	cursor: not-allowed;
	transform: none;
	box-shadow: none;
}

/* 证书详情样式 */
.certificate-info {
	padding: 5px 0;
	margin-bottom: 25px;
	border-bottom: 1px dashed #e0e0e0;
}

.certificate-info:last-child {
	margin-bottom: 10px;
	border-bottom: none;
}

.certificate-header {
	display: flex;
	align-items: center;
	margin-bottom: 25px;
	padding-bottom: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.certificate-icon {
	width: 48px;
	height: 48px;
	margin-right: 15px;
}

.certificate-status {
	background-color: #e6f7e6;
	color: #2c8a3e;
	padding: 6px 15px;
	border-radius: 20px;
	font-size: 14px;
	font-weight: 500;
	box-shadow: 0 2px 5px rgba(44, 138, 62, 0.15);
}

.detail-item {
	display: flex;
	margin-bottom: 18px;
	border-bottom: 1px solid #f5f5f5;
	padding-bottom: 18px;
}

.detail-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
	padding-bottom: 0;
}

.detail-label {
	width: 100px;
	color: #666;
	font-size: 15px;
}

.detail-value {
	flex: 1;
	color: #333;
	font-size: 15px;
	font-weight: 500;
}

/* 证书计数样式 */
.certificate-count {
	text-align: center;
	margin-top: 20px;
	padding: 10px;
	background-color: #f8f9fa;
	border-radius: 6px;
	color: #516790;
	font-weight: 500;
}

/* 无数据和错误提示 */
.no-data-result,
.error-result {
	text-align: center;
	padding: 40px 0;
}

.no-data-result i,
.error-result i {
	font-size: 48px;
	margin-bottom: 15px;
}

.no-data-result i {
	color: #f59e0b;
}

.error-result i {
	color: #e53e3e;
}

.no-data-result p,
.error-result p {
	color: #666;
	font-size: 15px;
	line-height: 1.5;
}

/* 占位符样式 */
.placeholder-content {
	text-align: center;
	padding: 60px 20px;
}

.placeholder-icon {
	width: 96px;
	height: 96px;
	margin-bottom: 20px;
	opacity: 0.8;
}

.placeholder-content p {
	color: #888;
	font-size: 15px;
}

/* 移动端适配 */
@media (max-width: 990px) {
	.certificate-main {
		flex-direction: column;
		align-items: center;
	}
	
	.query-form-container,
	.query-result-container,
	.query-result-placeholder {
		width: 100%;
		max-width: 100%;
	}
}

@media (max-width: 768px) {
	.certificate-container {
		padding: 0 15px;
	}
	
	.query-form-container,
	.query-result-container,
	.query-result-placeholder {
		padding: 25px;
	}
	
	.form-actions {
		flex-direction: column;
	}
	
	.query-btn, .reset-btn {
		width: 100%;
		margin-top: 10px;
	}
	
	.detail-item {
		flex-direction: column;
	}
	
	.detail-label {
		width: 100%;
		margin-bottom: 5px;
	}
}
</style>
