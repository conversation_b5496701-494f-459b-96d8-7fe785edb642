<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item label="简介" prop="intro">
        <el-input v-model="dataForm.intro" placeholder="简介"></el-input>
      </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <div class="upLoadPicBox" @click="modalPicTap('1')">
          <div v-if="dataForm.avatar" class="pictrue"><img :src="dataForm.avatar"></div>
          <div v-else class="upLoad">
            <i class="el-icon-camera cameraIconfont" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-input v-model="dataForm.tags" placeholder="标签，多个用逗号分隔"></el-input>
      </el-form-item>
      <el-form-item label="地址" prop="location">
        <el-input v-model="dataForm.location" placeholder="地址"></el-input>
      </el-form-item>
      <el-form-item label="是否展示" prop="isShow">
        <el-select v-model="dataForm.isShow" placeholder="是否展示" filterable>
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否首页" prop="isIndex">
        <el-select v-model="dataForm.isIndex" placeholder="是否首页" filterable>
          <el-option v-for="item in yesOrNo" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="paixu">
        <el-input-number v-model="dataForm.paixu" :min="0" placeholder="排序"></el-input-number>
      </el-form-item>
      <el-form-item label="详细介绍" prop="content">
        <Tinymce ref="tinymceEditor" v-model="dataForm.content" placeholder="详细介绍"></Tinymce>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import Tinymce from '@/components/Tinymce/index'
import { cmsHealersCreateApi, cmsHealersUpdateApi, cmsHealersDetailApi } from '@/api/cmsHealers'
import { yesOrNo } from '@/data/common'
export default {
  components: { Tinymce },
  data() {
    return {
      yesOrNo,
      visible: false,
      dataForm: {
        id: 0,
        name: '',
        intro: '',
        avatar: '',
        tags: '',
        location: '',
        isShow: 1,
        isIndex: 0,
        paixu: 0,
        content: ''
      },
      dataRule: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        isShow: [
          { required: true, message: '是否展示不能为空', trigger: 'blur' }
        ],
        isIndex: [
          { required: true, message: '是否首页不能为空', trigger: 'blur' }
        ],
        paixu: [
          { required: true, message: '排序不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.dataForm.content = ''
        this.$nextTick(() => {
          if (this.$refs.tinymceEditor) {
            this.$refs.tinymceEditor.resetContent('')
          }
        })
        if (this.dataForm.id) {
          cmsHealersDetailApi(this.dataForm.id).then((data) => {
            this.dataForm.name = data.name
            this.dataForm.intro = data.intro
            this.dataForm.avatar = data.avatar
            this.dataForm.tags = data.tags
            this.dataForm.location = data.location
            this.dataForm.isShow = data.isShow
            this.dataForm.isIndex = data.isIndex
            this.dataForm.paixu = data.paixu
            this.dataForm.content = data.content
            this.$nextTick(() => {
              if (this.$refs.tinymceEditor) {
                this.$refs.tinymceEditor.resetContent(data.content || '')
              }
            })
          }).catch((res) => {
            this.$message.error(res.message)
          });
        }
      })
    },
    // 点击图片上传
    modalPicTap(field) {
      const _this = this;
      this.$modalUpload(function (img) {
        _this.dataForm.avatar = img[0].sattDir
      }, field, 'content')
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.dataForm.id) {
            cmsHealersCreateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          } else {
            cmsHealersUpdateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.upLoadPicBox {
  width: 100px;
  height: 100px;
  position: relative;
  cursor: pointer;
}

.upLoadPicBox .pictrue {
  width: 100%;
  height: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.upLoadPicBox .pictrue img {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.upLoadPicBox .upLoad {
  width: 100%;
  height: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upLoadPicBox .upLoad:hover {
  border-color: #409EFF;
}

.upLoadPicBox .upLoad .cameraIconfont {
  font-size: 28px;
  color: #8c939d;
}
</style>