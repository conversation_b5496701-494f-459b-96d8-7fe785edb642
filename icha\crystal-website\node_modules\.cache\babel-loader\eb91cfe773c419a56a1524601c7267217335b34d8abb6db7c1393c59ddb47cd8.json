{"ast": null, "code": "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: \"Footer\",\n  data() {\n    return {\n      aboutaddress: '',\n      aboutemail: '',\n      aboutmobile: '',\n      footerabout: '',\n      aboutkefutime: ''\n    };\n  },\n  mounted() {\n    this.getConfigFooter();\n  },\n  methods: {\n    getConfigFooter() {\n      this.getRequest(\"/cms/config/footer\").then(resp => {\n        if (resp && resp.code == 200) {\n          this.aboutaddress = resp.data.aboutaddress;\n          this.aboutemail = resp.data.aboutemail;\n          this.aboutmobile = resp.data.aboutmobile;\n          this.footerabout = resp.data.footerabout;\n          this.aboutkefutime = resp.data.aboutkefutime;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "aboutaddress", "aboutemail", "aboutmobile", "footerabout", "aboutkefutime", "mounted", "getConfigFooter", "methods", "getRequest", "then", "resp", "code"], "sources": ["src/components/common/footer/Footer.vue"], "sourcesContent": ["<template>\r\n\t<div class=\"footer\">\r\n\t\t<div style=\"background-color:#383d61\" class=\"footer--bg\"></div>\r\n\t\t<div class=\"footer--inner\">\r\n\t\t\t<div class=\"am-g\">\r\n\t\t\t\t<div class=\"am-u-md-3 \">\r\n\t\t\t\t\t<div class=\"footer_main--column\">\r\n\t\t\t\t\t\t<!-- <strong class=\"footer_main--column_title\">产品中心</strong> -->\r\n\t\t\t\t\t\t<ul class=\"footer_navigation\">\r\n\t\t\t\t\t\t\t<li class=\"footer_navigation--item\"><router-link to=\"/about\" class=\"footer_navigation--link\">国际水晶疗愈协会</router-link></li>\r\n\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"am-u-md-6 \">\r\n\t\t\t\t\t<div class=\"footer_main--column\">\r\n\t\t\t\t\t\t<strong class=\"footer_main--column_title\">关于我们</strong>\r\n\t\t\t\t\t\t<div class=\"footer_about\">\r\n\t\t\t\t\t\t\t<p class=\"footer_about--text\">\r\n\t\t\t\t\t\t\t\t{{ footerabout }}\r\n\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"am-u-md-3 \">\r\n\t\t\t\t\t<div class=\"footer_main--column\">\r\n\t\t\t\t\t\t<strong class=\"footer_main--column_title\">联系详情</strong>\r\n\t\t\t\t\t\t<ul class=\"footer_contact_info\">\r\n\t\t\t\t\t\t\t<li class=\"footer_contact_info--item\"><i class=\"am-icon-phone\"></i><span>服务专线：{{ aboutmobile }}</span></li>\r\n\t\t\t\t\t\t\t<li class=\"footer_contact_info--item\"><i class=\"am-icon-map-marker\"></i><span>{{ aboutaddress }}</span></li>\r\n\t\t\t\t\t\t\t<li class=\"footer_contact_info--item\"><i class=\"am-icon-clock-o\"></i><span>{{ aboutkefutime }}</span></li>\r\n\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tname: \"Footer\",\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\taboutaddress: '',\r\n\t\t\taboutemail: '',\r\n\t\t\taboutmobile: '',\r\n\t\t\tfooterabout: '',\r\n\t\t\taboutkefutime: '',\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.getConfigFooter()\r\n\t},\r\n\tmethods: {\r\n\t\tgetConfigFooter() {\r\n\t\t\tthis.getRequest(\"/cms/config/footer\").then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.aboutaddress = resp.data.aboutaddress;\r\n\t\t\t\t\tthis.aboutemail = resp.data.aboutemail;\r\n\t\t\t\t\tthis.aboutmobile = resp.data.aboutmobile;\r\n\t\t\t\t\tthis.footerabout = resp.data.footerabout;\r\n\t\t\t\t\tthis.aboutkefutime = resp.data.aboutkefutime;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,YAAA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACAD,gBAAA;MACA,KAAAE,UAAA,uBAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAX,YAAA,GAAAU,IAAA,CAAAX,IAAA,CAAAC,YAAA;UACA,KAAAC,UAAA,GAAAS,IAAA,CAAAX,IAAA,CAAAE,UAAA;UACA,KAAAC,WAAA,GAAAQ,IAAA,CAAAX,IAAA,CAAAG,WAAA;UACA,KAAAC,WAAA,GAAAO,IAAA,CAAAX,IAAA,CAAAI,WAAA;UACA,KAAAC,aAAA,GAAAM,IAAA,CAAAX,IAAA,CAAAK,aAAA;QACA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}