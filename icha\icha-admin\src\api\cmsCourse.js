import request from '@/utils/request'

/**
 * 新增课程
 * @param data
 */
export function cmsCourseCreateApi(data) {
    return request({
        url: '/admin/cms/course/save',
        method: 'POST',
        data
    })
}

/**
 * 课程更新
 * @param data
 */
export function cmsCourseUpdateApi(data) {
    return request({
        url: '/admin/cms/course/update',
        method: 'POST',
        data
    })
}

/**
 * 课程详情
 * @param id
 */
export function cmsCourseDetailApi(id) {
    return request({
        url: `/admin/cms/course/info/${id}`,
        method: 'GET'
    })
}

/**
 * 课程删除
 * @param ids 要删除的id数组
 */
export function cmsCourseDeleteApi(ids) {
    return request({
        url: `/admin/cms/course/delete`,
        method: 'POST',
        data: ids
    })
}

/**
 * 课程列表
 * @param params
 */
export function cmsCourseListApi(params) {
    return request({
        url: '/admin/cms/course/list',
        method: 'GET',
        params
    })
} 