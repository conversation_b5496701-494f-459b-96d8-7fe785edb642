{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from '@/components/common/Layout.vue';\nimport Message from '@/utils/message';\nexport default {\n  components: {\n    Layout\n  },\n  name: 'ChakraTestList',\n  data() {\n    return {\n      questionList: [],\n      loading: true,\n      // 脉轮颜色\n      chakraColors: [\"#993734\",\n      // 海底轮\n      \"#be6f2a\",\n      // 脐轮\n      \"#d7c34a\",\n      // 太阳轮\n      \"#5f9057\",\n      // 心轮\n      \"#5b8aa4\",\n      // 喉轮\n      \"#2c3485\",\n      // 眉心轮\n      \"#7e4997\" // 顶轮\n      ],\n\n      chakraNames: [\"海底轮\", \"脐轮\", \"太阳轮\", \"心轮\", \"喉轮\", \"眉心轮\", \"顶轮\"]\n    };\n  },\n  mounted() {\n    this.getList();\n  },\n  methods: {\n    // 获取测试列表\n    getList() {\n      this.loading = true;\n      this.getRequest('/question/list').then(res => {\n        this.loading = false;\n        if (res.code == 200) {\n          this.questionList = res.data || [];\n          this.fetchChakraData();\n        } else {\n          Message.error(res.message || '获取列表失败');\n        }\n      }).catch(err => {\n        this.loading = false;\n        Message.error('获取列表失败');\n        console.error('获取列表失败:', err);\n      });\n    },\n    // 获取脉轮数据\n    fetchChakraData() {\n      this.questionList.forEach((item, idx) => {\n        if (item.status == 1) {\n          this.getRequest('/question/finishDetail', {\n            questionUserId: item.id\n          }).then(res => {\n            if (res.code == 200) {\n              const chakraData = [{\n                name: \"海底轮\",\n                value: res.data.questionUserEntity.root\n              }, {\n                name: \"脐轮\",\n                value: res.data.questionUserEntity.sacral\n              }, {\n                name: \"太阳轮\",\n                value: res.data.questionUserEntity.navel\n              }, {\n                name: \"心轮\",\n                value: res.data.questionUserEntity.heart\n              }, {\n                name: \"喉轮\",\n                value: res.data.questionUserEntity.throat\n              }, {\n                name: \"眉心轮\",\n                value: res.data.questionUserEntity.thirdEye\n              }, {\n                name: \"顶轮\",\n                value: res.data.questionUserEntity.crown\n              }];\n              this.$set(this.questionList[idx], 'chakraData', chakraData);\n            }\n          }).catch(error => {\n            console.log('获取脉轮数据失败', error);\n          });\n        }\n      });\n    },\n    // 获取状态样式类\n    getStatusClass(status) {\n      return status == 0 ? 'status-pending' : 'status-submitted';\n    },\n    // 卡片点击\n    handleCardClick(item) {\n      this.handleDetailClick(item);\n    },\n    // 详情按钮点击\n    handleDetailClick(item) {\n      if (item.status == 0) {\n        // 继续测试\n        this.getRequest('/question/startExam', {\n          questionUserId: item.id\n        }).then(res => {\n          if (res.code == 200) {\n            this.$router.push({\n              path: '/chakra-test/start',\n              query: {\n                questionUserId: res.data.questionUserId,\n                token: res.data.token\n              }\n            });\n          } else {\n            Message.error(res.message || '开始测试失败');\n          }\n        }).catch(err => {\n          Message.error('开始测试失败');\n          console.error('开始测试失败:', err);\n        });\n      } else {\n        // 查看详情\n        this.$router.push({\n          path: '/chakra-test/detail',\n          query: {\n            questionUserId: item.id\n          }\n        });\n      }\n    },\n    // 删除按钮点击\n    handleDeleteClick(id) {\n      Message.confirm('提示', '确认删除这条测试记录吗？', () => {\n        this.getRequest('/question/questionUserDelete', {\n          questionUserId: id\n        }).then(res => {\n          if (res.code == 200) {\n            Message.success('删除成功');\n            this.getList();\n          } else {\n            Message.error(res.message || '删除失败');\n          }\n        }).catch(err => {\n          Message.error('删除失败');\n          console.error('删除失败:', err);\n        });\n      }, () => {\n        // 取消删除\n      });\n    },\n    // 创建新测试\n    createNewTest() {\n      this.$router.push('/chakra-test');\n    },\n    // 返回上一页\n    goBack() {\n      this.$router.back();\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "Message", "components", "name", "data", "questionList", "loading", "chakraColors", "chakra<PERSON>ames", "mounted", "getList", "methods", "getRequest", "then", "res", "code", "fetchChakraData", "error", "message", "catch", "err", "console", "for<PERSON>ach", "item", "idx", "status", "questionUserId", "id", "chakra<PERSON>ata", "value", "questionUserEntity", "root", "sacral", "navel", "heart", "throat", "third<PERSON><PERSON>", "crown", "$set", "log", "getStatusClass", "handleCardClick", "handleDetailClick", "$router", "push", "path", "query", "token", "handleDeleteClick", "confirm", "success", "createNewTest", "goBack", "back"], "sources": ["src/views/ChakraTestList.vue"], "sourcesContent": ["<template>\r\n  <Layout>\r\n    <div class=\"chakra-list-page\">\r\n    <!-- 标题栏 -->\r\n    <div class=\"nav-title\">\r\n      <div class=\"nav-left\">\r\n        <van-button \r\n          class=\"back-button\"\r\n          @click=\"goBack\"\r\n          plain\r\n        >\r\n          <van-icon name=\"arrow-left\" size=\"18\" />\r\n        </van-button>\r\n        <div class=\"color-bar\"></div>\r\n        <div class=\"title-text\">我的脉轮测试</div>\r\n      </div>\r\n      <div class=\"nav-right\">\r\n        <van-button \r\n          class=\"new-test-button\"\r\n          @click=\"createNewTest\"\r\n          size=\"small\"\r\n          type=\"primary\"\r\n        >\r\n          <van-icon name=\"plus\" size=\"14\" />\r\n          <span>新测试</span>\r\n        </van-button>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 测试列表 -->\r\n    <div class=\"list-container\" v-if=\"questionList.length > 0\">\r\n      <div \r\n        v-for=\"item in questionList\" \r\n        :key=\"item.id\"\r\n        class=\"test-card\"\r\n        @click=\"handleCardClick(item)\"\r\n      >\r\n        <!-- 卡片头部 -->\r\n        <div class=\"card-header\">\r\n          <div class=\"date-info\">\r\n            <van-icon name=\"calendar-o\" size=\"16\" color=\"#666\" />\r\n            <span class=\"date-text\">{{ item.addTime }}</span>\r\n          </div>\r\n          <div class=\"status-tag\" :class=\"getStatusClass(item.status)\">\r\n            {{ item.status == 0 ? '未提交' : '已提交' }}\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 卡片内容 -->\r\n        <div class=\"card-content\">\r\n          <!-- 已提交的测试显示脉轮预览 -->\r\n          <div v-if=\"item.status == 1\" class=\"chakra-preview\">\r\n            <div class=\"chakra-indicators\">\r\n              <div \r\n                v-for=\"(color, index) in chakraColors\" \r\n                :key=\"index\"\r\n                class=\"chakra-item\"\r\n              >\r\n                <div class=\"chakra-dot\" :style=\"{ backgroundColor: color }\"></div>\r\n                <div class=\"chakra-value\">\r\n                  {{ item.chakraData && item.chakraData[index] ? item.chakraData[index].value : '-' }}\r\n                </div>\r\n                <div class=\"chakra-name\">{{ chakraNames[index] }}</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"preview-hint\">点击查看详细结果</div>\r\n          </div>\r\n          \r\n          <!-- 未提交的测试显示继续测试提示 -->\r\n          <div v-else class=\"test-preview\">\r\n            <div class=\"preview-icon\">\r\n              <van-icon name=\"edit\" size=\"24\" color=\"#c9ab79\" />\r\n            </div>\r\n            <div class=\"preview-text\">继续进行脉轮测试</div>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 卡片底部 -->\r\n        <div class=\"card-footer\">\r\n          <van-button \r\n            size=\"small\"\r\n            class=\"detail-btn\"\r\n            @click.stop=\"handleDetailClick(item)\"\r\n          >\r\n            <van-icon name=\"eye-o\" size=\"14\" />\r\n            <span>{{ item.status == 0 ? '继续测试' : '查看详情' }}</span>\r\n          </van-button>\r\n          <van-button \r\n            size=\"small\"\r\n            class=\"delete-btn\"\r\n            @click.stop=\"handleDeleteClick(item.id)\"\r\n          >\r\n            <van-icon name=\"delete-o\" size=\"14\" />\r\n            <span>删除</span>\r\n          </van-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 空状态 -->\r\n    <div v-else-if=\"!loading\" class=\"empty-state\">\r\n      <van-empty \r\n        image=\"search\" \r\n        description=\"暂无脉轮测试记录\"\r\n      >\r\n        <van-button \r\n          type=\"primary\" \r\n          class=\"create-btn\"\r\n          @click=\"createNewTest\"\r\n        >\r\n          <van-icon name=\"plus\" size=\"14\" />\r\n          <span>创建新测试</span>\r\n        </van-button>\r\n      </van-empty>\r\n    </div>\r\n    \r\n    <!-- 加载状态 -->\r\n    <van-loading v-if=\"loading\" class=\"page-loading\" />\r\n    </div>\r\n  </Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from '@/components/common/Layout.vue'\r\nimport Message from '@/utils/message'\r\n\r\nexport default {\r\n  components: {\r\n    Layout\r\n  },\r\n  name: 'ChakraTestList',\r\n  data() {\r\n    return {\r\n      questionList: [],\r\n      loading: true,\r\n      // 脉轮颜色\r\n      chakraColors: [\r\n        \"#993734\", // 海底轮\r\n        \"#be6f2a\", // 脐轮\r\n        \"#d7c34a\", // 太阳轮\r\n        \"#5f9057\", // 心轮\r\n        \"#5b8aa4\", // 喉轮\r\n        \"#2c3485\", // 眉心轮\r\n        \"#7e4997\"  // 顶轮\r\n      ],\r\n      chakraNames: [\r\n        \"海底轮\", \r\n        \"脐轮\", \r\n        \"太阳轮\", \r\n        \"心轮\", \r\n        \"喉轮\", \r\n        \"眉心轮\", \r\n        \"顶轮\"\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 获取测试列表\r\n    getList() {\r\n      this.loading = true\r\n      this.getRequest('/question/list').then(res => {\r\n        this.loading = false\r\n        if (res.code == 200) {\r\n          this.questionList = res.data || []\r\n          this.fetchChakraData()\r\n        } else {\r\n          Message.error(res.message || '获取列表失败')\r\n        }\r\n      }).catch(err => {\r\n        this.loading = false\r\n        Message.error('获取列表失败')\r\n        console.error('获取列表失败:', err)\r\n      })\r\n    },\r\n    \r\n    // 获取脉轮数据\r\n    fetchChakraData() {\r\n      this.questionList.forEach((item, idx) => {\r\n        if (item.status == 1) {\r\n          this.getRequest('/question/finishDetail', { questionUserId: item.id }).then(res => {\r\n            if (res.code == 200) {\r\n              const chakraData = [\r\n                { name: \"海底轮\", value: res.data.questionUserEntity.root },\r\n                { name: \"脐轮\", value: res.data.questionUserEntity.sacral },\r\n                { name: \"太阳轮\", value: res.data.questionUserEntity.navel },\r\n                { name: \"心轮\", value: res.data.questionUserEntity.heart },\r\n                { name: \"喉轮\", value: res.data.questionUserEntity.throat },\r\n                { name: \"眉心轮\", value: res.data.questionUserEntity.thirdEye },\r\n                { name: \"顶轮\", value: res.data.questionUserEntity.crown },\r\n              ]\r\n              this.$set(this.questionList[idx], 'chakraData', chakraData)\r\n            }\r\n          }).catch(error => {\r\n            console.log('获取脉轮数据失败', error)\r\n          })\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 获取状态样式类\r\n    getStatusClass(status) {\r\n      return status == 0 ? 'status-pending' : 'status-submitted'\r\n    },\r\n    \r\n    // 卡片点击\r\n    handleCardClick(item) {\r\n      this.handleDetailClick(item)\r\n    },\r\n    \r\n    // 详情按钮点击\r\n    handleDetailClick(item) {\r\n      if (item.status == 0) {\r\n        // 继续测试\r\n        this.getRequest('/question/startExam', { questionUserId: item.id }).then(res => {\r\n          if (res.code == 200) {\r\n            this.$router.push({\r\n              path: '/chakra-test/start',\r\n              query: {\r\n                questionUserId: res.data.questionUserId,\r\n                token: res.data.token\r\n              }\r\n            })\r\n          } else {\r\n            Message.error(res.message || '开始测试失败')\r\n          }\r\n        }).catch(err => {\r\n          Message.error('开始测试失败')\r\n          console.error('开始测试失败:', err)\r\n        })\r\n      } else {\r\n        // 查看详情\r\n        this.$router.push({\r\n          path: '/chakra-test/detail',\r\n          query: { questionUserId: item.id }\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 删除按钮点击\r\n    handleDeleteClick(id) {\r\n      Message.confirm('提示', '确认删除这条测试记录吗？', () => {\r\n        this.getRequest('/question/questionUserDelete', { questionUserId: id }).then(res => {\r\n          if (res.code == 200) {\r\n            Message.success('删除成功')\r\n            this.getList()\r\n          } else {\r\n            Message.error(res.message || '删除失败')\r\n          }\r\n        }).catch(err => {\r\n          Message.error('删除失败')\r\n          console.error('删除失败:', err)\r\n        })\r\n      }, () => {\r\n        // 取消删除\r\n      })\r\n    },\r\n    \r\n    // 创建新测试\r\n    createNewTest() {\r\n      this.$router.push('/chakra-test')\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      this.$router.back()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.chakra-list-page {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n  width: 100%;\r\n}\r\n\r\n.nav-title {\r\n  padding: 15px 25px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  height: 60px;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n  box-shadow: 0 4px 15px rgba(86, 70, 128, 0.1);\r\n  margin-bottom: 15px;\r\n  border-radius: 0 0 20px 20px;\r\n}\r\n\r\n.nav-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.nav-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-button {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background: rgba(86, 70, 128, 0.08);\r\n  border: 1px solid rgba(86, 70, 128, 0.2);\r\n  color: #564680;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.back-button:hover {\r\n  background: rgba(86, 70, 128, 0.15);\r\n  border-color: rgba(86, 70, 128, 0.4);\r\n  transform: translateX(-2px);\r\n}\r\n\r\n.color-bar {\r\n  width: 6px;\r\n  height: 25px;\r\n  background: linear-gradient(135deg, #564680, #516790, #c9ab79);\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(86, 70, 128, 0.3);\r\n}\r\n\r\n.title-text {\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.new-test-button {\r\n  background: linear-gradient(135deg, #564680, #516790);\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 8px 16px;\r\n  color: white;\r\n  font-weight: 600;\r\n  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.new-test-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);\r\n}\r\n\r\n.new-test-button span {\r\n  margin-left: 4px;\r\n}\r\n\r\n.list-container {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.test-card {\r\n  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 25px rgba(86, 70, 128, 0.08);\r\n  overflow: hidden;\r\n  margin-bottom: 20px;\r\n  transition: all 0.4s ease;\r\n  border: 2px solid transparent;\r\n  position: relative;\r\n  \r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 3px;\r\n    background: linear-gradient(90deg, #564680, #516790, #c9ab79);\r\n  }\r\n  \r\n  &:active {\r\n    transform: scale(0.98);\r\n  }\r\n  \r\n  &:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 15px 35px rgba(86, 70, 128, 0.12);\r\n    border-color: rgba(86, 70, 128, 0.2);\r\n  }\r\n}\r\n\r\n.card-header {\r\n  padding: 18px;\r\n  border-bottom: 2px solid rgba(86, 70, 128, 0.08);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);\r\n}\r\n\r\n.date-info {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #666;\r\n  font-size: 14px;\r\n  \r\n  .date-text {\r\n    margin-left: 4px;\r\n  }\r\n}\r\n\r\n.status-tag {\r\n  padding: 6px 16px;\r\n  border-radius: 20px;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  letter-spacing: 0.3px;\r\n  \r\n  &.status-pending {\r\n    background: linear-gradient(135deg, rgba(255, 152, 0, 0.15), rgba(255, 152, 0, 0.08));\r\n    color: #ff9800;\r\n    border: 2px solid rgba(255, 152, 0, 0.3);\r\n  }\r\n  \r\n  &.status-submitted {\r\n    background: linear-gradient(135deg, rgba(76, 175, 80, 0.15), rgba(76, 175, 80, 0.08));\r\n    color: #4caf50;\r\n    border: 2px solid rgba(76, 175, 80, 0.3);\r\n  }\r\n}\r\n\r\n.card-content {\r\n  padding: 15px 12px;\r\n  min-height: 75px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.chakra-preview {\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n\r\n.chakra-indicators {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 5px;\r\n  margin-bottom: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.chakra-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  width: 40px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.chakra-dot {\r\n  width: 14px;\r\n  height: 14px;\r\n  border-radius: 50%;\r\n  margin-bottom: 5px;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.chakra-indicators:hover .chakra-dot {\r\n  transform: scale(1.2);\r\n}\r\n\r\n.chakra-value {\r\n  font-size: 11px;\r\n  font-weight: 500;\r\n  color: #333;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.chakra-name {\r\n  font-size: 10px;\r\n  color: #666;\r\n  white-space: nowrap;\r\n}\r\n\r\n.preview-hint {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.test-preview {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.preview-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background-color: rgba(201, 171, 121, 0.1);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.preview-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.card-footer {\r\n  padding: 12px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  background-color: rgba(0, 0, 0, 0.02);\r\n}\r\n\r\n.detail-btn {\r\n  background: linear-gradient(135deg, #564680, #516790);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 8px 16px;\r\n  font-weight: 600;\r\n  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);\r\n  }\r\n  \r\n  span {\r\n    margin-left: 4px;\r\n  }\r\n}\r\n\r\n.delete-btn {\r\n  background: linear-gradient(135deg, #dd5c5f, #c85458);\r\n  color: #fff;\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 8px 16px;\r\n  font-weight: 600;\r\n  box-shadow: 0 3px 10px rgba(221, 92, 95, 0.3);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(221, 92, 95, 0.4);\r\n  }\r\n  \r\n  span {\r\n    margin-left: 4px;\r\n  }\r\n}\r\n\r\n.create-btn {\r\n  background: linear-gradient(135deg, #564680, #516790);\r\n  border: none;\r\n  margin-top: 20px;\r\n  border-radius: 25px;\r\n  padding: 12px 30px;\r\n  font-weight: 700;\r\n  box-shadow: 0 6px 20px rgba(86, 70, 128, 0.3);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:hover {\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 8px 25px rgba(86, 70, 128, 0.4);\r\n  }\r\n  \r\n  span {\r\n    margin-left: 6px;\r\n  }\r\n}\r\n\r\n.empty-state {\r\n  padding: 80px 30px;\r\n}\r\n\r\n.page-loading {\r\n  position: fixed;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* 移动端适配 */\r\n@media (min-width: 1200px) {\r\n  /* PC端大屏幕优化 */\r\n  .nav-title {\r\n    max-width: 1200px;\r\n    margin: 0 auto 20px;\r\n    border-radius: 0 0 25px 25px;\r\n    padding: 20px 40px;\r\n    height: 70px;\r\n  }\r\n  \r\n  .title-text {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 45px;\r\n    height: 45px;\r\n  }\r\n  \r\n  .new-test-button {\r\n    padding: 10px 20px;\r\n    font-size: 15px;\r\n  }\r\n  \r\n  .list-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 0 40px 20px;\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 25px;\r\n  }\r\n  \r\n  .test-card {\r\n    margin-bottom: 0;\r\n    min-height: 200px;\r\n  }\r\n  \r\n  .card-content {\r\n    min-height: 100px;\r\n  }\r\n  \r\n  .chakra-indicators {\r\n    justify-content: center;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .chakra-item {\r\n    width: 45px;\r\n  }\r\n  \r\n  .chakra-value {\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n  }\r\n  \r\n  .chakra-name {\r\n    font-size: 11px;\r\n  }\r\n  \r\n  .empty-state {\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n    padding: 120px 40px;\r\n  }\r\n}\r\n\r\n@media (min-width: 992px) and (max-width: 1199px) {\r\n  /* PC端中屏幕优化 */\r\n  .nav-title {\r\n    max-width: 960px;\r\n    margin: 0 auto 20px;\r\n    padding: 18px 30px;\r\n    height: 65px;\r\n  }\r\n  \r\n  .title-text {\r\n    font-size: 22px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 42px;\r\n    height: 42px;\r\n  }\r\n  \r\n  .list-container {\r\n    max-width: 960px;\r\n    margin: 0 auto;\r\n    padding: 0 30px 20px;\r\n    display: grid;\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 20px;\r\n  }\r\n  \r\n  .test-card {\r\n    margin-bottom: 0;\r\n  }\r\n  \r\n  .chakra-indicators {\r\n    gap: 6px;\r\n  }\r\n  \r\n  .chakra-item {\r\n    width: 42px;\r\n  }\r\n  \r\n  .empty-state {\r\n    max-width: 550px;\r\n    margin: 0 auto;\r\n    padding: 100px 30px;\r\n  }\r\n}\r\n\r\n@media (min-width: 769px) and (max-width: 991px) {\r\n  /* 平板横屏优化 */\r\n  .nav-title {\r\n    max-width: 750px;\r\n    margin: 0 auto 20px;\r\n    padding: 16px 25px;\r\n    height: 60px;\r\n  }\r\n  \r\n  .title-text {\r\n    font-size: 20px;\r\n  }\r\n  \r\n  .list-container {\r\n    max-width: 750px;\r\n    margin: 0 auto;\r\n    padding: 0 25px 20px;\r\n  }\r\n  \r\n  .test-card {\r\n    max-width: 700px;\r\n    margin: 0 auto 20px;\r\n  }\r\n  \r\n  .chakra-indicators {\r\n    gap: 8px;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .chakra-item {\r\n    width: 50px;\r\n  }\r\n  \r\n  .chakra-value {\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .chakra-name {\r\n    font-size: 11px;\r\n  }\r\n  \r\n  .empty-state {\r\n    max-width: 500px;\r\n    margin: 0 auto;\r\n    padding: 90px 25px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .nav-title {\r\n    padding: 12px 20px;\r\n    height: 55px;\r\n  }\r\n  \r\n  .nav-left {\r\n    gap: 8px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n  \r\n  .title-text {\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .new-test-button {\r\n    padding: 6px 12px;\r\n    font-size: 13px;\r\n  }\r\n  \r\n  .test-card {\r\n    margin-bottom: 16px;\r\n    border-radius: 12px;\r\n  }\r\n  \r\n  .card-header {\r\n    padding: 14px;\r\n  }\r\n  \r\n  .card-content {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .card-footer {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .detail-btn,\r\n  .delete-btn {\r\n    padding: 6px 12px;\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .nav-title {\r\n    padding: 10px 15px;\r\n    height: 50px;\r\n  }\r\n  \r\n  .nav-left {\r\n    gap: 6px;\r\n  }\r\n  \r\n  .back-button {\r\n    width: 32px;\r\n    height: 32px;\r\n  }\r\n  \r\n  .color-bar {\r\n    width: 4px;\r\n    height: 20px;\r\n  }\r\n  \r\n  .title-text {\r\n    font-size: 16px;\r\n  }\r\n  \r\n  .new-test-button {\r\n    padding: 5px 10px;\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .list-container {\r\n    padding: 0 15px 20px;\r\n  }\r\n  \r\n  .date-info {\r\n    font-size: 13px;\r\n  }\r\n  \r\n  .status-tag {\r\n    padding: 4px 12px;\r\n    font-size: 11px;\r\n  }\r\n  \r\n  .chakra-indicators {\r\n    gap: 3px;\r\n  }\r\n  \r\n  .chakra-item {\r\n    width: 32px;\r\n  }\r\n  \r\n  .chakra-value {\r\n    font-size: 10px;\r\n  }\r\n  \r\n  .chakra-name {\r\n    font-size: 9px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HA,OAAAA,MAAA;AACA,OAAAC,OAAA;AAEA;EACAC,UAAA;IACAF;EACA;EACAG,IAAA;EACAC,KAAA;IACA;MACAC,YAAA;MACAC,OAAA;MACA;MACAC,YAAA,GACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;;MACAC,WAAA,GACA,OACA,MACA,OACA,MACA,MACA,OACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAD,QAAA;MACA,KAAAJ,OAAA;MACA,KAAAM,UAAA,mBAAAC,IAAA,CAAAC,GAAA;QACA,KAAAR,OAAA;QACA,IAAAQ,GAAA,CAAAC,IAAA;UACA,KAAAV,YAAA,GAAAS,GAAA,CAAAV,IAAA;UACA,KAAAY,eAAA;QACA;UACAf,OAAA,CAAAgB,KAAA,CAAAH,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,CAAAC,GAAA;QACA,KAAAd,OAAA;QACAL,OAAA,CAAAgB,KAAA;QACAI,OAAA,CAAAJ,KAAA,YAAAG,GAAA;MACA;IACA;IAEA;IACAJ,gBAAA;MACA,KAAAX,YAAA,CAAAiB,OAAA,EAAAC,IAAA,EAAAC,GAAA;QACA,IAAAD,IAAA,CAAAE,MAAA;UACA,KAAAb,UAAA;YAAAc,cAAA,EAAAH,IAAA,CAAAI;UAAA,GAAAd,IAAA,CAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA,MAAAa,UAAA,IACA;gBAAAzB,IAAA;gBAAA0B,KAAA,EAAAf,GAAA,CAAAV,IAAA,CAAA0B,kBAAA,CAAAC;cAAA,GACA;gBAAA5B,IAAA;gBAAA0B,KAAA,EAAAf,GAAA,CAAAV,IAAA,CAAA0B,kBAAA,CAAAE;cAAA,GACA;gBAAA7B,IAAA;gBAAA0B,KAAA,EAAAf,GAAA,CAAAV,IAAA,CAAA0B,kBAAA,CAAAG;cAAA,GACA;gBAAA9B,IAAA;gBAAA0B,KAAA,EAAAf,GAAA,CAAAV,IAAA,CAAA0B,kBAAA,CAAAI;cAAA,GACA;gBAAA/B,IAAA;gBAAA0B,KAAA,EAAAf,GAAA,CAAAV,IAAA,CAAA0B,kBAAA,CAAAK;cAAA,GACA;gBAAAhC,IAAA;gBAAA0B,KAAA,EAAAf,GAAA,CAAAV,IAAA,CAAA0B,kBAAA,CAAAM;cAAA,GACA;gBAAAjC,IAAA;gBAAA0B,KAAA,EAAAf,GAAA,CAAAV,IAAA,CAAA0B,kBAAA,CAAAO;cAAA,EACA;cACA,KAAAC,IAAA,MAAAjC,YAAA,CAAAmB,GAAA,iBAAAI,UAAA;YACA;UACA,GAAAT,KAAA,CAAAF,KAAA;YACAI,OAAA,CAAAkB,GAAA,aAAAtB,KAAA;UACA;QACA;MACA;IACA;IAEA;IACAuB,eAAAf,MAAA;MACA,OAAAA,MAAA;IACA;IAEA;IACAgB,gBAAAlB,IAAA;MACA,KAAAmB,iBAAA,CAAAnB,IAAA;IACA;IAEA;IACAmB,kBAAAnB,IAAA;MACA,IAAAA,IAAA,CAAAE,MAAA;QACA;QACA,KAAAb,UAAA;UAAAc,cAAA,EAAAH,IAAA,CAAAI;QAAA,GAAAd,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAA4B,OAAA,CAAAC,IAAA;cACAC,IAAA;cACAC,KAAA;gBACApB,cAAA,EAAAZ,GAAA,CAAAV,IAAA,CAAAsB,cAAA;gBACAqB,KAAA,EAAAjC,GAAA,CAAAV,IAAA,CAAA2C;cACA;YACA;UACA;YACA9C,OAAA,CAAAgB,KAAA,CAAAH,GAAA,CAAAI,OAAA;UACA;QACA,GAAAC,KAAA,CAAAC,GAAA;UACAnB,OAAA,CAAAgB,KAAA;UACAI,OAAA,CAAAJ,KAAA,YAAAG,GAAA;QACA;MACA;QACA;QACA,KAAAuB,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA;YAAApB,cAAA,EAAAH,IAAA,CAAAI;UAAA;QACA;MACA;IACA;IAEA;IACAqB,kBAAArB,EAAA;MACA1B,OAAA,CAAAgD,OAAA;QACA,KAAArC,UAAA;UAAAc,cAAA,EAAAC;QAAA,GAAAd,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACAd,OAAA,CAAAiD,OAAA;YACA,KAAAxC,OAAA;UACA;YACAT,OAAA,CAAAgB,KAAA,CAAAH,GAAA,CAAAI,OAAA;UACA;QACA,GAAAC,KAAA,CAAAC,GAAA;UACAnB,OAAA,CAAAgB,KAAA;UACAI,OAAA,CAAAJ,KAAA,UAAAG,GAAA;QACA;MACA;QACA;MAAA,CACA;IACA;IAEA;IACA+B,cAAA;MACA,KAAAR,OAAA,CAAAC,IAAA;IACA;IAEA;IACAQ,OAAA;MACA,KAAAT,OAAA,CAAAU,IAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}