{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from '@/components/common/Layout.vue';\nimport { isMobilePhone } from '@/utils/index';\nimport Message from '@/utils/message';\nexport default {\n  components: {\n    Layout\n  },\n  name: 'ChakraTest',\n  data() {\n    return {\n      mailunshouye: '',\n      loading: false,\n      isMobilePhone: isMobilePhone()\n    };\n  },\n  mounted() {\n    this.getConfig();\n  },\n  methods: {\n    // 获取配置信息\n    getConfig() {\n      this.getRequest('/mailunConfig').then(res => {\n        if (res.code == 200) {\n          this.mailunshouye = res.data.mailunshouye;\n        }\n      }).catch(err => {\n        console.error('获取配置失败:', err);\n      });\n    },\n    // 开始测试\n    startTest() {\n      this.loading = true;\n      this.getRequest('/question/startExam').then(res => {\n        this.loading = false;\n        if (res.code == 200) {\n          // 跳转到答题页面\n          this.$router.push({\n            path: '/chakra-test/start',\n            query: {\n              questionUserId: res.data.questionUserId,\n              token: res.data.token\n            }\n          });\n        } else {\n          Message.error(res.message || '开始测试失败');\n        }\n      }).catch(err => {\n        this.loading = false;\n        Message.error('开始测试失败');\n        console.error('开始测试失败:', err);\n      });\n    },\n    // 跳转到测试列表\n    goToList() {\n      this.$router.push('/chakra-test/list');\n    },\n    // 跳转到脉轮简介\n    goToIntro() {\n      this.$router.push('/chakra-test/intro');\n    },\n    // 跳转到平衡脉轮\n    goToBalance() {\n      this.$router.push('/chakra-test/balance');\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "Message", "components", "name", "data", "mailunshouye", "loading", "mounted", "getConfig", "methods", "getRequest", "then", "res", "code", "catch", "err", "console", "error", "startTest", "$router", "push", "path", "query", "questionUserId", "token", "message", "goToList", "goToIntro", "goToBalance"], "sources": ["src/views/ChakraTest.vue"], "sourcesContent": ["<template>\r\n  <Layout>\r\n    <div class=\"chakra-test-page\">\r\n      <!-- 页面头部 -->\r\n      <div class=\"hero-header-section chakra-header\">\r\n        <div class=\"hero-content\">\r\n          <h1 class=\"hero-title\">\r\n            <van-icon name=\"fire-o\" size=\"40\" color=\"rgba(255,255,255,0.9)\" class=\"rotating-icon\" /> 脉轮测试\r\n          </h1>\r\n          <p class=\"hero-subtitle\">探索您的能量中心，开启身心灵平衡之旅</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"content-wrapper\">\r\n        <!-- 脉轮介绍部分 -->\r\n        <section class=\"chakra-intro-section\">\r\n          <div class=\"intro-content\">\r\n            <div class=\"intro-text\">\r\n              <h2 class=\"section-title\">什么是脉轮测试？</h2>\r\n              <p>脉轮是人体能量系统的重要组成部分，通过专业的脉轮测试，您可以了解自己七个主要脉轮的能量状态。</p>\r\n              <p>在开始测试之前，请找一个安静不受干扰的地方，让自己放松，放下任何情绪，以获得最准确的测试结果。</p>\r\n\r\n              <div class=\"test-features\">\r\n                <div class=\"feature-item\">\r\n                  <van-icon name=\"success\" size=\"20\" color=\"#c9ab79\" />\r\n                  <span>专业测试问卷</span>\r\n                </div>\r\n                <div class=\"feature-item\">\r\n                  <van-icon name=\"bar-chart-o\" size=\"20\" color=\"#c9ab79\" />\r\n                  <span>详细结果分析</span>\r\n                </div>\r\n                <div class=\"feature-item\">\r\n                  <van-icon name=\"like-o\" size=\"20\" color=\"#c9ab79\" />\r\n                  <span>个性化建议</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"intro-visual\">\r\n              <div class=\"chakra-circles-container\">\r\n                <img\r\n                  v-if=\"mailunshouye\"\r\n                  :src=\"mailunshouye\"\r\n                  alt=\"脉轮测试\"\r\n                  class=\"chakra-main-image\"\r\n                />\r\n                <div v-else class=\"chakra-circles\">\r\n                  <div class=\"chakra-circle\" style=\"background: #993734;\" title=\"海底轮\"></div>\r\n                  <div class=\"chakra-circle\" style=\"background: #be6f2a;\" title=\"脐轮\"></div>\r\n                  <div class=\"chakra-circle\" style=\"background: #d7c34a;\" title=\"太阳轮\"></div>\r\n                  <div class=\"chakra-circle\" style=\"background: #5f9057;\" title=\"心轮\"></div>\r\n                  <div class=\"chakra-circle\" style=\"background: #5b8aa4;\" title=\"喉轮\"></div>\r\n                  <div class=\"chakra-circle\" style=\"background: #2c3485;\" title=\"眉心轮\"></div>\r\n                  <div class=\"chakra-circle\" style=\"background: #7e4997;\" title=\"顶轮\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        <!-- 开始测试部分 -->\r\n        <section class=\"start-test-section\">\r\n          <div class=\"start-test-content\">\r\n            <h3>准备好开始您的脉轮之旅了吗？</h3>\r\n            <p>点击下方按钮开始专业的脉轮能量测试</p>\r\n            <button\r\n              class=\"crystal-btn-large start-test-btn\"\r\n              @click=\"startTest\"\r\n              :disabled=\"loading\"\r\n            >\r\n              <van-icon name=\"play-circle-o\" size=\"20\" color=\"white\" />\r\n              {{ loading ? '正在准备测试...' : '开始我的脉轮测试' }}\r\n            </button>\r\n          </div>\r\n        </section>\r\n\r\n        <!-- 快捷导航部分 -->\r\n        <section class=\"quick-nav-section\">\r\n          <h3 class=\"section-title\">探索更多</h3>\r\n          <div class=\"nav-cards\">\r\n            <div class=\"nav-card\" @click=\"goToList\">\r\n              <div class=\"nav-card-icon\">\r\n                <van-icon name=\"notes-o\" size=\"30\" />\r\n              </div>\r\n              <h4>我的测试</h4>\r\n              <p>查看历史测试记录和结果</p>\r\n            </div>\r\n\r\n            <div class=\"nav-card\" @click=\"goToIntro\">\r\n              <div class=\"nav-card-icon\">\r\n                <van-icon name=\"info-o\" size=\"30\" />\r\n              </div>\r\n              <h4>脉轮简介</h4>\r\n              <p>了解七个脉轮的详细知识</p>\r\n            </div>\r\n\r\n            <div class=\"nav-card\" @click=\"goToBalance\">\r\n              <div class=\"nav-card-icon\">\r\n                <van-icon name=\"balance-list-o\" size=\"30\" />\r\n              </div>\r\n              <h4>平衡脉轮</h4>\r\n              <p>学习脉轮平衡的方法和技巧</p>\r\n            </div>\r\n          </div>\r\n        </section>\r\n      </div>\r\n    </div>\r\n  </Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from '@/components/common/Layout.vue'\r\nimport { isMobilePhone } from '@/utils/index'\r\nimport Message from '@/utils/message'\r\n\r\nexport default {\r\n  components: {\r\n    Layout\r\n  },\r\n  name: 'ChakraTest',\r\n  data() {\r\n    return {\r\n      mailunshouye: '',\r\n      loading: false,\r\n      isMobilePhone: isMobilePhone()\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getConfig()\r\n  },\r\n  methods: {\r\n    // 获取配置信息\r\n    getConfig() {\r\n      this.getRequest('/mailunConfig').then(res => {\r\n        if (res.code == 200) {\r\n          this.mailunshouye = res.data.mailunshouye\r\n        }\r\n      }).catch(err => {\r\n        console.error('获取配置失败:', err)\r\n      })\r\n    },\r\n\r\n    // 开始测试\r\n    startTest() {\r\n      this.loading = true\r\n      this.getRequest('/question/startExam').then(res => {\r\n        this.loading = false\r\n        if (res.code == 200) {\r\n          // 跳转到答题页面\r\n          this.$router.push({\r\n            path: '/chakra-test/start',\r\n            query: {\r\n              questionUserId: res.data.questionUserId,\r\n              token: res.data.token\r\n            }\r\n          })\r\n        } else {\r\n          Message.error(res.message || '开始测试失败')\r\n        }\r\n      }).catch(err => {\r\n        this.loading = false\r\n        Message.error('开始测试失败')\r\n        console.error('开始测试失败:', err)\r\n      })\r\n    },\r\n    \r\n    // 跳转到测试列表\r\n    goToList() {\r\n      this.$router.push('/chakra-test/list')\r\n    },\r\n    \r\n    // 跳转到脉轮简介\r\n    goToIntro() {\r\n      this.$router.push('/chakra-test/intro')\r\n    },\r\n    \r\n    // 跳转到平衡脉轮\r\n    goToBalance() {\r\n      this.$router.push('/chakra-test/balance')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.chakra-test-page {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n}\r\n\r\n/* 页面头部样式 */\r\n.hero-header-section {\r\n  background: linear-gradient(135deg, #564680 0%, #516790 50%, #c9ab79 100%);\r\n  color: white;\r\n  text-align: center;\r\n  padding: 100px 20px;\r\n  position: relative;\r\n  overflow: hidden;\r\n  border-radius: 0 0 50px 50px;\r\n}\r\n\r\n.chakra-header {\r\n  background: linear-gradient(135deg, #564680 0%, #516790 50%, #c9ab79 100%);\r\n  box-shadow: 0 10px 30px rgba(86, 70, 128, 0.3);\r\n}\r\n\r\n.hero-header-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"3\" fill=\"rgba(255,255,255,0.15)\"/><circle cx=\"80\" cy=\"80\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"40\" cy=\"60\" r=\"1.5\" fill=\"rgba(255,255,255,0.12)\"/><circle cx=\"60\" cy=\"30\" r=\"2.5\" fill=\"rgba(255,255,255,0.08)\"/></svg>');\r\n  animation: float 30s infinite linear;\r\n}\r\n\r\n@keyframes float {\r\n  0% { transform: translateY(0px); }\r\n  100% { transform: translateY(-100px); }\r\n}\r\n\r\n.hero-content {\r\n  position: relative;\r\n  z-index: 1;\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.hero-title {\r\n  font-size: 3.5rem;\r\n  font-weight: 800;\r\n  margin-bottom: 25px;\r\n  text-shadow: 3px 3px 6px rgba(0,0,0,0.4);\r\n  letter-spacing: 2px;\r\n  background: linear-gradient(45deg, #ffffff, #f0f0f0);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.hero-title .van-icon {\r\n  margin-right: 15px;\r\n  animation: rotate 2s linear infinite;\r\n}\r\n\r\n.rotating-icon {\r\n  display: inline-block;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n.hero-title i {\r\n  margin-right: 15px;\r\n  color: rgba(255,255,255,0.9);\r\n}\r\n\r\n.hero-subtitle {\r\n  font-size: 1.2rem;\r\n  opacity: 0.9;\r\n  margin: 0;\r\n  font-weight: 300;\r\n}\r\n\r\n/* 内容包装器 */\r\n.content-wrapper {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n/* 脉轮介绍部分 */\r\n.chakra-intro-section {\r\n  padding: 100px 0;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n  position: relative;\r\n}\r\n\r\n.chakra-intro-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -50px;\r\n  left: 0;\r\n  right: 0;\r\n  height: 100px;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\r\n  transform: skewY(-2deg);\r\n  z-index: 1;\r\n}\r\n\r\n.intro-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 80px;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.intro-text {\r\n  flex: 1;\r\n  min-width: 300px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 2.5rem;\r\n  color: #333;\r\n  margin-bottom: 30px;\r\n  font-weight: 600;\r\n  position: relative;\r\n}\r\n\r\n.section-title::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -15px;\r\n  left: 0;\r\n  width: 80px;\r\n  height: 5px;\r\n  background: linear-gradient(90deg, #564680, #516790, #c9ab79);\r\n  border-radius: 3px;\r\n  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);\r\n}\r\n\r\n.intro-text p {\r\n  font-size: 1.1rem;\r\n  line-height: 1.8;\r\n  color: #555;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.test-features {\r\n  display: flex;\r\n  gap: 30px;\r\n  margin-top: 30px;\r\n}\r\n\r\n.feature-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  font-size: 1rem;\r\n  color: #666;\r\n}\r\n\r\n.feature-item .van-icon {\r\n  margin-right: 8px;\r\n}\r\n\r\n.feature-item i {\r\n  color: #c9ab79;\r\n  font-size: 1.2rem;\r\n}\r\n\r\n.intro-visual {\r\n  flex: 0 0 400px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.chakra-circles-container {\r\n  text-align: center;\r\n}\r\n\r\n.chakra-main-image {\r\n  max-width: 100%;\r\n  height: auto;\r\n  border-radius: 25px;\r\n  box-shadow: 0 20px 50px rgba(86, 70, 128, 0.2);\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.chakra-main-image:hover {\r\n  transform: translateY(-10px) scale(1.02);\r\n  box-shadow: 0 30px 70px rgba(86, 70, 128, 0.3);\r\n}\r\n\r\n.chakra-circles {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n  align-items: center;\r\n}\r\n\r\n.chakra-circle {\r\n  width: 45px;\r\n  height: 45px;\r\n  border-radius: 50%;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\r\n  animation: chakra-pulse 4s infinite;\r\n  cursor: pointer;\r\n  transition: all 0.4s ease;\r\n  position: relative;\r\n}\r\n\r\n.chakra-circle::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -5px;\r\n  left: -5px;\r\n  right: -5px;\r\n  bottom: -5px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.chakra-circle:hover {\r\n  transform: scale(1.3) rotate(15deg);\r\n  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.chakra-circle:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n@keyframes chakra-pulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.1); }\r\n}\r\n\r\n/* 开始测试部分 */\r\n.start-test-section {\r\n  padding: 100px 0;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.start-test-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 200px;\r\n  height: 200px;\r\n  background: radial-gradient(circle, rgba(86, 70, 128, 0.1), transparent);\r\n  border-radius: 50%;\r\n  z-index: 1;\r\n}\r\n\r\n.start-test-content {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.start-test-content h3 {\r\n  font-size: 2rem;\r\n  color: #333;\r\n  margin-bottom: 20px;\r\n  font-weight: 600;\r\n}\r\n\r\n.start-test-content p {\r\n  font-size: 1.1rem;\r\n  color: #666;\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.start-test-btn {\r\n  background: linear-gradient(135deg, #564680, #516790, #c9ab79);\r\n  border: none;\r\n  color: white;\r\n  padding: 20px 50px;\r\n  font-size: 1.2rem;\r\n  font-weight: 700;\r\n  border-radius: 60px;\r\n  cursor: pointer;\r\n  transition: all 0.4s ease;\r\n  box-shadow: 0 10px 25px rgba(86, 70, 128, 0.4);\r\n  position: relative;\r\n  overflow: hidden;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.start-test-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.start-test-btn:hover {\r\n  transform: translateY(-5px) scale(1.05);\r\n  box-shadow: 0 15px 40px rgba(86, 70, 128, 0.5);\r\n  background: linear-gradient(135deg, #516790, #c9ab79, #564680);\r\n}\r\n\r\n.start-test-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.start-test-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.start-test-btn .van-icon {\r\n  margin-right: 10px;\r\n}\r\n\r\n.start-test-btn i {\r\n  margin-right: 10px;\r\n}\r\n\r\n/* 快捷导航部分 */\r\n.quick-nav-section {\r\n  padding: 100px 0;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.quick-nav-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -100px;\r\n  left: 0;\r\n  right: 0;\r\n  height: 200px;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n  transform: skewY(2deg);\r\n  z-index: 1;\r\n}\r\n\r\n.quick-nav-section .section-title,\r\n.nav-cards {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.quick-nav-section .section-title {\r\n  text-align: center;\r\n  margin-bottom: 50px;\r\n}\r\n\r\n.nav-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 30px;\r\n  margin-top: 40px;\r\n}\r\n\r\n.nav-card {\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n  border-radius: 20px;\r\n  padding: 50px 35px;\r\n  text-align: center;\r\n  box-shadow: 0 10px 30px rgba(86, 70, 128, 0.1);\r\n  transition: all 0.4s ease;\r\n  cursor: pointer;\r\n  border: 3px solid transparent;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.nav-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, rgba(86, 70, 128, 0.05), transparent, rgba(201, 171, 121, 0.05));\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.nav-card:hover {\r\n  transform: translateY(-10px) scale(1.02);\r\n  box-shadow: 0 20px 50px rgba(86, 70, 128, 0.2);\r\n  border-color: #564680;\r\n}\r\n\r\n.nav-card:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n.nav-card-icon {\r\n  width: 90px;\r\n  height: 90px;\r\n  background: linear-gradient(135deg, #564680, #516790, #c9ab79);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 25px;\r\n  color: white;\r\n  box-shadow: 0 8px 20px rgba(86, 70, 128, 0.3);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.nav-card-icon .van-icon {\r\n  color: white;\r\n}\r\n\r\n.nav-card:hover .nav-card-icon {\r\n  transform: rotate(360deg) scale(1.1);\r\n  box-shadow: 0 12px 30px rgba(86, 70, 128, 0.4);\r\n}\r\n\r\n.nav-card h4 {\r\n  font-size: 1.3rem;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  font-weight: 600;\r\n}\r\n\r\n.nav-card p {\r\n  color: #666;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n/* 移动端适配 */\r\n@media (max-width: 768px) {\r\n  .hero-header-section {\r\n    padding: 80px 20px;\r\n    border-radius: 0 0 30px 30px;\r\n  }\r\n  \r\n  .hero-title {\r\n    font-size: 2.5rem;\r\n    letter-spacing: 1px;\r\n  }\r\n\r\n  .hero-subtitle {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .intro-content {\r\n    flex-direction: column;\r\n    gap: 40px;\r\n    text-align: center;\r\n  }\r\n\r\n  .intro-visual {\r\n    flex: none;\r\n  }\r\n\r\n  .test-features {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: center;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 1.8rem;\r\n  }\r\n\r\n  .nav-cards {\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n  }\r\n\r\n  .content-wrapper {\r\n    padding: 0 15px;\r\n  }\r\n\r\n  .chakra-intro-section,\r\n  .start-test-section,\r\n  .quick-nav-section {\r\n    padding: 60px 0;\r\n  }\r\n  \r\n  .start-test-btn {\r\n    padding: 18px 40px;\r\n    font-size: 1.1rem;\r\n  }\r\n  \r\n  .nav-card {\r\n    padding: 35px 25px;\r\n  }\r\n  \r\n  .nav-card-icon {\r\n    width: 70px;\r\n    height: 70px;\r\n  }\r\n  \r\n  .nav-card-icon .van-icon {\r\n    font-size: 26px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .hero-header-section {\r\n    padding: 60px 15px;\r\n    border-radius: 0 0 20px 20px;\r\n  }\r\n\r\n  .hero-title {\r\n    font-size: 2rem;\r\n    letter-spacing: 0.5px;\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 1.8rem;\r\n  }\r\n\r\n  .nav-card {\r\n    padding: 30px 20px;\r\n    border-radius: 15px;\r\n  }\r\n\r\n  .nav-card-icon {\r\n    width: 65px;\r\n    height: 65px;\r\n  }\r\n  \r\n  .nav-card-icon .van-icon {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .start-test-btn {\r\n    padding: 15px 35px;\r\n    font-size: 1rem;\r\n    border-radius: 40px;\r\n  }\r\n  \r\n  .chakra-circle {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgHA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA,OAAAC,OAAA;AAEA;EACAC,UAAA;IACAH;EACA;EACAI,IAAA;EACAC,KAAA;IACA;MACAC,YAAA;MACAC,OAAA;MACAN,aAAA,EAAAA,aAAA;IACA;EACA;EACAO,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACA;IACAD,UAAA;MACA,KAAAE,UAAA,kBAAAC,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAR,YAAA,GAAAO,GAAA,CAAAR,IAAA,CAAAC,YAAA;QACA;MACA,GAAAS,KAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,KAAA,YAAAF,GAAA;MACA;IACA;IAEA;IACAG,UAAA;MACA,KAAAZ,OAAA;MACA,KAAAI,UAAA,wBAAAC,IAAA,CAAAC,GAAA;QACA,KAAAN,OAAA;QACA,IAAAM,GAAA,CAAAC,IAAA;UACA;UACA,KAAAM,OAAA,CAAAC,IAAA;YACAC,IAAA;YACAC,KAAA;cACAC,cAAA,EAAAX,GAAA,CAAAR,IAAA,CAAAmB,cAAA;cACAC,KAAA,EAAAZ,GAAA,CAAAR,IAAA,CAAAoB;YACA;UACA;QACA;UACAvB,OAAA,CAAAgB,KAAA,CAAAL,GAAA,CAAAa,OAAA;QACA;MACA,GAAAX,KAAA,CAAAC,GAAA;QACA,KAAAT,OAAA;QACAL,OAAA,CAAAgB,KAAA;QACAD,OAAA,CAAAC,KAAA,YAAAF,GAAA;MACA;IACA;IAEA;IACAW,SAAA;MACA,KAAAP,OAAA,CAAAC,IAAA;IACA;IAEA;IACAO,UAAA;MACA,KAAAR,OAAA,CAAAC,IAAA;IACA;IAEA;IACAQ,YAAA;MACA,KAAAT,OAAA,CAAAC,IAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}