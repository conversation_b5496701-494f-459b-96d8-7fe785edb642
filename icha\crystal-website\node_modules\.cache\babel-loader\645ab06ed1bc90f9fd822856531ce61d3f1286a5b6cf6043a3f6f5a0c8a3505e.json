{"ast": null, "code": "// 外部已通过CDN引入Vue和Vant\n// import Vue from 'vue'\n// import Vant from 'vant'\n// import 'vant/lib/index.css'\nimport App from './App.vue';\nimport router from './router';\nimport './assets/css/style.css';\nimport '@fortawesome/fontawesome-free/css/all.min.css';\nimport { getRequest, postRequest, postRequestParams } from \"@/api/api\";\nimport wxShare from './utils/wxShare';\nimport Message from './utils/message';\n\n// 不需要Vue.use(Vant)，已通过CDN全局引入\n\nVue.prototype.getRequest = getRequest;\nVue.prototype.postRequest = postRequest;\nVue.prototype.postRequestParams = postRequestParams;\nVue.prototype.$toast = vant.Toast;\nVue.prototype.$wxShare = wxShare;\nVue.prototype.$message = Message;\nVue.config.productionTip = false;\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app');\nvant.Toast.setDefaultOptions({\n  position: 'bottom'\n});", "map": {"version": 3, "names": ["App", "router", "getRequest", "postRequest", "postRequestParams", "wxShare", "Message", "<PERSON><PERSON>", "prototype", "$toast", "vant", "Toast", "$wxShare", "$message", "config", "productionTip", "render", "h", "$mount", "setDefaultOptions", "position"], "sources": ["C:/Users/<USER>/Desktop/code/crystal-mall/icha/crystal-website/src/main.js"], "sourcesContent": ["// 外部已通过CDN引入Vue和Vant\r\n// import Vue from 'vue'\r\n// import Vant from 'vant'\r\n// import 'vant/lib/index.css'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport './assets/css/style.css'\r\nimport '@fortawesome/fontawesome-free/css/all.min.css'\r\nimport {getRequest,postRequest,postRequestParams} from \"@/api/api\";\r\nimport wxShare from './utils/wxShare'\r\nimport Message from './utils/message'\r\n\r\n// 不需要Vue.use(Vant)，已通过CDN全局引入\r\n\r\nVue.prototype.getRequest = getRequest;\r\nVue.prototype.postRequest = postRequest;\r\nVue.prototype.postRequestParams = postRequestParams;\r\nVue.prototype.$toast = vant.Toast\r\nVue.prototype.$wxShare = wxShare\r\nVue.prototype.$message = Message\r\n\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n  router,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n\r\nvant.Toast.setDefaultOptions({position:'bottom'})"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAOA,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAO,wBAAwB;AAC/B,OAAO,+CAA+C;AACtD,SAAQC,UAAU,EAACC,WAAW,EAACC,iBAAiB,QAAO,WAAW;AAClE,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,OAAO,MAAM,iBAAiB;;AAErC;;AAEAC,GAAG,CAACC,SAAS,CAACN,UAAU,GAAGA,UAAU;AACrCK,GAAG,CAACC,SAAS,CAACL,WAAW,GAAGA,WAAW;AACvCI,GAAG,CAACC,SAAS,CAACJ,iBAAiB,GAAGA,iBAAiB;AACnDG,GAAG,CAACC,SAAS,CAACC,MAAM,GAAGC,IAAI,CAACC,KAAK;AACjCJ,GAAG,CAACC,SAAS,CAACI,QAAQ,GAAGP,OAAO;AAChCE,GAAG,CAACC,SAAS,CAACK,QAAQ,GAAGP,OAAO;AAEhCC,GAAG,CAACO,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIR,GAAG,CAAC;EACNN,MAAM;EACNe,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACjB,GAAG;AACpB,CAAC,CAAC,CAACkB,MAAM,CAAC,MAAM,CAAC;AAEjBR,IAAI,CAACC,KAAK,CAACQ,iBAAiB,CAAC;EAACC,QAAQ,EAAC;AAAQ,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}