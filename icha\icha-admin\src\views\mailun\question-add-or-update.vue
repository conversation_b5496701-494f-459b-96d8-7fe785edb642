<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" label-width="120px">
      <el-form-item label="题目类型" prop="type">
        <el-select v-model="dataForm.type" @change="changeTypeHandle" placeholder="题目类型" filterable>
          <el-option v-for="item in questionType" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="题目名称" prop="name">
        <Tinymce v-model="dataForm.name"></Tinymce>
      </el-form-item>
      <!-- <el-form-item label="自定义分值" prop="points">
        <el-input v-model="dataForm.points" placeholder="自定义分值"></el-input>
      </el-form-item> -->
      <div v-if="dataForm.type != 2">
        <el-form-item label="选项">
          <el-button type="primary" @click="addOptionHandle(options.length)" size="small">添加选项</el-button>
          <el-table :data="options">
            <el-table-column v-if="false" prop="optionId" header-align="center" align="center" label="选项">
            </el-table-column>
            <el-table-column prop="name" width="120px" header-align="center" align="center" label="内容">
              <div slot-scope="scope">
                <el-input v-model="scope.row.name" placeholder="内容"></el-input>
              </div>
            </el-table-column>
            <el-table-column prop="root" header-align="center" align="center" label="海底轮">
              <div slot-scope="scope">
                <el-input v-model="scope.row.root" placeholder="海底轮"></el-input>
              </div>
            </el-table-column>
            <el-table-column prop="sacral" header-align="center" align="center" label="脐轮">
              <div slot-scope="scope">
                <el-input v-model="scope.row.sacral" placeholder="脐轮"></el-input>
              </div>
            </el-table-column>
            <el-table-column prop="navel" header-align="center" align="center" label="太阳轮">
              <div slot-scope="scope">
                <el-input v-model="scope.row.navel" placeholder="太阳轮"></el-input>
              </div>
            </el-table-column>
            <el-table-column prop="heart" header-align="center" align="center" label="心轮">
              <div slot-scope="scope">
                <el-input v-model="scope.row.heart" placeholder="心轮"></el-input>
              </div>
            </el-table-column>
            <el-table-column prop="throat" header-align="center" align="center" label="喉轮">
              <div slot-scope="scope">
                <el-input v-model="scope.row.throat" placeholder="喉轮"></el-input>
              </div>
            </el-table-column>
            <el-table-column prop="thirdEye" header-align="center" align="center" label="眉心轮">
              <div slot-scope="scope">
                <el-input v-model="scope.row.thirdEye" placeholder="眉心轮"></el-input>
              </div>
            </el-table-column>
            <el-table-column prop="crown" header-align="center" align="center" label="顶轮">
              <div slot-scope="scope">
                <el-input v-model="scope.row.crown" placeholder="顶轮"></el-input>
              </div>
            </el-table-column>
            <el-table-column header-align="center" align="center" label="操作">
              <div slot-scope="scope">
                <el-button type="primary" size="small" @click="deletePaymentHandle(scope.row)">删除</el-button>
              </div>
            </el-table-column>
          </el-table>
        </el-form-item>
        <!-- <el-form-item v-if="dataForm.type == 0" label="答案" prop="charOptionId">
          <el-select v-model="dataForm.charOptionId" placeholder="请选择类型">
            <el-option v-for="item in options" :key="item.optionId" :label="item.optionId"
              :value="item.optionId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-else label="答案" prop="charOptionId">
          <el-select v-model="dataForm.charOptionId" multiple placeholder="请选择类型">
            <el-option v-for="item in options" :key="item.optionId" :label="item.optionId"
              :value="item.optionId"></el-option>
          </el-select>
        </el-form-item> -->
      </div>
      <!-- 填空题 -->
      <div v-else>
        <el-form-item label="答案" prop="charOptionId">
          <el-input v-model="dataForm.charOptionId" placeholder="答案"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { QuestionCreateApi, questionUpdateApi, questionDetailApi, questionDeleteApi, questionListApi, } from '@/api/question'
import { questionoptionfindByQuestionIdApi } from '@/api/questionoption'
import { questionType } from '@/data/question'
import Tinymce from '@/components/Tinymce/index'
export default {
  data() {
    return {
      questionType,
      visible: false,
      dataForm: {
        id: 0,
        activityId: '',
        examId: '',
        name: '',
        type: 0,
        optionId: '',
        charOptionId: '',
        points: 0,
      },
      options: [
        {
          optionId: 'A',
          name: '',
          root: 0,
          sacral: 0,
          navel: 0,
          heart: 0,
          throat: 0,
          thirdEye: 0,
          crown: 0,
        }
      ],
      character: ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"],
      dataRule: {
        activityId: [
          { required: true, message: '会议id不能为空', trigger: 'blur' }
        ],
        examId: [
          { required: true, message: '考卷id不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '题目名称不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '题目类型不能为空', trigger: 'blur' }
        ],
        optionId: [
          { required: true, message: '正确答案不能为空', trigger: 'blur' }
        ],
        points: [
          { required: true, message: '自定义分值不能为空', trigger: 'blur' }
        ],
      }
    }
  },
  components: {
    Tinymce
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      this.dataForm.name = ''
        this.options = [];
        if (this.dataForm.id) {

          questionDetailApi(this.dataForm.id).then((data) => {
            console.log(data)
            this.dataForm.name = data.name
            this.dataForm.type = data.type
            if (this.dataForm.type == 1) {
              this.dataForm.charOptionId = data.charOptionId.split(',');
            } else {
              this.dataForm.charOptionId = data.charOptionId;
            }
            this.dataForm.points = data.points
          }).catch((res) => {
            this.$message.error(res.message)
          });
          questionoptionfindByQuestionIdApi(this.dataForm.id).then((data) => {
            this.options = data
          }).catch((res) => {
            this.$message.error(res.message)
          });
        } else {
          this.options = [
            {
              optionId: 'A',
              name: '完全没有',
              root: 0,
              sacral: 0,
              navel: 0,
              heart: 0,
              throat: 0,
              thirdEye: 0,
              crown: 0,
            },
            {
              optionId: 'B',
              name: '轻微感觉',
              root: 0,
              sacral: 0,
              navel: 0,
              heart: 0,
              throat: 0,
              thirdEye: 0,
              crown: 0,
            },
            {
              optionId: 'C',
              name: '中等感觉',
              root: 0,
              sacral: 0,
              navel: 0,
              heart: 0,
              throat: 0,
              thirdEye: 0,
              crown: 0,
            },
            {
              optionId: 'D',
              name: '较强感觉',
              root: 0,
              sacral: 0,
              navel: 0,
              heart: 0,
              throat: 0,
              thirdEye: 0,
              crown: 0,
            },
            {
              optionId: 'E',
              name: '感觉强烈',
              root: 0,
              sacral: 0,
              navel: 0,
              heart: 0,
              throat: 0,
              thirdEye: 0,
              crown: 0,
            },
          ]
        }
      })
    },
    changeTypeHandle() {

      if (this.dataForm.type == 1) {
        // 多选
        this.dataForm.charOptionId = []
      } else {
        // 单选,填空
        this.dataForm.charOptionId = ""
      }
    },
    addOptionHandle(length) {
      if (length >= 26) {
        this.$message.error("选项数量已上线");
        return false
      }
      this.options.push({
        optionId: this.character[length],
        name: ''
      })
      this.$forceUpdate()
    },
    deletePaymentHandle(item) {
      var index = this.options.indexOf(item)
      if (this.options.length <= 1) {
        this.$message.error("至少保留一个选项");
      } else {
        if (index !== -1) {
          if (index == this.options.length - 1) {
            // 最后一个选项
            this.options.splice(index, 1)
          } else {
            // 答案处理
            if (this.dataForm.type == 0) {
              // 单选
              if (this.dataForm.charOptionId = this.options[index].id) {
                this.dataForm.charOptionId = ''
              }
            } else if (this.dataForm.type == 1) {
              // 多选
              if (this.dataForm.charOptionId.indexOf(this.options[index].id) != -1) {
                this.dataForm.charOptionId.splice(this.dataForm.charOptionId.indexOf(this.options[index].id), 1)
                for (let e in this.dataForm.charOptionId) {
                  if (this.character.indexOf(this.dataForm.charOptionId[e]) > this.character.indexOf(this.options[index].id)) {
                    // 在删除选项之后
                    this.dataForm.charOptionId[e] = this.character[this.character.indexOf(this.dataForm.charOptionId[e]) - 1]
                  }
                }
              }
            }
            let e
            for (let e in this.options) {
              if (e > index) {
                this.options[e].optionId = this.character[e - 1]
              }
            }
            this.options.splice(index, 1)
          }
          this.$forceUpdate()
        }
      }
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dataForm.type != 2) {
            for (let i = 0; i < this.options.length; i++) {
              if (this.options[i].optionId == "" || this.options[i].name == "") {
                this.$message.error("选项不为空，请填写完整")
                return false
              }
            }
          }
          // 多选赋值
          if (this.dataForm.type == 1) {
            this.dataForm.charOptionId = this.dataForm.charOptionId.toString();
          }
          this.dataForm.questionOptionEntities = this.options;
          if (!this.dataForm.id) {

            QuestionCreateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          } else {

            questionUpdateApi(this.dataForm).then(() => {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.visible = false
                  this.$emit('refreshDataList')
                }
              })
            }).catch((res) => {
              this.$message.error(res.message)
            });
          }
        }
      })
    }
  }
}
</script>
