<template>
  <!-- 基于 Element UI 新增和修改弹窗 -->
  <el-dialog :title="!dataForm.id ? '添加-ADD' : '修改-EDITE'" :close-on-click-modal="false" :visible.sync="visible">
    <!-- 新增和创建表单表单 -->
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataSubmit()" label-width="80px">
      <el-form-item label="商品ID" prop="productId">
        <el-input v-model="dataForm.productId" placeholder="商品ID"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="dataForm.sort" placeholder="排序"></el-input>
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="dataForm.userId" placeholder="用户ID"></el-input>
      </el-form-item>
      <el-form-item label="添加时间" prop="addTime">
        <el-input v-model="dataForm.addTime" placeholder="添加时间"></el-input>
      </el-form-item>
      <el-form-item label="是否删除" prop="isDel">
        <el-input v-model="dataForm.isDel" placeholder="是否删除"></el-input>
      </el-form-item>
      <el-form-item label="手串名称" prop="name">
        <el-input v-model="dataForm.name" placeholder="手串名称"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import * as api from './.userbraceletsapi.js'
export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: 0,
        productId: '',
        sort: '',
        userId: '',
        addTime: '',
        isDel: '',
        name: ''
      },
      dataRule: {
        productId: [
          { required: true, message: '商品ID  为必填项', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '排序  为必填项', trigger: 'blur' }
        ],
        userId: [
          { required: true, message: '用户ID  为必填项', trigger: 'blur' }
        ],
        addTime: [
          { required: true, message: '添加时间  为必填项', trigger: 'blur' }
        ],
        isDel: [
          { required: true, message: '是否删除  为必填项', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '手串名称  为必填项', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init(id) { // 初始化表单验证规则
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          api.userbraceletsDetailApi(id).then(res => {
            this.dataForm = res;
          })
        }
      })
    },
    // 表单数据提交
    dataSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          api.UserBraceletsCreateApi().then(res => {
            // TODO 保存数据
          });
        }
      })
    }
  }
}
</script>
