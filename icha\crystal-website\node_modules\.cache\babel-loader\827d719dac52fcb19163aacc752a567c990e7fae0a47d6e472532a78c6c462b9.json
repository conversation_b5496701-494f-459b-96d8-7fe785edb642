{"ast": null, "code": "/**\r\n * 消息提示工具类，封装Vant UI消息组件\r\n */\n// 使用全局vant对象，不需要导入\n// import { Toast, Dialog } from 'vant';\n\n// 定义消息类型\nconst MessageType = {\n  INFO: 'info',\n  SUCCESS: 'success',\n  WARNING: 'warning',\n  ERROR: 'error'\n};\n\n/**\r\n * Message类封装了消息提示的常用方法\r\n */\nclass Message {\n  /**\r\n   * 显示普通消息\r\n   * @param {string} message 消息内容\r\n   * @param {number} duration 显示时长(ms)，默认3000ms\r\n   */\n  static info(message, duration = 3000) {\n    vant.Toast({\n      message: message,\n      duration: duration,\n      type: MessageType.INFO\n    });\n  }\n\n  /**\r\n   * 显示成功消息\r\n   * @param {string} message 消息内容\r\n   * @param {number} duration 显示时长(ms)，默认3000ms\r\n   */\n  static success(message, duration = 3000) {\n    vant.Toast.success({\n      message: message,\n      duration: duration\n    });\n  }\n\n  /**\r\n   * 显示警告消息\r\n   * @param {string} message 消息内容\r\n   * @param {number} duration 显示时长(ms)，默认3000ms\r\n   */\n  static warning(message, duration = 3000) {\n    vant.Toast({\n      message: message,\n      duration: duration,\n      type: MessageType.WARNING\n    });\n  }\n\n  /**\r\n   * 显示错误消息\r\n   * @param {string} message 消息内容\r\n   * @param {number} duration 显示时长(ms)，默认3000ms\r\n   */\n  static error(message, duration = 3000) {\n    vant.Toast.fail({\n      message: message,\n      duration: duration\n    });\n  }\n\n  /**\r\n   * 显示加载中消息\r\n   * @param {string} message 消息内容，默认为\"加载中...\"\r\n   * @param {boolean} forbidClick 是否禁止背景点击，默认为true\r\n   */\n  static loading(message = '加载中...', forbidClick = true) {\n    return vant.Toast.loading({\n      message: message,\n      forbidClick: forbidClick,\n      duration: 0\n    });\n  }\n\n  /**\r\n   * 关闭所有消息提示\r\n   */\n  static clear() {\n    vant.Toast.clear();\n  }\n\n  /**\r\n   * 显示确认对话框\r\n   * @param {string} title 标题\r\n   * @param {string} message 内容\r\n   * @param {Function} callback 确认回调函数\r\n   * @param {Function} cancelCallback 取消回调函数\r\n   */\n  static confirm(title, message, callback, cancelCallback) {\n    vant.Dialog.confirm({\n      title: title,\n      message: message\n    }).then(() => {\n      callback && callback();\n    }).catch(() => {\n      cancelCallback && cancelCallback();\n    });\n  }\n\n  /**\r\n   * 显示提示对话框\r\n   * @param {string} title 标题\r\n   * @param {string} message 内容\r\n   * @param {Function} callback 确认回调函数\r\n   */\n  static alert(title, message, callback) {\n    vant.Dialog.alert({\n      title: title,\n      message: message\n    }).then(() => {\n      callback && callback();\n    });\n  }\n}\nexport default Message;", "map": {"version": 3, "names": ["MessageType", "INFO", "SUCCESS", "WARNING", "ERROR", "Message", "info", "message", "duration", "vant", "Toast", "type", "success", "warning", "error", "fail", "loading", "forbidClick", "clear", "confirm", "title", "callback", "cancelCallback", "Dialog", "then", "catch", "alert"], "sources": ["C:/Users/<USER>/Desktop/code/crystal-mall/icha/crystal-website/src/utils/message.js"], "sourcesContent": ["/**\r\n * 消息提示工具类，封装Vant UI消息组件\r\n */\r\n// 使用全局vant对象，不需要导入\r\n// import { Toast, Dialog } from 'vant';\r\n\r\n// 定义消息类型\r\nconst MessageType = {\r\n  INFO: 'info',\r\n  SUCCESS: 'success',\r\n  WARNING: 'warning',\r\n  ERROR: 'error'\r\n}\r\n\r\n/**\r\n * Message类封装了消息提示的常用方法\r\n */\r\nclass Message {\r\n  /**\r\n   * 显示普通消息\r\n   * @param {string} message 消息内容\r\n   * @param {number} duration 显示时长(ms)，默认3000ms\r\n   */\r\n  static info(message, duration = 3000) {\r\n    vant.Toast({\r\n      message: message,\r\n      duration: duration,\r\n      type: MessageType.INFO\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 显示成功消息\r\n   * @param {string} message 消息内容\r\n   * @param {number} duration 显示时长(ms)，默认3000ms\r\n   */\r\n  static success(message, duration = 3000) {\r\n    vant.Toast.success({\r\n      message: message,\r\n      duration: duration\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 显示警告消息\r\n   * @param {string} message 消息内容\r\n   * @param {number} duration 显示时长(ms)，默认3000ms\r\n   */\r\n  static warning(message, duration = 3000) {\r\n    vant.Toast({\r\n      message: message,\r\n      duration: duration,\r\n      type: MessageType.WARNING\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 显示错误消息\r\n   * @param {string} message 消息内容\r\n   * @param {number} duration 显示时长(ms)，默认3000ms\r\n   */\r\n  static error(message, duration = 3000) {\r\n    vant.Toast.fail({\r\n      message: message,\r\n      duration: duration\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 显示加载中消息\r\n   * @param {string} message 消息内容，默认为\"加载中...\"\r\n   * @param {boolean} forbidClick 是否禁止背景点击，默认为true\r\n   */\r\n  static loading(message = '加载中...', forbidClick = true) {\r\n    return vant.Toast.loading({\r\n      message: message,\r\n      forbidClick: forbidClick,\r\n      duration: 0\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 关闭所有消息提示\r\n   */\r\n  static clear() {\r\n    vant.Toast.clear();\r\n  }\r\n\r\n  /**\r\n   * 显示确认对话框\r\n   * @param {string} title 标题\r\n   * @param {string} message 内容\r\n   * @param {Function} callback 确认回调函数\r\n   * @param {Function} cancelCallback 取消回调函数\r\n   */\r\n  static confirm(title, message, callback, cancelCallback) {\r\n    vant.Dialog.confirm({\r\n      title: title,\r\n      message: message\r\n    }).then(() => {\r\n      callback && callback();\r\n    }).catch(() => {\r\n      cancelCallback && cancelCallback();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 显示提示对话框\r\n   * @param {string} title 标题\r\n   * @param {string} message 内容\r\n   * @param {Function} callback 确认回调函数\r\n   */\r\n  static alert(title, message, callback) {\r\n    vant.Dialog.alert({\r\n      title: title,\r\n      message: message\r\n    }).then(() => {\r\n      callback && callback();\r\n    });\r\n  }\r\n}\r\n\r\nexport default Message; "], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAMA,WAAW,GAAG;EAClBC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACZ;AACF;AACA;AACA;AACA;EACE,OAAOC,IAAIA,CAACC,OAAO,EAAEC,QAAQ,GAAG,IAAI,EAAE;IACpCC,IAAI,CAACC,KAAK,CAAC;MACTH,OAAO,EAAEA,OAAO;MAChBC,QAAQ,EAAEA,QAAQ;MAClBG,IAAI,EAAEX,WAAW,CAACC;IACpB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOW,OAAOA,CAACL,OAAO,EAAEC,QAAQ,GAAG,IAAI,EAAE;IACvCC,IAAI,CAACC,KAAK,CAACE,OAAO,CAAC;MACjBL,OAAO,EAAEA,OAAO;MAChBC,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOK,OAAOA,CAACN,OAAO,EAAEC,QAAQ,GAAG,IAAI,EAAE;IACvCC,IAAI,CAACC,KAAK,CAAC;MACTH,OAAO,EAAEA,OAAO;MAChBC,QAAQ,EAAEA,QAAQ;MAClBG,IAAI,EAAEX,WAAW,CAACG;IACpB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOW,KAAKA,CAACP,OAAO,EAAEC,QAAQ,GAAG,IAAI,EAAE;IACrCC,IAAI,CAACC,KAAK,CAACK,IAAI,CAAC;MACdR,OAAO,EAAEA,OAAO;MAChBC,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOQ,OAAOA,CAACT,OAAO,GAAG,QAAQ,EAAEU,WAAW,GAAG,IAAI,EAAE;IACrD,OAAOR,IAAI,CAACC,KAAK,CAACM,OAAO,CAAC;MACxBT,OAAO,EAAEA,OAAO;MAChBU,WAAW,EAAEA,WAAW;MACxBT,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,OAAOU,KAAKA,CAAA,EAAG;IACbT,IAAI,CAACC,KAAK,CAACQ,KAAK,CAAC,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,OAAOA,CAACC,KAAK,EAAEb,OAAO,EAAEc,QAAQ,EAAEC,cAAc,EAAE;IACvDb,IAAI,CAACc,MAAM,CAACJ,OAAO,CAAC;MAClBC,KAAK,EAAEA,KAAK;MACZb,OAAO,EAAEA;IACX,CAAC,CAAC,CAACiB,IAAI,CAAC,MAAM;MACZH,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IACxB,CAAC,CAAC,CAACI,KAAK,CAAC,MAAM;MACbH,cAAc,IAAIA,cAAc,CAAC,CAAC;IACpC,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOI,KAAKA,CAACN,KAAK,EAAEb,OAAO,EAAEc,QAAQ,EAAE;IACrCZ,IAAI,CAACc,MAAM,CAACG,KAAK,CAAC;MAChBN,KAAK,EAAEA,KAAK;MACZb,OAAO,EAAEA;IACX,CAAC,CAAC,CAACiB,IAAI,CAAC,MAAM;MACZH,QAAQ,IAAIA,QAAQ,CAAC,CAAC;IACxB,CAAC,CAAC;EACJ;AACF;AAEA,eAAehB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}