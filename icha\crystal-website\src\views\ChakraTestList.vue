<template>
  <Layout>
    <div class="chakra-list-page">
    <!-- 标题栏 -->
    <div class="nav-title">
      <div class="nav-left">
        <van-button 
          class="back-button"
          @click="goBack"
          plain
        >
          <van-icon name="arrow-left" size="18" />
        </van-button>
        <div class="color-bar"></div>
        <div class="title-text">我的脉轮测试</div>
      </div>
      <div class="nav-right">
        <van-button 
          class="new-test-button"
          @click="createNewTest"
          size="small"
          type="primary"
        >
          <van-icon name="plus" size="14" />
          <span>新测试</span>
        </van-button>
      </div>
    </div>
    
    <!-- 测试列表 -->
    <div class="list-container" v-if="questionList.length > 0">
      <div 
        v-for="item in questionList" 
        :key="item.id"
        class="test-card"
        @click="handleCardClick(item)"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="date-info">
            <van-icon name="calendar-o" size="16" color="#666" />
            <span class="date-text">{{ item.addTime }}</span>
          </div>
          <div class="status-tag" :class="getStatusClass(item.status)">
            {{ item.status == 0 ? '未提交' : '已提交' }}
          </div>
        </div>
        
        <!-- 卡片内容 -->
        <div class="card-content">
          <!-- 已提交的测试显示脉轮预览 -->
          <div v-if="item.status == 1" class="chakra-preview">
            <div class="chakra-indicators">
              <div 
                v-for="(color, index) in chakraColors" 
                :key="index"
                class="chakra-item"
              >
                <div class="chakra-dot" :style="{ backgroundColor: color }"></div>
                <div class="chakra-value">
                  {{ item.chakraData && item.chakraData[index] ? item.chakraData[index].value : '-' }}
                </div>
                <div class="chakra-name">{{ chakraNames[index] }}</div>
              </div>
            </div>
            <div class="preview-hint">点击查看详细结果</div>
          </div>
          
          <!-- 未提交的测试显示继续测试提示 -->
          <div v-else class="test-preview">
            <div class="preview-icon">
              <van-icon name="edit" size="24" color="#c9ab79" />
            </div>
            <div class="preview-text">继续进行脉轮测试</div>
          </div>
        </div>
        
        <!-- 卡片底部 -->
        <div class="card-footer">
          <van-button 
            size="small"
            class="detail-btn"
            @click.stop="handleDetailClick(item)"
          >
            <van-icon name="eye-o" size="14" />
            <span>{{ item.status == 0 ? '继续测试' : '查看详情' }}</span>
          </van-button>
          <van-button 
            size="small"
            class="delete-btn"
            @click.stop="handleDeleteClick(item.id)"
          >
            <van-icon name="delete-o" size="14" />
            <span>删除</span>
          </van-button>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="!loading" class="empty-state">
      <van-empty 
        image="search" 
        description="暂无脉轮测试记录"
      >
        <van-button 
          type="primary" 
          class="create-btn"
          @click="createNewTest"
        >
          <van-icon name="plus" size="14" />
          <span>创建新测试</span>
        </van-button>
      </van-empty>
    </div>
    
    <!-- 加载状态 -->
    <van-loading v-if="loading" class="page-loading" />
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/common/Layout.vue'
import Message from '@/utils/message'

export default {
  components: {
    Layout
  },
  name: 'ChakraTestList',
  data() {
    return {
      questionList: [],
      loading: true,
      // 脉轮颜色
      chakraColors: [
        "#993734", // 海底轮
        "#be6f2a", // 脐轮
        "#d7c34a", // 太阳轮
        "#5f9057", // 心轮
        "#5b8aa4", // 喉轮
        "#2c3485", // 眉心轮
        "#7e4997"  // 顶轮
      ],
      chakraNames: [
        "海底轮", 
        "脐轮", 
        "太阳轮", 
        "心轮", 
        "喉轮", 
        "眉心轮", 
        "顶轮"
      ]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 获取测试列表
    getList() {
      this.loading = true
      this.getRequest('/question/list').then(res => {
        this.loading = false
        if (res.code == 200) {
          this.questionList = res.data || []
          this.fetchChakraData()
        } else {
          Message.error(res.message || '获取列表失败')
        }
      }).catch(err => {
        this.loading = false
        Message.error('获取列表失败')
        console.error('获取列表失败:', err)
      })
    },
    
    // 获取脉轮数据
    fetchChakraData() {
      this.questionList.forEach((item, idx) => {
        if (item.status == 1) {
          this.getRequest('/question/finishDetail', { questionUserId: item.id }).then(res => {
            if (res.code == 200) {
              const chakraData = [
                { name: "海底轮", value: res.data.questionUserEntity.root },
                { name: "脐轮", value: res.data.questionUserEntity.sacral },
                { name: "太阳轮", value: res.data.questionUserEntity.navel },
                { name: "心轮", value: res.data.questionUserEntity.heart },
                { name: "喉轮", value: res.data.questionUserEntity.throat },
                { name: "眉心轮", value: res.data.questionUserEntity.thirdEye },
                { name: "顶轮", value: res.data.questionUserEntity.crown },
              ]
              this.$set(this.questionList[idx], 'chakraData', chakraData)
            }
          }).catch(error => {
            console.log('获取脉轮数据失败', error)
          })
        }
      })
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      return status == 0 ? 'status-pending' : 'status-submitted'
    },
    
    // 卡片点击
    handleCardClick(item) {
      this.handleDetailClick(item)
    },
    
    // 详情按钮点击
    handleDetailClick(item) {
      if (item.status == 0) {
        // 继续测试
        this.getRequest('/question/startExam', { questionUserId: item.id }).then(res => {
          if (res.code == 200) {
            this.$router.push({
              path: '/chakra-test/start',
              query: {
                questionUserId: res.data.questionUserId,
                token: res.data.token
              }
            })
          } else {
            Message.error(res.message || '开始测试失败')
          }
        }).catch(err => {
          Message.error('开始测试失败')
          console.error('开始测试失败:', err)
        })
      } else {
        // 查看详情
        this.$router.push({
          path: '/chakra-test/detail',
          query: { questionUserId: item.id }
        })
      }
    },
    
    // 删除按钮点击
    handleDeleteClick(id) {
      Message.confirm('提示', '确认删除这条测试记录吗？', () => {
        this.getRequest('/question/questionUserDelete', { questionUserId: id }).then(res => {
          if (res.code == 200) {
            Message.success('删除成功')
            this.getList()
          } else {
            Message.error(res.message || '删除失败')
          }
        }).catch(err => {
          Message.error('删除失败')
          console.error('删除失败:', err)
        })
      }, () => {
        // 取消删除
      })
    },
    
    // 创建新测试
    createNewTest() {
      this.$router.push('/chakra-test')
    },
    
    // 返回上一页
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.chakra-list-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  width: 100%;
}

.nav-title {
  padding: 15px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 4px 15px rgba(86, 70, 128, 0.1);
  margin-bottom: 15px;
  border-radius: 0 0 20px 20px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-right {
  display: flex;
  align-items: center;
}

.back-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(86, 70, 128, 0.08);
  border: 1px solid rgba(86, 70, 128, 0.2);
  color: #564680;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(86, 70, 128, 0.15);
  border-color: rgba(86, 70, 128, 0.4);
  transform: translateX(-2px);
}

.color-bar {
  width: 6px;
  height: 25px;
  background: linear-gradient(135deg, #564680, #516790, #c9ab79);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(86, 70, 128, 0.3);
}

.title-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  letter-spacing: 0.5px;
}

.new-test-button {
  background: linear-gradient(135deg, #564680, #516790);
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  color: white;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);
  transition: all 0.3s ease;
}

.new-test-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);
}

.new-test-button span {
  margin-left: 4px;
}

.list-container {
  padding: 0 20px 20px;
}

.test-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(86, 70, 128, 0.08);
  overflow: hidden;
  margin-bottom: 20px;
  transition: all 0.4s ease;
  border: 2px solid transparent;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #564680, #516790, #c9ab79);
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(86, 70, 128, 0.12);
    border-color: rgba(86, 70, 128, 0.2);
  }
}

.card-header {
  padding: 18px;
  border-bottom: 2px solid rgba(86, 70, 128, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
}

.date-info {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
  
  .date-text {
    margin-left: 4px;
  }
}

.status-tag {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.3px;
  
  &.status-pending {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.15), rgba(255, 152, 0, 0.08));
    color: #ff9800;
    border: 2px solid rgba(255, 152, 0, 0.3);
  }
  
  &.status-submitted {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.15), rgba(76, 175, 80, 0.08));
    color: #4caf50;
    border: 2px solid rgba(76, 175, 80, 0.3);
  }
}

.card-content {
  padding: 15px 12px;
  min-height: 75px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chakra-preview {
  width: 100%;
  text-align: center;
}

.chakra-indicators {
  display: flex;
  justify-content: center;
  gap: 5px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.chakra-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 40px;
  margin-bottom: 8px;
}

.chakra-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  margin-bottom: 5px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.chakra-indicators:hover .chakra-dot {
  transform: scale(1.2);
}

.chakra-value {
  font-size: 11px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.chakra-name {
  font-size: 10px;
  color: #666;
  white-space: nowrap;
}

.preview-hint {
  font-size: 12px;
  color: #999;
}

.test-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.preview-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(201, 171, 121, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-text {
  font-size: 14px;
  color: #666;
}

.card-footer {
  padding: 12px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: rgba(0, 0, 0, 0.02);
}

.detail-btn {
  background: linear-gradient(135deg, #564680, #516790);
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);
  }
  
  span {
    margin-left: 4px;
  }
}

.delete-btn {
  background: linear-gradient(135deg, #dd5c5f, #c85458);
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(221, 92, 95, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(221, 92, 95, 0.4);
  }
  
  span {
    margin-left: 4px;
  }
}

.create-btn {
  background: linear-gradient(135deg, #564680, #516790);
  border: none;
  margin-top: 20px;
  border-radius: 25px;
  padding: 12px 30px;
  font-weight: 700;
  box-shadow: 0 6px 20px rgba(86, 70, 128, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(86, 70, 128, 0.4);
  }
  
  span {
    margin-left: 6px;
  }
}

.empty-state {
  padding: 80px 30px;
}

.page-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 移动端适配 */
@media (min-width: 1200px) {
  /* PC端大屏幕优化 */
  .nav-title {
    max-width: 1200px;
    margin: 0 auto 20px;
    border-radius: 0 0 25px 25px;
    padding: 20px 40px;
    height: 70px;
  }
  
  .title-text {
    font-size: 24px;
  }
  
  .back-button {
    width: 45px;
    height: 45px;
  }
  
  .new-test-button {
    padding: 10px 20px;
    font-size: 15px;
  }
  
  .list-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px 20px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
  
  .test-card {
    margin-bottom: 0;
    min-height: 200px;
  }
  
  .card-content {
    min-height: 100px;
  }
  
  .chakra-indicators {
    justify-content: center;
    gap: 8px;
  }
  
  .chakra-item {
    width: 45px;
  }
  
  .chakra-value {
    font-size: 12px;
    font-weight: 600;
  }
  
  .chakra-name {
    font-size: 11px;
  }
  
  .empty-state {
    max-width: 600px;
    margin: 0 auto;
    padding: 120px 40px;
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  /* PC端中屏幕优化 */
  .nav-title {
    max-width: 960px;
    margin: 0 auto 20px;
    padding: 18px 30px;
    height: 65px;
  }
  
  .title-text {
    font-size: 22px;
  }
  
  .back-button {
    width: 42px;
    height: 42px;
  }
  
  .list-container {
    max-width: 960px;
    margin: 0 auto;
    padding: 0 30px 20px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .test-card {
    margin-bottom: 0;
  }
  
  .chakra-indicators {
    gap: 6px;
  }
  
  .chakra-item {
    width: 42px;
  }
  
  .empty-state {
    max-width: 550px;
    margin: 0 auto;
    padding: 100px 30px;
  }
}

@media (min-width: 769px) and (max-width: 991px) {
  /* 平板横屏优化 */
  .nav-title {
    max-width: 750px;
    margin: 0 auto 20px;
    padding: 16px 25px;
    height: 60px;
  }
  
  .title-text {
    font-size: 20px;
  }
  
  .list-container {
    max-width: 750px;
    margin: 0 auto;
    padding: 0 25px 20px;
  }
  
  .test-card {
    max-width: 700px;
    margin: 0 auto 20px;
  }
  
  .chakra-indicators {
    gap: 8px;
    justify-content: center;
  }
  
  .chakra-item {
    width: 50px;
  }
  
  .chakra-value {
    font-size: 12px;
  }
  
  .chakra-name {
    font-size: 11px;
  }
  
  .empty-state {
    max-width: 500px;
    margin: 0 auto;
    padding: 90px 25px;
  }
}

@media (max-width: 768px) {
  .nav-title {
    padding: 12px 20px;
    height: 55px;
  }
  
  .nav-left {
    gap: 8px;
  }
  
  .back-button {
    width: 36px;
    height: 36px;
  }
  
  .title-text {
    font-size: 18px;
  }
  
  .new-test-button {
    padding: 6px 12px;
    font-size: 13px;
  }
  
  .test-card {
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .card-header {
    padding: 14px;
  }
  
  .card-content {
    padding: 12px;
  }
  
  .card-footer {
    padding: 10px;
  }
  
  .detail-btn,
  .delete-btn {
    padding: 6px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .nav-title {
    padding: 10px 15px;
    height: 50px;
  }
  
  .nav-left {
    gap: 6px;
  }
  
  .back-button {
    width: 32px;
    height: 32px;
  }
  
  .color-bar {
    width: 4px;
    height: 20px;
  }
  
  .title-text {
    font-size: 16px;
  }
  
  .new-test-button {
    padding: 5px 10px;
    font-size: 12px;
  }
  
  .list-container {
    padding: 0 15px 20px;
  }
  
  .date-info {
    font-size: 13px;
  }
  
  .status-tag {
    padding: 4px 12px;
    font-size: 11px;
  }
  
  .chakra-indicators {
    gap: 3px;
  }
  
  .chakra-item {
    width: 32px;
  }
  
  .chakra-value {
    font-size: 10px;
  }
  
  .chakra-name {
    font-size: 9px;
  }
}
</style>
