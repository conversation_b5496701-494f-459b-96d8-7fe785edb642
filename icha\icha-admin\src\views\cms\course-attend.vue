<template>
  <div class="course-attend-container">
    <!-- 搜索栏 -->
    <el-card class="search-container">
      <el-form :inline="true" :model="queryParams" ref="queryForm" size="small">
        <!-- <el-form-item label="课程ID" prop="cmsCourseId">
          <el-input v-model="queryParams.cmsCourseId" placeholder="请输入课程ID" clearable @keyup.enter.native="handleQuery" />
        </el-form-item> -->
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- <el-form-item label="报名状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择报名状态" clearable>
            <el-option v-for="item in attendStatusOptions" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="table-container">
      <div class="table-header">
        <!-- <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button> -->
        <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple"
          @click="handleDelete">删除</el-button>
          <el-button type="primary" style="float: right;" size="small" @click="$router.go(-1)">返回上一页</el-button>
      </div>

      <!-- 课程报名表格 -->
      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="ID" align="center" prop="id" width="80" /> -->
        <!-- <el-table-column label="课程ID" align="center" prop="cmsCourseId" /> -->
        <el-table-column label="用户名" align="center" prop="userName" />
        <el-table-column label="手机号" align="center" prop="mobile" />
        <!-- <el-table-column label="用户ID" align="center" prop="userId" /> -->
        <!-- <el-table-column label="报名状态" align="center" prop="status">
          <template slot-scope="scope">
            {{ getStatusLabel(scope.row.status) }}
          </template>
        </el-table-column> -->
        <el-table-column label="邀请码" align="center" prop="inviteCode" />
        <el-table-column label="添加时间" align="center" prop="addTime" width="160" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button> -->
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="block">
        <el-pagination :page-sizes="[20, 40, 60, 80]" :page-size="queryParams.limit"
          :current-page="queryParams.page" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="pageChange" />
      </div>
    </el-card>

    <!-- 新增/修改弹窗 -->
    <course-attend-add-or-update ref="addOrUpdate" @refreshDataList="getList"></course-attend-add-or-update>
  </div>
</template>

<script>
import {
  cmsCourseAttendListApi,
  cmsCourseAttendDeleteApi
} from '@/api/cmsCourseAttend'
import CourseAttendAddOrUpdate from './course-attend-add-or-update'
import { attendStatus } from '@/data/common'

export default {
  name: 'CourseAttend',
  components: {
    CourseAttendAddOrUpdate
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 课程报名列表
      dataList: [],
      // 查询参数
      queryParams: {
        page: 1,
        limit: 20,
        cmsCourseId: undefined,
        userId: undefined,
        status: undefined
      },
      // 报名状态选项
      attendStatusOptions: attendStatus
    }
  },
  created() {
    this.queryParams.cmsCourseId = this.$route.query.id
    this.getList()
  },
  methods: {
    /** 查询课程报名列表 */
    getList() {
      this.loading = true
      cmsCourseAttendListApi(this.queryParams).then(res => {
        this.dataList = res.list || [];
        this.total = parseInt(res.total);
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.addOrUpdate.init('',this.queryParams.cmsCourseId)
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.addOrUpdate.init(row.id, this.queryParams.cmsCourseId)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除选中的数据?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return cmsCourseAttendDeleteApi([].concat(ids))
      }).then(() => {
        this.getList()
        this.$message.success('删除成功')
      }).catch(() => { })
    },
    /** 获取状态标签 */
    getStatusLabel(status) {
      const found = this.attendStatusOptions.find(item => item.key === status)
      return found ? found.value : ''
    },
    // 页码改变
    pageChange(page) {
      this.queryParams.page = page
      this.getList()
    },
    // 每页条数改变
    handleSizeChange(val) {
      this.queryParams.limit = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.course-attend-container {
  padding: 20px;

  .search-container {
    margin-bottom: 20px;
  }

  .table-container {
    .table-header {
      margin-bottom: 20px;
    }
  }
}
</style> 