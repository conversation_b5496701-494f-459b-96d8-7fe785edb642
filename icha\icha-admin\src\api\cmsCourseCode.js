import request from '@/utils/request'

/**
 * 获取课程邀请码列表
 * @param {Object} params 查询参数
 */
export function cmsCourseCodeListApi(params) {
    return request({
        url: '/admin/cms/course/code/list',
        method: 'get',
        params
    })
}
export function cmsCourseCodeCreateCodeApi(params) {
    return request({
        url: '/admin/cms/course/code/createCode',
        method: 'get',
        params
    })
}

/**
 * 获取课程邀请码详情
 * @param {Number} id 邀请码ID
 */
export function cmsCourseCodeInfoApi(id) {
    return request({
        url: `/admin/cms/course/code/info/${id}`,
        method: 'get'
    })
}

/**
 * 新增课程邀请码
 * @param {Object} data 邀请码信息
 */
export function cmsCourseCodeAddApi(data) {
    return request({
        url: '/admin/cms/course/code/save',
        method: 'post',
        data
    })
}

/**
 * 更新课程邀请码
 * @param {Object} data 邀请码信息
 */
export function cmsCourseCodeUpdateApi(data) {
    return request({
        url: '/admin/cms/course/code/update',
        method: 'post',
        data
    })
}

/**
 * 删除课程邀请码
 * @param {Array} ids 邀请码ID数组
 */
export function cmsCourseCodeDeleteApi(ids) {
    return request({
        url: '/admin/cms/course/code/delete',
        method: 'post',
        data: ids
    })
}

/**
 * 更新邀请码使用状态
 * @param {Object} data 包含id和is_use信息
 */
export function cmsCourseCodeUpdateStatusApi(data) {
    return request({
        url: '/admin/cms/course/code/status',
        method: 'post',
        data
    })
} 