<template>
  <el-dialog :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :visible.sync="visible">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmit()"
      label-width="120px">
      <el-form-item label="证书编号" prop="number">
        <el-input v-model="dataForm.number" placeholder="证书编号"></el-input>
      </el-form-item>
      <el-form-item label="图片" prop="image">
        <div class="upLoadPicBox" @click="modalPicTap('1')">
          <div v-if="dataForm.image" class="pictrue"><img :src="dataForm.image"></div>
          <div v-else class="upLoad">
            <i class="el-icon-camera cameraIconfont" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="用户名" prop="name">
        <el-input v-model="dataForm.name" placeholder="用户名"></el-input>
      </el-form-item>
      <el-form-item label="证书类型" prop="type">
        <el-select v-model="dataForm.type" placeholder="选择证书类型">
          <el-option v-for="item in certificateType" :key="item.key" :label="item.value"
            :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <!-- 开始时间 -->
      <el-form-item label="颁证日期" prop="startTime">
        <el-date-picker v-model="dataForm.startTime" type="date" value-format="yyyy/MM/dd" placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <!-- 结束时间 -->
      <!-- <el-form-item label="结束时间" prop="endTime">
        <el-date-picker v-model="dataForm.endTime" type="date" value-format="yyyy/MM/dd" placeholder="选择日期">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="发证机构" prop="issuer">
        <el-input v-model="dataForm.issuer" placeholder="发证机构"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { cmsCertificateInfoApi, cmsCertificateAddApi, cmsCertificateUpdateApi } from '@/api/cmsCertificate'
import { certificateType } from '@/data/common'
export default {
  data() {
    return {
      visible: false,
      dateRange: [],
      certificateType,
      dataForm: {
        id: 0,
        number: '',
        image: '',
        name: '',
        type: 0,
        startTime: '',
        endTime: '',
        issuer: ''
      },
      dataRule: {
        number: [{ required: true, message: '证书编号不能为空', trigger: 'blur' }],
        name: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        type: [{ required: true, message: '证书类型不能为空', trigger: 'change' }],
        // dateRange: [{ required: true, message: '证书有效期不能为空', trigger: 'change' }],
        issuer: [{ required: true, message: '发证机构不能为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    init(id) {
      this.dataForm.id = id || 0
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          cmsCertificateInfoApi(this.dataForm.id).then(data => {
            if (data) {
              this.dataForm.number = data.number
              this.dataForm.name = data.name
              this.dataForm.type = data.type
              this.dataForm.image = data.image
              this.dataForm.startTime = data.startTime
              this.dataForm.endTime = data.endTime
              // this.dateRange = data.startTime && data.endTime ? [data.startTime, data.endTime] : []
              this.dataForm.issuer = data.issuer
            }
          }).catch(() => { })
        }
      })
    },
    // 点击图片上传
    modalPicTap(field) {
      const _this = this;
      this.$modalUpload(function (img) {
        _this.dataForm.image = img[0].sattDir
      }, field, 'content')
    },
    // 表单提交
    dataFormSubmit() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const submitApi = this.dataForm.id ? cmsCertificateUpdateApi : cmsCertificateAddApi
          // if (this.dateRange && this.dateRange.length === 2) {
          //   this.dataForm.startTime = this.dateRange[0]
          //   this.dataForm.endTime = this.dateRange[1]
          // }
          submitApi({ ...this.dataForm }).then(() => {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch((res) => {
            this.$message.error(res.message)
          })
        }
      })
    }
  }
}
</script>