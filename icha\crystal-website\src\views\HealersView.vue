<template>
	<Layout>
		<!-- 页面顶部背景区域 -->
		<div class="healers-hero-section">
			<div class="hero-content">
				<h1 class="hero-title"><i class="fa fa-diamond fa-spin-pulse"></i> 水晶疗愈师</h1>
				<p class="hero-subtitle"><i class="fa fa-heart"></i> 找到您的专属疗愈指导者，开启内在平衡之旅</p>
			</div>
		</div>

		<!-- 主要内容区域 -->
		<div class="section healers-section">
			<div class="container" style="max-width: 1200px">
				<!-- 搜索和筛选区域 -->
				<div class="course-search">
					<div class="search-container">
						<input type="text" v-model="searchKeyword" placeholder="搜索疗愈师名称或关键词" class="search-input" />
						<button class="search-btn" @click="searchHealers">搜索</button>
					</div>
				</div>

				<!-- 列表内容区域 -->
				<div class="healers-list-container">
					<div class="crystal-grid" :class="{'crystal-grid-mobile': isMobilePhone}">
						<div v-for="(healer, index) in healerList" :key="index" class="crystal-card">
							<div class="crystal-card-img">
								<img :src="healer.avatar" alt="">
								<div class="card-overlay"></div>
							</div>
							<div class="crystal-badge"><i class="fa fa-certificate"></i> 认证</div>
							<div class="crystal-card-info">
								<h3><i class="fa fa-user-circle-o"></i> {{healer.name}}</h3>
								<div class="crystal-tags">
									<span v-for="(tag, i) in healer.tags" :key="i"><i class="fa fa-star-o"></i> {{tag}}</span>
								</div>
								<p><i class="fa fa-quote-left quote-icon"></i> {{healer.intro}}</p>
								<div class="crystal-card-footer">
									<span class="crystal-card-location"><i class="fa fa-map-marker"></i> {{healer.location}}</span>
									<button class="crystal-btn" @click="viewHealerDetail(healer.id)"><i class="fa fa-info-circle"></i> 查看详情</button>
								</div>
							</div>
						</div>
					</div>

					<!-- 空状态展示 -->
					<div v-if="healerList.length == 0" class="healers-empty-state">
						<i class="fa fa-search fa-4x empty-icon"></i>
						<h3>未找到匹配的水晶疗愈师</h3>
						<p><i class="fa fa-info-circle"></i> 请尝试更改搜索条件或清除筛选器</p>
						<button class="crystal-btn" @click="resetFilters"><i class="fa fa-refresh"></i> 清除筛选</button>
					</div>
					<!-- 分页 -->
					<ul class="am-pagination" style="text-align: center;" v-if="total > 0">
						<li :class="pageIndex == 1 ? 'am-disabled':''" @click="changeIndex(pageIndex - 1)">
							<a href="javascript:void(0);">&laquo;</a>
						</li>
						
						<li v-for="p in totalPage" :key="p" @click="changeIndex(p)" :class="pageIndex == p ? 'am-active':''">
							<a href="javascript:void(0);">{{p}}</a>
						</li>
						
						<li :class="pageIndex == totalPage ? 'am-disabled':''" @click="changeIndex(pageIndex + 1)">
							<a href="javascript:void(0);">&raquo;</a>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";

export default {
	name: "HealersView",
	components: { Layout },
	data() {
		return {
			isMobilePhone: isMobilePhone(),
			healerList: [],
			searchKeyword: '',
			pageIndex: 1,
			pageSize: 6,
			total: 0,
			totalPage: 1
		}
	},
	mounted() {
		this.$wxShare();
		this.getHealers();
	},
	methods: {
		// 获取疗愈师列表
		getHealers() {
			// 模拟数据，实际项目中应替换为真实接口
			this.getRequest("/cms/healers/list", {
				'page': this.pageIndex,
				'limit': this.pageSize,
				'name': this.searchKeyword
			}).then(resp => {
				if (resp && resp.code == 200) {
					this.healerList = resp.data.list || [];
					this.healerList.forEach(item => {
						item.tags = item.tags ? item.tags.split(',') : []
					})
					this.total = resp.data.total || 0;
					this.totalPage = resp.data.totalPage || 1;
				} else {
					this.healerList = [];
					this.total = 0;
					this.totalPage = 1;
				}
			})
		},
		searchHealers() {
			this.pageIndex = 1; // 搜索时重置为第一页
			this.getHealers();
		},
		changeIndex(p) {
			if (p < 1) {
				this.pageIndex = 1;
			} else if (p > this.totalPage) {
				this.pageIndex = this.totalPage;
			} else {
				this.pageIndex = p;
				this.getHealers();
			}
		},
		resetFilters() {
			this.searchKeyword = '';
			this.pageIndex = 1;
			this.getHealers();
		},
		viewHealerDetail(id) {
			this.$router.push(`/healer-detail/${id}?from=healers`);
		}
	},
}
</script>

<style scoped>
/* 页面顶部背景区域样式 */
.healers-hero-section {
	height: 400px;
	background: linear-gradient(45deg, #7b4397, #dc2430);
	background-image: url('https://img.freepik.com/free-photo/close-up-beautiful-crystals-arrangement_23-2149129123.jpg');
	background-size: cover;
	background-position: center;
	background-blend-mode: overlay;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	text-align: center;
	margin-bottom: 0;
	width: 100%;
}

.healers-hero-section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(55, 30, 94, 0.7);
}

.hero-content {
	position: relative;
	z-index: 2;
	max-width: 800px;
	padding: 0 20px;
}

.hero-title {
	font-size: 42px;
	font-weight: 700;
	margin-bottom: 20px;
	text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
	animation: fadeInDown 1s ease-out;
}

.hero-subtitle {
	font-size: 18px;
	font-weight: 400;
	margin-bottom: 30px;
	text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
	animation: fadeInUp 1s ease-out;
}

/* 主内容区域样式 */
.healers-section {
	padding: 60px 0;
	/* background-color: #f8f5ff; */
}

/* 搜索和筛选区域样式 */
.healers-search-container {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-bottom: 40px;
	padding: 20px;
	background: white;
	border-radius: 15px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
	position: relative;
	z-index: 2;
}

.healers-search {
	position: relative;
	flex: 1;
	min-width: 250px;
	margin-right: 20px;
}

.healers-search i {
	position: absolute;
	left: 15px;
	top: 50%;
	transform: translateY(-50%);
	color: #7b4397;
}

.healers-search input {
	width: 100%;
	padding: 12px 15px 12px 40px;
	border: 1px solid #ddd;
	border-radius: 25px;
	font-size: 16px;
	transition: all 0.3s;
}

.healers-search input:focus {
	outline: none;
	border-color: #7b4397;
	box-shadow: 0 0 0 3px rgba(123, 67, 151, 0.1);
}

.healers-filter {
	display: flex;
	flex-wrap: wrap;
	gap: 15px;
}

.filter-group {
	position: relative;
}

.filter-icon {
	position: absolute;
	left: 15px;
	top: 50%;
	transform: translateY(-50%);
	color: #7b4397;
	z-index: 1;
}

.healers-filter select {
	padding: 12px 20px 12px 40px;
	border: 1px solid #ddd;
	border-radius: 25px;
	font-size: 16px;
	background-color: white;
	color: #333;
	cursor: pointer;
	transition: all 0.3s;
	appearance: none;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%237b4397' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-position: right 15px center;
	padding-right: 40px;
	min-width: 180px;
}

.healers-filter select:focus {
	outline: none;
	border-color: #7b4397;
	box-shadow: 0 0 0 3px rgba(123, 67, 151, 0.1);
}

/* 列表内容区域样式 */
.healers-list-container {
	position: relative;
	min-height: 500px;
}

/* 空状态展示样式 */
.healers-empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 20px;
	text-align: center;
	background: white;
	border-radius: 15px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.empty-icon {
	color: #ddd6f3;
	margin-bottom: 20px;
	opacity: 0.7;
}

.healers-empty-state h3 {
	font-size: 22px;
	color: #3a2c58;
	margin-bottom: 10px;
}

.healers-empty-state p {
	color: #666;
	margin-bottom: 20px;
}

/* 分页控件样式 */
.healers-pagination {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 50px;
	padding: 20px;
	background: white;
	border-radius: 15px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.pagination-info {
	color: #666;
	font-size: 14px;
}

.pagination-controls {
	display: flex;
	align-items: center;
	gap: 15px;
}

.pagination-btn {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	border: 1px solid #ddd;
	background: white;
	color: #3a2c58;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s;
}

.pagination-btn:hover:not(.disabled) {
	background: #f5f0ff;
	border-color: #7b4397;
	color: #7b4397;
}

.pagination-btn.disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

.pagination-page {
	font-size: 16px;
	color: #3a2c58;
	font-weight: 500;
}

/* 卡片样式（复用IndexView中的样式） */
.crystal-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
	justify-content: center;
}

.crystal-grid-mobile {
	flex-direction: column;
	align-items: center;
}

.crystal-card {
	width: 350px;
	background: #fff;
	border-radius: 15px;
	overflow: hidden;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	cursor: pointer;
	transform: translateY(0);
	position: relative;
}

.crystal-card:hover {
	transform: translateY(-10px);
	box-shadow: 0 15px 30px rgba(86, 70, 128, 0.2);
}

.crystal-card-img {
	height: 230px;
	overflow: hidden;
	position: relative;
}

.crystal-card-img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.6s;
}

.card-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 50%;
	background: linear-gradient(to top, rgba(86, 70, 128, 0.7), transparent);
	opacity: 0;
	transition: all 0.3s;
}

.crystal-card:hover .card-overlay {
	opacity: 1;
}

.crystal-card:hover .crystal-card-img img {
	transform: scale(1.08);
}

.crystal-card-info {
	padding: 25px;
	position: relative;
}

.crystal-card-info h3 {
	font-size: 22px;
	color: #3a2c58;
	margin-bottom: 12px;
	font-weight: 600;
}

.crystal-card-info h3 i {
	color: #7b4397;
	margin-right: 5px;
	font-size: 20px;
}

.crystal-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin-bottom: 15px;
}

.crystal-tags span {
	background: linear-gradient(135deg, #ddd6f3, #faaca8);
	color: #3a2c58;
	font-size: 12px;
	padding: 5px 12px;
	border-radius: 20px;
	font-weight: 500;
}

.crystal-tags span i {
	font-size: 10px;
	margin-right: 3px;
}

.crystal-card-info p {
	color: #666;
	font-size: 15px;
	line-height: 1.6;
	margin-bottom: 20px;
	height: 72px;
	overflow: hidden;
}

.crystal-card-info p i {
	font-size: 16px;
	margin-right: 5px;
	opacity: 0.7;
}

.crystal-card-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.crystal-card-location {
	color: #888;
	font-size: 14px;
}

.crystal-btn {
	background: linear-gradient(135deg, #7b4397, #dc2430);
	color: white;
	border: none;
	padding: 10px 20px;
	border-radius: 25px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s;
	box-shadow: 0 4px 15px rgba(123, 67, 151, 0.3);
}

.crystal-btn:hover {
	background: linear-gradient(135deg, #dc2430, #7b4397);
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(123, 67, 151, 0.4);
}

.crystal-badge {
	position: absolute;
	top: 15px;
	right: 15px;
	background: linear-gradient(135deg, #7b4397, #dc2430);
	color: white;
	padding: 5px 10px;
	border-radius: 25px;
	font-size: 12px;
	font-weight: 500;
	z-index: 2;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.quote-icon {
	color: #ddd6f3;
	font-size: 16px;
	margin-right: 5px;
	opacity: 0.7;
}

/* 动画效果 */
@keyframes fadeInDown {
	from {
		opacity: 0;
		transform: translateY(-30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 装饰元素 */
.decoration-element {
	position: absolute;
	font-size: 120px;
	color: rgba(123, 67, 151, 0.05);
	z-index: 1;
}

.top-left {
	top: 50px;
	left: 0;
}

.bottom-right {
	bottom: 50px;
	right: 0;
}

/* 添加动画效果 */
.fa-spin-pulse {
	animation: spin-pulse 2s infinite alternate;
}

.fa-spin-slow {
	animation: spin 10s linear infinite;
}

@keyframes spin-pulse {
	0% {
		transform: scale(1) rotate(0);
	}
	100% {
		transform: scale(1.1) rotate(15deg);
	}
}

@keyframes spin {
	0% {
		transform: rotate(0);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* 移动端适配 */
@media (max-width: 768px) {
	.healers-hero-section {
		height: 300px;
	}
	
	.hero-title {
		font-size: 32px;
	}
	
	.hero-subtitle {
		font-size: 16px;
	}
	
	.healers-section {
		padding: 40px 0;
	}
	
	.healers-search-container {
		flex-direction: column;
		gap: 20px;
	}
	
	.healers-search {
		margin-right: 0;
		margin-bottom: 15px;
	}
	
	.healers-filter {
		width: 100%;
	}
	
	.healers-filter select {
		flex: 1;
		min-width: 140px;
	}
	
	.healers-pagination {
		flex-direction: column;
		gap: 15px;
	}
	
	.decoration-element {
		font-size: 80px;
	}
}

.course-search {
	margin: 20px 0 30px;
	display: flex;
	justify-content: center;
}

.search-container {
	width: 100%;
	max-width: 500px;
	display: flex;
}

.search-input {
	flex: 1;
	padding: 10px 15px;
	border: 1px solid #ddd;
	border-radius: 4px 0 0 4px;
	font-size: 14px;
}

.search-btn {
	background-color: #516790;
	color: #fff;
	border: none;
	padding: 0 20px;
	border-radius: 0 4px 4px 0;
	cursor: pointer;
	transition: background-color 0.3s;
}

.search-btn:hover {
	background-color: #3e5178;
}
</style> 