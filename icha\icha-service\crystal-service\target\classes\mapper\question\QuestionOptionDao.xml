<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.crystal.service.dao.QuestionOptionDao">

	<!-- 根据包名 模块名 以及类名 生成Mapper XML 配置文件 -->
    <resultMap type="com.crystal.common.model.question.QuestionOptionEntity" id="questionOptionMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="addTime" column="add_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="questionId" column="question_id"/>
        <result property="optionId" column="option_id"/>
        <result property="root" column="root"/>
        <result property="sacral" column="sacral"/>
        <result property="navel" column="navel"/>
        <result property="heart" column="heart"/>
        <result property="throat" column="throat"/>
        <result property="thirdEye" column="third_eye"/>
        <result property="crown" column="crown"/>
    </resultMap>


</mapper>
