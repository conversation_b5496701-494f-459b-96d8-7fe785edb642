// 外部已通过CDN引入Vue和Vant
// import Vue from 'vue'
// import Vant from 'vant'
// import 'vant/lib/index.css'
import App from './App.vue'
import router from './router'
import './assets/css/style.css'
import {getRequest,postRequest,postRequestParams} from "@/api/api";
import wxShare from './utils/wxShare'
import Message from './utils/message'

// 不需要Vue.use(Vant)，已通过CDN全局引入

Vue.prototype.getRequest = getRequest;
Vue.prototype.postRequest = postRequest;
Vue.prototype.postRequestParams = postRequestParams;
Vue.prototype.$toast = vant.Toast
Vue.prototype.$wxShare = wxShare
Vue.prototype.$message = Message

Vue.config.productionTip = false

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')

vant.Toast.setDefaultOptions({position:'bottom'})