<template>
	<Layout>
		<div class="layout-container" style="width: 100%">
			<!-- 美化后的页面头部 -->
			<div class="hero-header-section join-header">
				<div class="hero-content">
					<h1 class="hero-title"><i class="fas fa-handshake fa-spin-pulse"></i> 加入我们</h1>
					<p class="hero-subtitle">共同探索水晶能量的奥秘，成为专业疗愈社区的一员</p>
				</div>
			</div>

			<!-- 加入介绍 -->
			<section class="join-section intro-section">
				<div class="content-wrapper">
					<h2 class="section-title">为什么加入ICHA</h2>
					<div class="join-benefits">
						<div class="benefit-card">
							<div class="benefit-icon">
								<i class="fas fa-globe-asia"></i>
							</div>
							<h3>全球网络</h3>
							<p>成为ICHA一员，您将加入遍布全球的水晶疗愈专业网络，与来自不同文化和背景的同行建立联系。</p>
						</div>
						<div class="benefit-card">
							<div class="benefit-icon">
								<i class="fas fa-certificate"></i>
							</div>
							<h3>专业认证</h3>
							<p>获得国际认可的专业资格证书，提升您在水晶疗愈领域的专业地位与信誉。</p>
						</div>
						<div class="benefit-card">
							<div class="benefit-icon">
								<i class="fas fa-graduation-cap"></i>
							</div>
							<h3>持续学习</h3>
							<p>优先参与高级工作坊、大师班和专业培训，不断提升您的水晶疗愈技能和知识。</p>
						</div>
						<div class="benefit-card">
							<div class="benefit-icon">
								<i class="fas fa-gem"></i>
							</div>
							<h3>资源共享</h3>
							<p>获取专属教材、研究资料和实用工具，助力您的个人发展和专业实践。</p>
						</div>
					</div>
				</div>
			</section>

			<!-- 会员类型 -->
			<!-- <section class="join-section membership-section">
				<div class="content-wrapper">
					<h2 class="section-title">会员类型</h2>
					<div class="membership-types">
						<div class="membership-card">
							<div class="membership-header">
								<i class="fas fa-user"></i>
								<h3>个人会员</h3>
							</div>
							<div class="membership-content">
								<ul>
									<li><i class="fas fa-check"></i> 水晶爱好者</li>
									<li><i class="fas fa-check"></i> 初学疗愈师</li>
									<li><i class="fas fa-check"></i> 能量工作者</li>
									<li><i class="fas fa-check"></i> 身心灵从业者</li>
								</ul>
							</div>
						</div>
						<div class="membership-card">
							<div class="membership-header">
								<i class="fas fa-user-tie"></i>
								<h3>专业会员</h3>
							</div>
							<div class="membership-content">
								<ul>
									<li><i class="fas fa-check"></i> 认证水晶疗愈师</li>
									<li><i class="fas fa-check"></i> 授权讲师</li>
									<li><i class="fas fa-check"></i> 能量疗愈专家</li>
									<li><i class="fas fa-check"></i> 自然医学从业者</li>
								</ul>
							</div>
						</div>
						<div class="membership-card">
							<div class="membership-header">
								<i class="fas fa-building"></i>
								<h3>机构会员</h3>
							</div>
							<div class="membership-content">
								<ul>
									<li><i class="fas fa-check"></i> 教育培训机构</li>
									<li><i class="fas fa-check"></i> 水晶品牌与经销商</li>
									<li><i class="fas fa-check"></i> 疗愈中心</li>
									<li><i class="fas fa-check"></i> 研究组织</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</section> -->

			<!-- 加入流程 -->
			<!-- <section class="join-section process-section">
				<div class="content-wrapper">
					<h2 class="section-title">加入流程</h2>
					<div class="join-process">
						<div class="process-step">
							<div class="step-number">1</div>
							<div class="step-content">
								<h3><i class="fas fa-wpforms"></i> 提交申请</h3>
								<p>填写完整的会员申请表，提供相关资质证明和个人/机构介绍。</p>
							</div>
						</div>
						<div class="process-step">
							<div class="step-number">2</div>
							<div class="step-content">
								<h3><i class="fas fa-search"></i> 资格审核</h3>
								<p>协会委员会将审核您的申请，确认您的专业背景和资质。</p>
							</div>
						</div>
						<div class="process-step">
							<div class="step-number">3</div>
							<div class="step-content">
								<h3><i class="fas fa-comment-dots"></i> 面谈交流</h3>
								<p>通过线上或线下方式与协会代表进行简短面谈，了解彼此期望。</p>
							</div>
						</div>
						<div class="process-step">
							<div class="step-number">4</div>
							<div class="step-content">
								<h3><i class="fas fa-handshake"></i> 正式加入</h3>
								<p>完成会费支付，签署会员协议，获得会员资格与专属权益。</p>
							</div>
						</div>
					</div>
				</div>
			</section> -->

			<!-- 申请表单 -->
			<!-- <section class="join-section application-section">
				<div class="content-wrapper">
					<h2 class="section-title">申请加入</h2>
					<div class="application-form-container">
						<form class="application-form" @submit.prevent="submitApplication">
							<div class="form-group">
								<label><i class="fas fa-user"></i> 姓名</label>
								<input type="text" v-model="formData.name" required placeholder="请输入您的姓名">
							</div>
							<div class="form-group">
								<label><i class="fas fa-envelope"></i> 电子邮箱</label>
								<input type="email" v-model="formData.email" required placeholder="请输入您的电子邮箱">
							</div>
							<div class="form-group">
								<label><i class="fas fa-phone"></i> 联系电话</label>
								<input type="tel" v-model="formData.phone" required placeholder="请输入您的联系电话">
							</div>
							<div class="form-group">
								<label><i class="fas fa-users"></i> 会员类型</label>
								<select v-model="formData.memberType" required>
									<option value="">请选择会员类型</option>
									<option value="individual">个人会员</option>
									<option value="professional">专业会员</option>
									<option value="institutional">机构会员</option>
								</select>
							</div>
							<div class="form-group full-width">
								<label><i class="fas fa-briefcase"></i> 专业背景</label>
								<textarea v-model="formData.background" rows="3" placeholder="请简述您的专业背景与相关经验"></textarea>
							</div>
							<div class="form-group full-width">
								<label><i class="fas fa-comment"></i> 加入意向</label>
								<textarea v-model="formData.intention" rows="3" placeholder="请分享您希望加入ICHA的原因和期望"></textarea>
							</div>
							<div class="form-group submit-group">
								<button type="submit" class="submit-btn">提交申请</button>
							</div>
						</form>
					</div>
				</div>
			</section> -->

			<!-- 联系信息 -->
			<section class="join-section contact-section">
				<div class="content-wrapper">
					<h2 class="section-title">联系我们</h2>
					<div class="contact-methods">
						<div class="contact-method">
							<i class="fas fa-phone-alt"></i>
							<h3>电话咨询</h3>
							<p>{{ aboutmobile }}</p>
						</div>
						<div class="contact-method">
							<i class="fas fa-envelope-open-text"></i>
							<h3>电子邮件</h3>
							<p>{{ aboutemail }}</p>
						</div>
						<div class="contact-method">
							<i class="fas fa-map-marker-alt"></i>
							<h3>总部地址</h3>
							<p>{{ aboutaddress }}</p>
						</div>
						<div class="contact-method">
							<i class="fas fa-comments"></i>
							<h3>咨询时间</h3>
							<p>{{ aboutkefutime }}</p>
						</div>
					</div>
				</div>
			</section>
		</div>
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import '../assets/css/common-headers.css'; // 导入头部共用样式

export default {
	name: "JoinView",
	components: { Layout },
	data() {
		return {
			aboutkefutime: '',
			aboutaddress: '',
			aboutemail: '',
			aboutmobile: '',
			isMobilePhone: isMobilePhone(),
			companyInfo: {},
			designers: [],
			formData: {
				name: '',
				email: '',
				phone: '',
				memberType: '',
				background: '',
				intention: ''
			}
		}
	},
	mounted() {
		this.$wxShare();
		this.getConfigJoin()
	},
	methods: {
		getConfigJoin() {
			this.getRequest("/cms/config/join").then(resp => {
				if (resp && resp.code == 200) {
					this.aboutkefutime = resp.data.aboutkefutime;
					this.aboutaddress = resp.data.aboutaddress;
					this.aboutemail = resp.data.aboutemail;
					this.aboutmobile = resp.data.aboutmobile;
				}
			})
		},
		submitApplication() {
			// 提交申请逻辑实现
			console.log('提交的表单数据:', this.formData);
			this.$message.success('申请已成功提交，我们将尽快与您联系！');
			// 重置表单
			this.formData = {
				name: '',
				email: '',
				phone: '',
				memberType: '',
				background: '',
				intention: ''
			};
		}
	}
}
</script>

<style scoped>
/* 美化后的页面头部样式 */
.join-header {
	background-image: url('https://img.freepik.com/free-photo/esoteric-concept-with-spiritual-crystals_23-2149320909.jpg') !important;
}

/* 页面整体样式 */
.content-wrapper {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
}

.section-title {
	text-align: center;
	font-size: 32px;
	color: #333;
	margin-bottom: 40px;
	position: relative;
	font-weight: 600;
}

.section-title:after {
	content: '';
	display: block;
	width: 60px;
	height: 3px;
	background-color: #516790;
	margin: 15px auto 0;
}

/* 加入介绍部分 */
.join-section {
	padding: 60px 0;
}

.join-section:nth-child(even) {
	background-color: #f9f9f9;
}

.join-benefits {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 30px;
}

.benefit-card {
	flex: 1;
	min-width: 250px;
	max-width: 300px;
	padding: 25px;
	background-color: #fff;
	border-radius: 8px;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
	text-align: center;
	transition: transform 0.3s, box-shadow 0.3s;
}

.benefit-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
	width: 80px;
	height: 80px;
	margin: 0 auto 20px;
	background-color: #516790;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.benefit-icon i {
	font-size: 36px;
	color: white;
}

.benefit-card h3 {
	font-size: 20px;
	color: #333;
	margin-bottom: 15px;
}

.benefit-card p {
	color: #666;
	line-height: 1.6;
}

/* 会员类型部分 */
.membership-types {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 30px;
}

.membership-card {
	flex: 1;
	min-width: 250px;
	max-width: 350px;
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
	background-color: #fff;
}

.membership-header {
	background-color: #516790;
	color: white;
	padding: 20px;
	text-align: center;
}

.membership-header i {
	font-size: 32px;
	margin-bottom: 10px;
}

.membership-header h3 {
	font-size: 22px;
	margin: 0;
}

.membership-content {
	padding: 25px;
}

.membership-content ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.membership-content li {
	margin-bottom: 12px;
	padding-left: 28px;
	position: relative;
	color: #555;
}

.membership-content li i {
	position: absolute;
	left: 0;
	top: 3px;
	color: #516790;
}

/* 加入流程部分 */
.join-process {
	max-width: 800px;
	margin: 0 auto;
}

.process-step {
	display: flex;
	margin-bottom: 30px;
	align-items: center;
}

.step-number {
	width: 50px;
	height: 50px;
	background-color: #516790;
	border-radius: 50%;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24px;
	font-weight: bold;
	margin-right: 20px;
	flex-shrink: 0;
}

.step-content {
	flex: 1;
	background-color: #fff;
	padding: 20px;
	border-radius: 8px;
	box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.step-content h3 {
	margin-top: 0;
	color: #333;
	font-size: 18px;
	display: flex;
	align-items: center;
}

.step-content h3 i {
	margin-right: 10px;
	color: #516790;
}

.step-content p {
	margin-bottom: 0;
	color: #666;
}

/* 申请表单部分 */
.application-form-container {
	max-width: 800px;
	margin: 0 auto;
	background-color: #fff;
	padding: 30px;
	border-radius: 10px;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.application-form {
	display: flex;
	flex-wrap: wrap;
	gap: 20px;
}

.form-group {
	flex: 1 0 calc(50% - 10px);
}

.full-width {
	flex: 1 0 100%;
}

.form-group label {
	display: block;
	margin-bottom: 8px;
	font-weight: 500;
	color: #333;
}

.form-group label i {
	margin-right: 8px;
	color: #516790;
}

.form-group input,
.form-group select,
.form-group textarea {
	width: 100%;
	padding: 12px 15px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 16px;
	transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
	border-color: #516790;
	outline: none;
}

.submit-group {
	flex: 1 0 100%;
	text-align: center;
	margin-top: 10px;
}

.submit-btn {
	background-color: #516790;
	color: white;
	border: none;
	padding: 12px 40px;
	font-size: 16px;
	border-radius: 4px;
	cursor: pointer;
	transition: background-color 0.3s;
}

.submit-btn:hover {
	background-color: #405580;
}

/* 联系信息部分 */
.contact-methods {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 30px;
}

.contact-method {
	flex: 1;
	min-width: 200px;
	max-width: 250px;
	text-align: center;
	padding: 25px;
	background-color: #fff;
	border-radius: 8px;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.contact-method i {
	font-size: 40px;
	color: #516790;
	margin-bottom: 15px;
}

.contact-method h3 {
	font-size: 18px;
	color: #333;
	margin-bottom: 10px;
}

.contact-method p {
	color: #666;
}

/* 响应式布局 */
@media (max-width: 992px) {
	.benefit-card,
	.membership-card,
	.contact-method {
		min-width: 200px;
	}
}

@media (max-width: 768px) {
	.section-title {
		font-size: 28px;
	}
	
	.join-benefits,
	.membership-types,
	.contact-methods {
		gap: 20px;
	}
	
	.form-group {
		flex: 1 0 100%;
	}
}

@media (max-width: 576px) {
	.join-section {
		padding: 40px 0;
	}
	
	.section-title {
		font-size: 24px;
		margin-bottom: 30px;
	}
	
	.benefit-card,
	.membership-card,
	.contact-method {
		min-width: 100%;
	}
	
	.process-step {
		flex-direction: column;
		text-align: center;
	}
	
	.step-number {
		margin-right: 0;
		margin-bottom: 15px;
	}
	
	.application-form-container {
		padding: 20px;
	}
}
</style>
