{"ast": null, "code": "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone } from \"@/utils/index\";\nimport '../assets/css/common-headers.css'; // 导入头部共用样式\n\nexport default {\n  name: \"WorkshopView\",\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      isMobilePhone: isMobilePhone(),\n      workshopList: [],\n      searchKeyword: '',\n      pageIndex: 1,\n      pageSize: 6,\n      total: 0,\n      totalPage: 1,\n      // 报名相关\n      showEnrollPopup: false,\n      currentWorkshop: {},\n      enrollForm: {\n        name: '',\n        phone: '',\n        number: 1,\n        message: ''\n      },\n      submitting: false,\n      userInfo: {},\n      // 我的报名相关\n      showMyEnrollPopup: false,\n      myEnrollList: []\n    };\n  },\n  mounted() {\n    this.$wxShare();\n    this.getWorkshops();\n  },\n  methods: {\n    // 获取工作坊列表\n    getWorkshops() {\n      this.getRequest(\"/cms/workshop/list\", {\n        'page': this.pageIndex,\n        'limit': this.pageSize,\n        'title': this.searchKeyword\n      }).then(resp => {\n        if (resp && resp.code == 200) {\n          this.workshopList = resp.data.list || [];\n          this.total = resp.data.total || 0;\n          this.totalPage = resp.data.totalPage || 1;\n        } else {\n          this.workshopList = [];\n          this.total = 0;\n          this.totalPage = 1;\n        }\n      });\n    },\n    // 搜索工作坊\n    searchWorkshops() {\n      this.pageIndex = 1;\n      this.getWorkshops();\n    },\n    // 切换页码\n    changeIndex(index) {\n      if (index < 1 || index > this.totalPage) {\n        return;\n      }\n      this.pageIndex = index;\n      this.getWorkshops();\n    },\n    // 显示报名表单\n    showEnrollForm(workshop) {\n      this.getRequest(\"/user\").then(resp => {\n        if (resp && resp.code == 200) {\n          this.userInfo = resp.data;\n          this.currentWorkshop = workshop;\n          this.showEnrollPopup = true;\n          // 重置表单\n          this.enrollForm = {\n            name: this.userInfo.nickname,\n            phone: this.userInfo.phone,\n            number: 1,\n            message: ''\n          };\n          sessionStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\n          localStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\n        }\n      });\n    },\n    // 验证手机号\n    validatePhone(val) {\n      return /^1[3-9]\\d{9}$/.test(val);\n    },\n    // 验证邮箱\n    validateEmail(val) {\n      return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/.test(val);\n    },\n    // 提交报名表单\n    submitEnrollForm() {\n      this.submitting = true;\n\n      // 构造提交数据\n      const formData = {\n        cmsWorkshopId: this.currentWorkshop.id,\n        ...this.enrollForm\n      };\n\n      // 实际项目中替换为真实接口\n      this.postRequest(\"/cmsAttend/workshop\", formData).then(resp => {\n        this.submitting = false;\n        console.log(resp);\n        if (resp && resp.code == 200) {\n          this.$toast.success('报名成功！');\n          this.showEnrollPopup = false;\n          // 更新工作坊剩余名额\n          this.getWorkshops();\n        } else {\n          this.$toast.fail(resp.message || '报名失败，请稍后再试');\n        }\n      });\n    },\n    // 显示我的报名记录\n    showMyEnrolls() {\n      this.getRequest(\"/cmsAttend/myList\").then(resp => {\n        if (resp && resp.code == 200) {\n          this.myEnrollList = resp.data || [];\n          this.showMyEnrollPopup = true;\n        } else {\n          this.$toast.fail(resp.message || '获取报名记录失败');\n        }\n      });\n    },\n    // 格式化日期\n    formatDate(timestamp) {\n      if (!timestamp) return '';\n      const date = new Date(timestamp);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n    },\n    // 获取状态对应的类名\n    getStatusClass(status) {\n      switch (status) {\n        case 0:\n          return 'status-pending';\n        case 1:\n          return 'status-confirmed';\n        case 2:\n          return 'status-completed';\n        case -1:\n          return 'status-cancelled';\n        default:\n          return '';\n      }\n    },\n    // 获取状态对应的文本\n    getStatusText(status) {\n      switch (status) {\n        case 0:\n          return '待确认';\n        case 1:\n          return '已确认';\n        case 2:\n          return '已完成';\n        case -1:\n          return '已取消';\n        default:\n          return '未知状态';\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "name", "components", "data", "workshopList", "searchKeyword", "pageIndex", "pageSize", "total", "totalPage", "showEnrollPopup", "currentWorkshop", "enrollForm", "phone", "number", "message", "submitting", "userInfo", "showMyEnrollPopup", "myEnrollList", "mounted", "$wxShare", "getWorkshops", "methods", "getRequest", "then", "resp", "code", "list", "searchWorkshops", "changeIndex", "index", "showEnrollForm", "workshop", "nickname", "sessionStorage", "setItem", "JSON", "stringify", "localStorage", "validatePhone", "val", "test", "validateEmail", "submitEnrollForm", "formData", "cmsWorkshopId", "id", "postRequest", "console", "log", "$toast", "success", "fail", "showMyEnrolls", "formatDate", "timestamp", "date", "Date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getStatusClass", "status", "getStatusText"], "sources": ["src/views/workshop.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t<div class=\"layout-container\" style=\"width: 100%\">\r\n\t\t\t<!-- 美化后的页面头部 -->\r\n\t\t\t<div class=\"hero-header-section workshop-header\">\r\n\t\t\t\t<div class=\"hero-content\">\r\n\t\t\t\t\t<h1 class=\"hero-title\"><i class=\"fa fa-diamond fa-spin-pulse\"></i> 工作坊</h1>\r\n\t\t\t\t\t<p class=\"hero-subtitle\">专业体验，深入感知水晶能量的奇妙魅力</p>\r\n\t\t\t\t\t<button class=\"my-enroll-btn\" @click=\"showMyEnrolls\"><i class=\"fa fa-list-ul\"></i> 我的报名</button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<!-- 搜索区域 -->\r\n\t\t<div class=\"section\">\r\n\t\t\t<div class=\"container\" style=\"max-width: 1160px\">\r\n\t\t\t\t<div class=\"workshop-search\">\r\n\t\t\t\t\t<div class=\"search-container\">\r\n\t\t\t\t\t\t<input type=\"text\" v-model=\"searchKeyword\" placeholder=\"搜索工作坊名称或关键词\" class=\"search-input\" />\r\n\t\t\t\t\t\t<button class=\"search-btn\" @click=\"searchWorkshops\">搜索</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<!-- 工作坊列表 -->\r\n\t\t\t\t<div class=\"crystal-workshop-container\">\r\n\t\t\t\t\t<div class=\"crystal-grid\" :class=\"{ 'crystal-grid-mobile': isMobilePhone }\">\r\n\t\t\t\t\t\t<div v-for=\"(workshop, index) in workshopList\" :key=\"index\" class=\"crystal-workshop-card\">\r\n\t\t\t\t\t\t\t<div class=\"crystal-workshop-img\">\r\n\t\t\t\t\t\t\t\t<img :src=\"workshop.avatar\" alt=\"\">\r\n\t\t\t\t\t\t\t\t<div class=\"workshop-overlay\"></div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"crystal-workshop-info\">\r\n\t\t\t\t\t\t\t\t<h3>{{ workshop.title }}</h3>\r\n\t\t\t\t\t\t\t\t<p>{{ workshop.brief }}</p>\r\n\t\t\t\t\t\t\t\t<div class=\"crystal-workshop-meta\">\r\n\t\t\t\t\t\t\t\t\t<!-- <span><i class=\"fa fa-calendar\"></i> {{workshop.startTime}}</span> -->\r\n\t\t\t\t\t\t\t\t\t<span><i class=\"fa fa-map-marker\"></i> {{ workshop.location }}</span>\r\n\t\t\t\t\t\t\t\t\t<!-- <span><i class=\"fa fa-users\"></i> 剩余名额: {{workshop.number - workshop.readyNumber}}</span> -->\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"crystal-workshop-footer\">\r\n\t\t\t\t\t\t\t\t\t<!-- <span class=\"crystal-workshop-price\">¥{{workshop.price}}元</span> -->\r\n\t\t\t\t\t\t\t\t\t<span class=\"crystal-workshop-price\">剩余名额: {{ workshop.number -\r\n\t\t\t\t\t\t\t\t\t\tworkshop.readyNumber}}</span>\r\n\t\t\t\t\t\t\t\t\t<button class=\"crystal-btn pulse-btn\"\r\n\t\t\t\t\t\t\t\t\t\t@click=\"showEnrollForm(workshop)\">立即报名</button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<!-- 无数据提示 -->\r\n\t\t\t\t\t<div v-if=\"workshopList.length == 0\" class=\"no-data\">\r\n\t\t\t\t\t\t<p>暂无工作坊数据</p>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<!-- 分页 -->\r\n\t\t\t\t\t<ul class=\"am-pagination\" style=\"text-align: center;\" v-if=\"total > 0\">\r\n\t\t\t\t\t\t<li :class=\"pageIndex == 1 ? 'am-disabled' : ''\" @click=\"changeIndex(pageIndex - 1)\">\r\n\t\t\t\t\t\t\t<a href=\"javascript:void(0);\">&laquo;</a>\r\n\t\t\t\t\t\t</li>\r\n\r\n\t\t\t\t\t\t<li v-for=\"p in totalPage\" :key=\"p\" @click=\"changeIndex(p)\"\r\n\t\t\t\t\t\t\t:class=\"pageIndex == p ? 'am-active' : ''\">\r\n\t\t\t\t\t\t\t<a href=\"javascript:void(0);\">{{ p }}</a>\r\n\t\t\t\t\t\t</li>\r\n\r\n\t\t\t\t\t\t<li :class=\"pageIndex == totalPage ? 'am-disabled' : ''\" @click=\"changeIndex(pageIndex + 1)\">\r\n\t\t\t\t\t\t\t<a href=\"javascript:void(0);\">&raquo;</a>\r\n\t\t\t\t\t\t</li>\r\n\t\t\t\t\t</ul>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<!-- 报名表单弹窗 -->\r\n\t\t<van-popup v-model=\"showEnrollPopup\" round closeable close-icon=\"close\" position=\"center\"\r\n\t\t\t:style=\"{ width: isMobilePhone ? '90%' : '500px' }\">\r\n\t\t\t<div class=\"enroll-form\">\r\n\t\t\t\t<h2 class=\"enroll-title\">{{ currentWorkshop.title }} - 报名表</h2>\r\n\t\t\t\t<van-form @submit=\"submitEnrollForm\">\r\n\t\t\t\t\t<van-field v-model=\"enrollForm.name\" name=\"name\" label=\"姓名\" placeholder=\"请输入姓名\"\r\n\t\t\t\t\t\t:rules=\"[{ required: true, message: '请填写姓名' }]\" />\r\n\t\t\t\t\t<van-field v-model=\"enrollForm.phone\" name=\"phone\" label=\"手机号码\" placeholder=\"请输入手机号码\" :rules=\"[\r\n\t\t\t\t\t\t{ required: true, message: '请填写手机号码' },\r\n\t\t\t\t\t\t{ validator: validatePhone, message: '手机号码格式不正确' }\r\n\t\t\t\t\t]\" />\r\n\t\t\t\t\t<van-field name=\"count\" label=\"报名人数\">\r\n\t\t\t\t\t\t<template #input>\r\n\t\t\t\t\t\t\t<van-stepper v-model=\"enrollForm.number\" min=\"1\"\r\n\t\t\t\t\t\t\t\t:max=\"currentWorkshop.number - currentWorkshop.readyNumber\" />\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</van-field>\r\n\t\t\t\t\t<van-field v-model=\"enrollForm.message\" name=\"message\" label=\"留言\" type=\"textarea\"\r\n\t\t\t\t\t\tplaceholder=\"有什么想告诉我们的？\" rows=\"2\" autosize />\r\n\t\t\t\t\t<div style=\"margin: 16px;\">\r\n\t\t\t\t\t\t<van-button round block type=\"primary\" native-type=\"submit\" :loading=\"submitting\">\r\n\t\t\t\t\t\t\t提交报名\r\n\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</van-form>\r\n\t\t\t</div>\r\n\t\t</van-popup>\r\n\r\n\t\t<!-- 我的报名记录弹窗 -->\r\n\t\t<van-popup v-model=\"showMyEnrollPopup\" round closeable close-icon=\"close\" position=\"center\"\r\n\t\t\t:style=\"{ width: isMobilePhone ? '90%' : '600px' }\">\r\n\t\t\t<div class=\"my-enroll-list\">\r\n\t\t\t\t<h2 class=\"enroll-title\">我的工作坊报名记录</h2>\r\n\t\t\t\t\r\n\t\t\t\t<div v-if=\"myEnrollList.length > 0\" class=\"enroll-records\">\r\n\t\t\t\t\t<div v-for=\"(item, index) in myEnrollList\" :key=\"index\" class=\"enroll-record-item\">\r\n\t\t\t\t\t\t<div class=\"record-workshop-info\">\r\n\t\t\t\t\t\t\t<h3>{{ item.cmsWorkshopName }}</h3>\r\n\t\t\t\t\t\t\t<!-- 姓名，联系方式 -->\r\n\t\t\t\t\t\t\t<p><i class=\"fa fa-user\"></i> 姓名: {{ item.name }}</p>\r\n\t\t\t\t\t\t\t<p><i class=\"fa fa-phone\"></i> 联系方式: {{ item.phone }}</p>\r\n\t\t\t\t\t\t\t<p class=\"record-date\"><i class=\"fa fa-calendar\"></i> 报名时间: {{ formatDate(item.addTime) }}</p>\r\n\t\t\t\t\t\t\t<!-- <p><i class=\"fa fa-map-marker\"></i> 地点: {{ item.workshop.location }}</p> -->\r\n\t\t\t\t\t\t\t<p><i class=\"fa fa-users\"></i> 报名人数: {{ item.number }}人</p>\r\n\t\t\t\t\t\t\t<p v-if=\"item.message\"><i class=\"fa fa-comment\"></i> 留言: {{ item.message }}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div v-else class=\"no-data\">\r\n\t\t\t\t\t<p>暂无报名记录</p>\r\n\t\t\t\t</div>\r\n\t\t\t\t\r\n\t\t\t\t<div class=\"popup-footer\">\r\n\t\t\t\t\t<van-button round block type=\"primary\" @click=\"showMyEnrollPopup = false\">关闭</van-button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</van-popup>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport '../assets/css/common-headers.css'; // 导入头部共用样式\r\n\r\nexport default {\r\n\tname: \"WorkshopView\",\r\n\tcomponents: { Layout },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tworkshopList: [],\r\n\t\t\tsearchKeyword: '',\r\n\t\t\tpageIndex: 1,\r\n\t\t\tpageSize: 6,\r\n\t\t\ttotal: 0,\r\n\t\t\ttotalPage: 1,\r\n\t\t\t// 报名相关\r\n\t\t\tshowEnrollPopup: false,\r\n\t\t\tcurrentWorkshop: {},\r\n\t\t\tenrollForm: {\r\n\t\t\t\tname: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tnumber: 1,\r\n\t\t\t\tmessage: ''\r\n\t\t\t},\r\n\t\t\tsubmitting: false,\r\n\t\t\tuserInfo: {},\r\n\t\t\t// 我的报名相关\r\n\t\t\tshowMyEnrollPopup: false,\r\n\t\t\tmyEnrollList: []\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$wxShare();\r\n\t\tthis.getWorkshops();\r\n\t},\r\n\tmethods: {\r\n\t\t// 获取工作坊列表\r\n\t\tgetWorkshops() {\r\n\t\t\tthis.getRequest(\"/cms/workshop/list\", {\r\n\t\t\t\t'page': this.pageIndex,\r\n\t\t\t\t'limit': this.pageSize,\r\n\t\t\t\t'title': this.searchKeyword\r\n\t\t\t}).then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.workshopList = resp.data.list || [];\r\n\t\t\t\t\tthis.total = resp.data.total || 0;\r\n\t\t\t\t\tthis.totalPage = resp.data.totalPage || 1;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.workshopList = [];\r\n\t\t\t\t\tthis.total = 0;\r\n\t\t\t\t\tthis.totalPage = 1;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 搜索工作坊\r\n\t\tsearchWorkshops() {\r\n\t\t\tthis.pageIndex = 1;\r\n\t\t\tthis.getWorkshops();\r\n\t\t},\r\n\r\n\t\t// 切换页码\r\n\t\tchangeIndex(index) {\r\n\t\t\tif (index < 1 || index > this.totalPage) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.pageIndex = index;\r\n\t\t\tthis.getWorkshops();\r\n\t\t},\r\n\r\n\t\t// 显示报名表单\r\n\t\tshowEnrollForm(workshop) {\r\n\t\t\tthis.getRequest(\"/user\").then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.userInfo = resp.data;\r\n\t\t\t\t\tthis.currentWorkshop = workshop;\r\n\t\t\t\t\tthis.showEnrollPopup = true;\r\n\t\t\t\t\t// 重置表单\r\n\t\t\t\t\tthis.enrollForm = {\r\n\t\t\t\t\t\tname: this.userInfo.nickname,\r\n\t\t\t\t\t\tphone: this.userInfo.phone,\r\n\t\t\t\t\t\tnumber: 1,\r\n\t\t\t\t\t\tmessage: ''\r\n\t\t\t\t\t};\r\n\t\t\t\t\tsessionStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\r\n\t\t\t\t\tlocalStorage.setItem(\"userInfo\", JSON.stringify(resp.data));\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 验证手机号\r\n\t\tvalidatePhone(val) {\r\n\t\t\treturn /^1[3-9]\\d{9}$/.test(val);\r\n\t\t},\r\n\r\n\t\t// 验证邮箱\r\n\t\tvalidateEmail(val) {\r\n\t\t\treturn /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/.test(val);\r\n\t\t},\r\n\r\n\t\t// 提交报名表单\r\n\t\tsubmitEnrollForm() {\r\n\t\t\tthis.submitting = true;\r\n\r\n\t\t\t// 构造提交数据\r\n\t\t\tconst formData = {\r\n\t\t\t\tcmsWorkshopId: this.currentWorkshop.id,\r\n\t\t\t\t...this.enrollForm\r\n\t\t\t};\r\n\r\n\t\t\t// 实际项目中替换为真实接口\r\n\t\t\tthis.postRequest(\"/cmsAttend/workshop\", formData).then(resp => {\r\n\t\t\t  this.submitting = false;\r\n\t\t\t  console.log(resp);\r\n\t\t\t  if (resp && resp.code == 200) {\r\n\t\t\t    this.$toast.success('报名成功！');\r\n\t\t\t    this.showEnrollPopup = false;\r\n\t\t\t    // 更新工作坊剩余名额\r\n\t\t\t    this.getWorkshops();\r\n\t\t\t  } else {\r\n\t\t\t    this.$toast.fail(resp.message || '报名失败，请稍后再试');\r\n\t\t\t  }\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 显示我的报名记录\r\n\t\tshowMyEnrolls() {\r\n\t\t\tthis.getRequest(\"/cmsAttend/myList\").then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.myEnrollList = resp.data || [];\r\n\t\t\t\t\tthis.showMyEnrollPopup = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$toast.fail(resp.message || '获取报名记录失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 格式化日期\r\n\t\tformatDate(timestamp) {\r\n\t\t\tif (!timestamp) return '';\r\n\t\t\tconst date = new Date(timestamp);\r\n\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\r\n\t\t},\r\n\r\n\t\t// 获取状态对应的类名\r\n\t\tgetStatusClass(status) {\r\n\t\t\tswitch (status) {\r\n\t\t\t\tcase 0: return 'status-pending';\r\n\t\t\t\tcase 1: return 'status-confirmed';\r\n\t\t\t\tcase 2: return 'status-completed';\r\n\t\t\t\tcase -1: return 'status-cancelled';\r\n\t\t\t\tdefault: return '';\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 获取状态对应的文本\r\n\t\tgetStatusText(status) {\r\n\t\t\tswitch (status) {\r\n\t\t\t\tcase 0: return '待确认';\r\n\t\t\t\tcase 1: return '已确认';\r\n\t\t\t\tcase 2: return '已完成';\r\n\t\t\t\tcase -1: return '已取消';\r\n\t\t\t\tdefault: return '未知状态';\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 美化后的页面头部样式 */\r\n.workshop-header {\r\n\tbackground-image: url('https://img.freepik.com/free-photo/watercolor-pastel-texture-background_53876-98173.jpg') !important;\r\n}\r\n\r\n/* 我的报名按钮样式 */\r\n.my-enroll-btn {\r\n\tpadding: 10px 20px;\r\n\tbackground-color: #dc2430;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tborder-radius: 25px;\r\n\tfont-size: 16px;\r\n\tfont-weight: 500;\r\n\tcursor: pointer;\r\n\tmargin-top: 15px;\r\n\ttransition: all 0.3s ease;\r\n\tbox-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\r\n\tdisplay: inline-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.my-enroll-btn i {\r\n\tmargin-right: 8px;\r\n}\r\n\r\n.my-enroll-btn:hover {\r\n\tbackground-color: rgba(86, 70, 128, 1);\r\n\tbox-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);\r\n\ttransform: translateY(-2px);\r\n}\r\n\r\n.my-enroll-btn:active {\r\n\ttransform: translateY(0);\r\n\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 工作坊列表样式 */\r\n.workshop-search {\r\n\tmargin-bottom: 30px;\r\n}\r\n\r\n.search-container {\r\n\tdisplay: flex;\r\n\tmax-width: 600px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n.search-input {\r\n\tflex: 1;\r\n\tpadding: 12px 15px;\r\n\tborder: 1px solid #ddd;\r\n\tborder-radius: 4px 0 0 4px;\r\n\tfont-size: 16px;\r\n}\r\n\r\n.search-btn {\r\n\tpadding: 0 20px;\r\n\tbackground-color: #564680;\r\n\tborder: none;\r\n\tcolor: white;\r\n\tborder-radius: 0 4px 4px 0;\r\n\tcursor: pointer;\r\n\tfont-size: 16px;\r\n\ttransition: background-color 0.3s;\r\n}\r\n\r\n.search-btn:hover {\r\n\tbackground-color: #483c6f;\r\n}\r\n\r\n/* 我的报名记录样式 */\r\n.my-enroll-list {\r\n\tpadding: 20px;\r\n}\r\n\r\n.enroll-records {\r\n\tmax-height: 60vh;\r\n\toverflow-y: auto;\r\n\tmargin-bottom: 20px;\r\n\tpadding-right: 10px;\r\n}\r\n\r\n.enroll-record-item {\r\n\tbackground-color: #f9f9f9;\r\n\tborder-radius: 8px;\r\n\tpadding: 15px;\r\n\tmargin-bottom: 15px;\r\n\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.enroll-record-item:hover {\r\n\tbox-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\r\n\ttransform: translateY(-2px);\r\n}\r\n\r\n.record-workshop-info h3 {\r\n\tmargin: 0 0 10px;\r\n\tcolor: #333;\r\n\tfont-size: 18px;\r\n}\r\n\r\n.record-workshop-info p {\r\n\tmargin: 5px 0;\r\n\tcolor: #555;\r\n\tfont-size: 14px;\r\n}\r\n\r\n.record-workshop-info i {\r\n\tcolor: #564680;\r\n\tmargin-right: 5px;\r\n\twidth: 14px;\r\n\ttext-align: center;\r\n}\r\n\r\n.record-date {\r\n\tcolor: #888 !important;\r\n\tfont-size: 13px !important;\r\n}\r\n\r\n.record-status {\r\n\tdisplay: inline-block;\r\n\tpadding: 4px 10px;\r\n\tborder-radius: 15px;\r\n\tfont-size: 12px;\r\n\tfont-weight: 500;\r\n\tmargin-top: 10px;\r\n\tcolor: white;\r\n}\r\n\r\n.status-pending {\r\n\tbackground-color: #f39c12;\r\n}\r\n\r\n.status-confirmed {\r\n\tbackground-color: #3498db;\r\n}\r\n\r\n.status-completed {\r\n\tbackground-color: #2ecc71;\r\n}\r\n\r\n.status-cancelled {\r\n\tbackground-color: #e74c3c;\r\n}\r\n\r\n.popup-footer {\r\n\tmargin-top: 20px;\r\n}\r\n\r\n.crystal-workshop-container {\r\n\tmargin-top: 30px;\r\n}\r\n\r\n.crystal-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(3, 1fr);\r\n\tgap: 30px;\r\n\tmargin-bottom: 40px;\r\n}\r\n\r\n.crystal-grid-mobile {\r\n\tgrid-template-columns: 1fr;\r\n\t/* padding: 0 15px; */\r\n}\r\n\r\n.crystal-workshop-card {\r\n\twidth: 350px;\r\n\tborder-radius: 10px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n\ttransition: transform 0.3s, box-shadow 0.3s;\r\n\tbackground: white;\r\n}\r\n\r\n.crystal-workshop-card:hover {\r\n\ttransform: translateY(-5px);\r\n\tbox-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.crystal-workshop-img {\r\n\tposition: relative;\r\n\theight: 200px;\r\n\toverflow: hidden;\r\n}\r\n\r\n.crystal-workshop-img img {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tobject-fit: cover;\r\n\ttransition: transform 0.5s;\r\n}\r\n\r\n.crystal-workshop-card:hover .crystal-workshop-img img {\r\n\ttransform: scale(1.05);\r\n}\r\n\r\n.workshop-overlay {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: linear-gradient(to bottom, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.5));\r\n}\r\n\r\n.crystal-workshop-info {\r\n\tpadding: 20px;\r\n}\r\n\r\n.crystal-workshop-info h3 {\r\n\tmargin: 0 0 10px;\r\n\tcolor: #333;\r\n\tfont-size: 18px;\r\n}\r\n\r\n.crystal-workshop-info p {\r\n\tmargin: 0 0 15px;\r\n\tcolor: #666;\r\n\tfont-size: 14px;\r\n\tline-height: 1.5;\r\n\theight: 63px;\r\n\toverflow: hidden;\r\n}\r\n\r\n.crystal-workshop-meta {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tmargin-bottom: 15px;\r\n\tfont-size: 13px;\r\n\tcolor: #777;\r\n}\r\n\r\n.crystal-workshop-meta span {\r\n\tmargin-right: 15px;\r\n\tmargin-bottom: 5px;\r\n}\r\n\r\n.crystal-workshop-meta i {\r\n\tmargin-right: 5px;\r\n\tcolor: #564680;\r\n}\r\n\r\n.crystal-workshop-footer {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.crystal-workshop-price {\r\n\tfont-size: 18px;\r\n\tfont-weight: bold;\r\n\tcolor: #e83e8c;\r\n}\r\n\r\n.crystal-btn {\r\n\tpadding: 8px 15px;\r\n\tbackground-color: #564680;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tborder-radius: 20px;\r\n\tcursor: pointer;\r\n\tfont-size: 14px;\r\n\ttransition: background-color 0.3s;\r\n}\r\n\r\n.crystal-btn:hover {\r\n\tbackground-color: #483c6f;\r\n}\r\n\r\n.pulse-btn {\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.pulse-btn:after {\r\n\tcontent: \"\";\r\n\tposition: absolute;\r\n\ttop: 50%;\r\n\tleft: 50%;\r\n\twidth: 5px;\r\n\theight: 5px;\r\n\tbackground: rgba(255, 255, 255, 0.5);\r\n\topacity: 0;\r\n\tborder-radius: 100%;\r\n\ttransform: scale(1, 1) translate(-50%);\r\n\ttransform-origin: 50% 50%;\r\n}\r\n\r\n.pulse-btn:hover:after {\r\n\tanimation: ripple 1.2s ease-out;\r\n}\r\n\r\n@keyframes ripple {\r\n\t0% {\r\n\t\ttransform: scale(0, 0);\r\n\t\topacity: 0.5;\r\n\t}\r\n\r\n\t100% {\r\n\t\ttransform: scale(50, 50);\r\n\t\topacity: 0;\r\n\t}\r\n}\r\n\r\n.no-data {\r\n\ttext-align: center;\r\n\tpadding: 40px 0;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 分页样式 */\r\n.am-pagination {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\tlist-style: none;\r\n\tpadding: 0;\r\n\tmargin: 30px 0;\r\n}\r\n\r\n.am-pagination li {\r\n\tmargin: 0 5px;\r\n}\r\n\r\n.am-pagination a {\r\n\tdisplay: block;\r\n\tpadding: 8px 12px;\r\n\tcolor: #666;\r\n\tbackground: #f5f5f5;\r\n\tborder-radius: 4px;\r\n\ttext-decoration: none;\r\n}\r\n\r\n.am-pagination .am-active a {\r\n\tbackground-color: #564680;\r\n\tcolor: white;\r\n}\r\n\r\n.am-pagination .am-disabled a {\r\n\tcolor: #ccc;\r\n\tcursor: not-allowed;\r\n}\r\n\r\n/* 报名表单样式 */\r\n.enroll-form {\r\n\tpadding: 20px;\r\n}\r\n\r\n.enroll-title {\r\n\ttext-align: center;\r\n\tmargin-bottom: 20px;\r\n\tcolor: #564680;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n\t.crystal-grid {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t\tpadding: 0 15px;\r\n\t}\r\n\r\n\t.search-container {\r\n\t\twidth: 90%;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n}\r\n\r\n@media (max-width: 576px) {\r\n\t.crystal-grid {\r\n\t\tgrid-template-columns: 1fr;\r\n\t\tpadding: 0 15px;\r\n\t}\r\n\r\n\t.crystal-workshop-meta {\r\n\t\tfont-size: 12px;\r\n\t}\r\n\r\n\t.crystal-workshop-meta span {\r\n\t\tmargin-right: 10px;\r\n\t}\r\n\r\n\t.crystal-workshop-footer {\r\n\t\tflex-direction: column;\r\n\t\tgap: 10px;\r\n\t\talign-items: flex-start;\r\n\t}\r\n\r\n\t.crystal-btn {\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.my-enroll-btn {\r\n\t\tfont-size: 14px;\r\n\t\tpadding: 8px 16px;\r\n\t}\r\n\t\r\n\t.record-workshop-info h3 {\r\n\t\tfont-size: 16px;\r\n\t}\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwIA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAH;EAAA;EACAI,KAAA;IACA;MACAH,aAAA,EAAAA,aAAA;MACAI,YAAA;MACAC,aAAA;MACAC,SAAA;MACAC,QAAA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,eAAA;MACAC,eAAA;MACAC,UAAA;QACAX,IAAA;QACAY,KAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACAC,UAAA;MACAC,QAAA;MACA;MACAC,iBAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACA;IACAD,aAAA;MACA,KAAAE,UAAA;QACA,aAAAlB,SAAA;QACA,cAAAC,QAAA;QACA,cAAAF;MACA,GAAAoB,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAvB,YAAA,GAAAsB,IAAA,CAAAvB,IAAA,CAAAyB,IAAA;UACA,KAAApB,KAAA,GAAAkB,IAAA,CAAAvB,IAAA,CAAAK,KAAA;UACA,KAAAC,SAAA,GAAAiB,IAAA,CAAAvB,IAAA,CAAAM,SAAA;QACA;UACA,KAAAL,YAAA;UACA,KAAAI,KAAA;UACA,KAAAC,SAAA;QACA;MACA;IACA;IAEA;IACAoB,gBAAA;MACA,KAAAvB,SAAA;MACA,KAAAgB,YAAA;IACA;IAEA;IACAQ,YAAAC,KAAA;MACA,IAAAA,KAAA,QAAAA,KAAA,QAAAtB,SAAA;QACA;MACA;MACA,KAAAH,SAAA,GAAAyB,KAAA;MACA,KAAAT,YAAA;IACA;IAEA;IACAU,eAAAC,QAAA;MACA,KAAAT,UAAA,UAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAV,QAAA,GAAAS,IAAA,CAAAvB,IAAA;UACA,KAAAQ,eAAA,GAAAsB,QAAA;UACA,KAAAvB,eAAA;UACA;UACA,KAAAE,UAAA;YACAX,IAAA,OAAAgB,QAAA,CAAAiB,QAAA;YACArB,KAAA,OAAAI,QAAA,CAAAJ,KAAA;YACAC,MAAA;YACAC,OAAA;UACA;UACAoB,cAAA,CAAAC,OAAA,aAAAC,IAAA,CAAAC,SAAA,CAAAZ,IAAA,CAAAvB,IAAA;UACAoC,YAAA,CAAAH,OAAA,aAAAC,IAAA,CAAAC,SAAA,CAAAZ,IAAA,CAAAvB,IAAA;QACA;MACA;IACA;IAEA;IACAqC,cAAAC,GAAA;MACA,uBAAAC,IAAA,CAAAD,GAAA;IACA;IAEA;IACAE,cAAAF,GAAA;MACA,0DAAAC,IAAA,CAAAD,GAAA;IACA;IAEA;IACAG,iBAAA;MACA,KAAA5B,UAAA;;MAEA;MACA,MAAA6B,QAAA;QACAC,aAAA,OAAAnC,eAAA,CAAAoC,EAAA;QACA,QAAAnC;MACA;;MAEA;MACA,KAAAoC,WAAA,wBAAAH,QAAA,EAAApB,IAAA,CAAAC,IAAA;QACA,KAAAV,UAAA;QACAiC,OAAA,CAAAC,GAAA,CAAAxB,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAwB,MAAA,CAAAC,OAAA;UACA,KAAA1C,eAAA;UACA;UACA,KAAAY,YAAA;QACA;UACA,KAAA6B,MAAA,CAAAE,IAAA,CAAA3B,IAAA,CAAAX,OAAA;QACA;MACA;IACA;IAEA;IACAuC,cAAA;MACA,KAAA9B,UAAA,sBAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAR,YAAA,GAAAO,IAAA,CAAAvB,IAAA;UACA,KAAAe,iBAAA;QACA;UACA,KAAAiC,MAAA,CAAAE,IAAA,CAAA3B,IAAA,CAAAX,OAAA;QACA;MACA;IACA;IAEA;IACAwC,WAAAC,SAAA;MACA,KAAAA,SAAA;MACA,MAAAC,IAAA,OAAAC,IAAA,CAAAF,SAAA;MACA,UAAAC,IAAA,CAAAE,WAAA,MAAAC,MAAA,CAAAH,IAAA,CAAAI,QAAA,QAAAC,QAAA,YAAAF,MAAA,CAAAH,IAAA,CAAAM,OAAA,IAAAD,QAAA;IACA;IAEA;IACAE,eAAAC,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACAC,cAAAD,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}