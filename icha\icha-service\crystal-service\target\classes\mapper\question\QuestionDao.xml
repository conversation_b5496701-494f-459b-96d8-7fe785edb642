<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.crystal.service.dao.QuestionDao">

	<!-- 根据包名 模块名 以及类名 生成Mapper XML 配置文件 -->
    <resultMap type="com.crystal.common.model.question.QuestionEntity" id="questionMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="optionId" column="option_id"/>
        <result property="charOptionId" column="char_option_id"/>
        <result property="addTime" column="add_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>


</mapper>
