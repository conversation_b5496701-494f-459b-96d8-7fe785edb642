<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.crystal.service.dao.CmsCourseDao">

    <!-- 根据包名 模块名 以及类名 生成Mapper XML 配置文件 -->
    <resultMap type="com.crystal.common.model.cms.CmsCourseEntity" id="cmsCourseMap">
        <result property="id" column="id"/>
        <result property="addTime" column="add_time"/>
        <result property="isDel" column="is_del"/>
        <result property="title" column="title"/>
        <result property="brief" column="brief"/>
        <result property="cover" column="cover"/>
        <result property="tags" column="tags"/>
        <result property="location" column="location"/>
        <result property="isShow" column="is_show"/>
        <result property="isIndex" column="is_index"/>
        <result property="paixu" column="paixu"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="price" column="price"/>
    </resultMap>

</mapper> 