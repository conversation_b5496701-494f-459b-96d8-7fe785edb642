<template>
	<Layout>
		<!-- 页面顶部背景区域 -->
		<div class="hero-section" :style="{backgroundImage: 'url(' + indexImage + ')'}">
			<div class="hero-content">
				<h1 class="hero-title"><i class="fa fa-diamond fa-spin-pulse"></i> {{indexTitle}}</h1>
				<p class="hero-subtitle"><i class="fa fa-heart"></i> {{indexDesc}}</p>
				<div class="hero-buttons">
					<!-- <button class="hero-btn"><i class="fa fa-compass"></i> 开始探索</button> -->
					<button @click="turnAbout" class="hero-btn outline"><i class="fa fa-info-circle"></i> 了解更多</button>
				</div>
			</div>
		</div>

		<!-- 寻找水晶疗愈师 -->
		<div class="section section-healer">
			<div class="container" style="max-width: 1160px">
				<div class="section--header fancy-title">
					<div class="crystal-icon">
						<i class="fa fa-user-md fa-2x"></i>
					</div>
					<h2 class="section--title">寻找水晶疗愈师</h2>
					<div class="title-decoration">
						<span></span>
					</div>
					<p class="section--desc"><i class="fa fa-certificate fa-spin-slow"></i> 专业的水晶疗愈师，为您带来身心灵的平衡</p>
				</div>

				<div class="crystal-healer-container">
					<div class="crystal-grid" :class="{'crystal-grid-mobile': isMobilePhone}">
						<div v-for="(healer, index) in healerList" :key="index" class="crystal-card" @click="showHealerDetail(healer)">
							<div class="crystal-card-img">
								<img :src="healer.avatar" alt="">
								<div class="card-overlay"></div>
							</div>
							<div class="crystal-badge"><i class="fa fa-certificate"></i> 认证</div>
							<div class="crystal-card-info">
								<h3><i class="fa fa-user-circle-o"></i> {{healer.name}}</h3>
								<div class="crystal-tags">
									<span v-for="(tag, i) in healer.tags" :key="i"><i class="fa fa-star-o"></i> {{tag}}</span>
								</div>
								<p><i class="fa fa-quote-left quote-icon"></i> {{healer.intro}}</p>
								<div class="crystal-card-footer">
									<span class="crystal-card-location"><i class="fa fa-map-marker"></i> {{healer.location}}</span>
									<button class="crystal-btn" @click="showHealerDetail(healer)"><i class="fa fa-info-circle"></i> 查看详情</button>
								</div>
							</div>
						</div>
					</div>
					<div class="crystal-more" @click="showMore('healer')">
						<span>查看更多</span>
						<i class="fa fa-angle-right"></i>
					</div>
				</div>
			</div>
		</div>

		<!-- 查找水晶疗愈课程 -->
		<div class="section section-course">
			<div class="container" style="max-width: 1160px">
				<div class="section--header fancy-title">
					<div class="crystal-icon">
						<i class="fa fa-graduation-cap fa-2x"></i>
					</div>
					<h2 class="section--title">查找水晶疗愈课程</h2>
					<div class="title-decoration">
						<span></span>
					</div>
					<p class="section--desc"><i class="fa fa-book"></i> 找到适合您的水晶疗愈课程，开启身心灵之旅</p>
				</div>

				<div class="crystal-course-container">
					<div class="crystal-grid" :class="{'crystal-grid-mobile': isMobilePhone}">
						<div v-for="(course, index) in courseList" :key="index" class="crystal-course-card">
							<div class="crystal-course-img">
								<img :src="course.cover" alt="">
								<div class="course-overlay"></div>
								<div class="crystal-course-tag"><i class="fa fa-bookmark-o"></i> {{course.tag || '热门'}}</div>
							</div>
							<div class="crystal-course-info">
								<h3><i class="fa fa-diamond"></i> {{course.title}}</h3>
								<p><i class="fa fa-info-circle info-icon"></i> {{course.brief}}</p>
								<!-- <div class="crystal-course-meta">
									<span><i class="fa fa-calendar"></i> {{course.startTime}}</span>
									<span><i class="fa fa-map-marker"></i> {{course.location}}</span>
								</div> -->
								<div class="crystal-course-footer">
									<span class="crystal-course-price"><i class="fa fa-jpy"></i> ¥{{course.price}}</span>
									<button v-if="!course.isAttend" @click="showApply(course)" class="crystal-btn pulse-btn"><i class="fa fa-pencil"></i> 立即报名</button>
									<button v-else @click="goCourseDetail(course.id)" class="crystal-btn crystal-btn-secondary">观看课程</button>
								</div>
							</div>
						</div>
					</div>
					<div class="crystal-more" @click="showMore('course')">
						<span>查看更多</span>
						<i class="fa fa-angle-right"></i>
					</div>
				</div>
			</div>
		</div>

		<!-- 脉轮测试 -->
		<!-- <div class="section section-chakra">
			<div class="container" style="max-width: 1160px">
				<div class="section--header fancy-title">
					<div class="crystal-icon">
						<i class="fa fa-circle-o fa-2x"></i>
					</div>
					<h2 class="section--title">脉轮测试</h2>
					<div class="title-decoration">
						<span></span>
					</div>
					<p class="section--desc"><i class="fa fa-magic"></i> 了解您的脉轮能量状态，开启身心灵平衡之旅</p>
				</div>

				<div class="chakra-test-container">
					<div class="chakra-intro">
						<div class="chakra-intro-content">
							<h3><i class="fa fa-lightbulb-o"></i> 什么是脉轮测试？</h3>
							<p>脉轮是人体能量系统的重要组成部分，通过专业的脉轮测试，您可以了解自己七个主要脉轮的能量状态，发现需要平衡和调整的部分。</p>

							<div class="chakra-features">
								<div class="chakra-feature">
									<i class="fa fa-check-circle"></i>
									<span>专业测试问卷</span>
								</div>
								<div class="chakra-feature">
									<i class="fa fa-chart-bar"></i>
									<span>详细结果分析</span>
								</div>
								<div class="chakra-feature">
									<i class="fa fa-heart"></i>
									<span>个性化建议</span>
								</div>
							</div>
						</div>
						<div class="chakra-intro-image">
							<div class="chakra-circles">
								<div class="chakra-circle" style="background: #993734;"></div>
								<div class="chakra-circle" style="background: #be6f2a;"></div>
								<div class="chakra-circle" style="background: #d7c34a;"></div>
								<div class="chakra-circle" style="background: #5f9057;"></div>
								<div class="chakra-circle" style="background: #5b8aa4;"></div>
								<div class="chakra-circle" style="background: #2c3485;"></div>
								<div class="chakra-circle" style="background: #7e4997;"></div>
							</div>
						</div>
					</div>

					<div class="chakra-actions">
						<button @click="goToChakraTest" class="crystal-btn-large pulse-btn">
							<i class="fa fa-play-circle"></i> 开始脉轮测试
						</button>
						<div class="chakra-links">
							<a @click="goToChakraIntro" class="chakra-link">
								<i class="fa fa-info-circle"></i> 脉轮简介
							</a>
							<a @click="goToChakraBalance" class="chakra-link">
								<i class="fa fa-balance-scale"></i> 平衡脉轮
							</a>
						</div>
					</div>
				</div>
			</div>
		</div> -->

		<!-- 成为会员机构 -->
		<div class="section member-section">
			<div class="container" style="max-width: 1160px">
				<div class="section--header fancy-title">
					<div class="crystal-icon">
						<i class="fa fa-users fa-2x"></i>
					</div>
					<h2 class="section--title">成为会员机构</h2>
					<div class="title-decoration">
						<span></span>
					</div>
					<p class="section--desc"><i class="fa fa-handshake-o"></i> 加入我们的专业网络，共同推广水晶疗愈</p>
				</div>

				<div class="member-benefits">
					<div class="member-grid" :class="{'member-grid-mobile': isMobilePhone}">
						<div v-for="(benefit, index) in memberBenefits" :key="index" class="member-benefit-card">
							<div class="member-benefit-icon">
								<i :class="benefit.faIcon"></i>
							</div>
							<h3>{{benefit.title}}</h3>
							<p>{{benefit.desc}}</p>
						</div>
					</div>
					<div class="member-join" @click="turnJoin">
						<button class="crystal-btn-large shine-btn"><i class="fa fa-rocket"></i> 立即加入</button>
						<p class="member-join-text"><i class="fa fa-building"></i> 已有<span>328</span>家机构成为我们的会员</p>
					</div>
				</div>
			</div>
		</div>
		
		<!-- 装饰元素 -->
		<div class="decoration-element top-right"><i class="fa fa-diamond"></i></div>
		<div class="decoration-element bottom-left"><i class="fa fa-sun-o fa-spin-slow"></i></div>

		<!-- 课程报名弹窗复用组件 -->
		<CourseApplyDialog :visible="showApplyDialog" :course="applyCourse" @close="closeApplyDialog" @success="applySuccess" />
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import Message from "@/utils/message";
import CourseApplyDialog from '@/components/CourseApplyDialog.vue';
export default {
	name: "IndexView",
	components: { Layout, CourseApplyDialog },
	data() {
		return {
			indexImage: '',
			indexTitle: '',
			indexDesc: '',
			isMobilePhone: isMobilePhone(),
			tabList: [],
			tabIndex: 0,
			slideshow: [],

			// 水晶疗愈师列表
			healerList: [
			],
			// 水晶疗愈课程列表
			courseList: [
			],
			// 会员机构福利
			memberBenefits: [
				{
					id: 1,
					faIcon: 'fa fa-line-chart fa-3x',
					title: '优质流量',
					desc: '获得平台精准推荐，提高曝光度和客户转化率'
				},
				{
					id: 2,
					faIcon: 'fa fa-certificate fa-3x',
					title: '专业认证',
					desc: '获得行业权威认证，提升机构专业形象和信誉度'
				},
				{
					id: 3,
					faIcon: 'fa fa-share-alt fa-3x',
					title: '资源共享',
					desc: '共享行业最新资讯、教材和教学方法，保持竞争优势'
				},
				{
					id: 4,
					faIcon: 'fa fa-handshake-o fa-3x',
					title: '商业合作',
					desc: '接触更多优质合作伙伴，拓展业务发展空间'
				}
			],
			showApplyDialog: false,
			applyCourse: null,
		}
	},
	mounted() {
		this.getIndex();
		this.getIndexConfig();
		this.$wxShare();
	},
	methods: {
		goCourseDetail(id) {
			this.$router.push({
				path: '/course-detail',
				query: { id: id }
			})
		},
		turnJoin() {
			this.$router.push({
				path: '/join',
			})
		},
		turnAbout() {
			this.$router.push({
				path: '/about',
			})
		},
		// 图片点击放大
		showImg(e) {
			if (this.isMobilePhone) {
				vant.ImagePreview({
					images: [e], // 图片集合
					closeable: true, // 关闭按钮
				});
			}
		},
		getIndex() {
			const userInfoStr = localStorage.getItem("userInfo") || '{}';
			const userInfo = JSON.parse(userInfoStr);
			this.getRequest("/cms/index", {
				userId: userInfo.uid || ''
			}).then(resp => {
				console.log(resp)
				if (resp.data && resp.code == 200) {
					this.courseList = resp.data.course
					this.healerList = resp.data.healers
					this.healerList.forEach(item => {
						item.tags = item.tags ? item.tags.split(',') : []
					})
					//console.log(this.slideshow)
				} else {
					this.courseList = []
					this.healerList = []
					Message.error(resp.data.message || '获取首页数据失败')
				}
			})
		},
		getIndexConfig() {
			this.getRequest("/cms/config/index").then(resp => {
				console.log(resp)
				if (resp.data && resp.code == 200) {
					this.indexImage = resp.data.indexImage
					this.indexTitle = resp.data.indexTitle
					this.indexDesc = resp.data.indexDesc
				} else {
				}
			})
		},
		// 显示疗愈师详情
		showHealerDetail(healer) {
			this.$router.push(`/healer-detail/${healer.id}?from=index`);
		},
		// 查看更多
		showMore(type) {
			console.log('查看更多:', type);
			// 这里可以添加跳转到列表页的逻辑
			if(type == 'healer'){
				this.$router.push({
					path: '/healers',
				})
			}else if(type == 'course'){
				this.$router.push({
					path: '/course',
				})
			}
		},
		showApply(course) {
			this.applyCourse = course;
			this.showApplyDialog = true;
		},
		closeApplyDialog() {
			this.showApplyDialog = false;
			this.applyCourse = null;
		},
		applySuccess() {
			this.getIndex();
		},
		// 脉轮测试相关方法
		goToChakraTest() {
			this.$router.push('/chakra-test');
		},
		goToChakraIntro() {
			this.$router.push('/chakra-test/intro');
		},
		goToChakraBalance() {
			this.$router.push('/chakra-test/balance');
		},
	},
}
</script>

<style scoped>
/* 基础样式 */
.product {
	text-align: center;
	height: 80px;
	line-height: 80px;
	font-size: 24px;
	font-weight: 600;
	color: #564680;
}

.am-g {
	width: 96%;
}

.item-img {
	padding: 10px;
	box-shadow: 2px 3px 10px 0px rgba(0, 0, 0, 0.16);
	border-radius: 8px;
	margin-top: 20px;
}

/* 英雄区域样式 */
.hero-section {
	height: 500px;
	background: linear-gradient(45deg, #7b4397, #dc2430);
	background-size: cover;
	background-position: center;
	background-blend-mode: overlay;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	text-align: center;
	margin-bottom: 50px;
	width: 100%;
}

.hero-section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(55, 30, 94, 0.7);
}

.hero-content {
	position: relative;
	z-index: 2;
	max-width: 800px;
	padding: 0 20px;
}

.hero-title {
	font-size: 48px;
	font-weight: 700;
	margin-bottom: 20px;
	text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
	animation: fadeInDown 1s ease-out;
}

.hero-title i {
	margin-right: 10px;
	color: rgba(255, 255, 255, 0.9);
}

.hero-subtitle {
	font-size: 20px;
	font-weight: 400;
	margin-bottom: 30px;
	text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
	animation: fadeInUp 1s ease-out;
}

.hero-subtitle i {
	margin-right: 10px;
	color: #ff6b8b;
}

.hero-buttons {
	display: flex;
	gap: 20px;
	justify-content: center;
	margin-top: 30px;
	animation: fadeInUp 1.5s ease-out;
}

.hero-btn {
	background: linear-gradient(135deg, #7b4397, #dc2430);
	color: white;
	border: none;
	padding: 12px 25px;
	border-radius: 30px;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s;
	box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.hero-btn i {
	margin-right: 8px;
}

.hero-btn:hover {
	transform: translateY(-3px);
	box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
}

.hero-btn.outline {
	background: transparent;
	border: 2px solid white;
}

.hero-btn.outline:hover {
	background: rgba(255, 255, 255, 0.2);
}

/* 区块通用样式 */
.section {
	padding: 80px 0;
	position: relative;
}

.section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: url('https://img.freepik.com/free-vector/abstract-background-with-geometric-pattern_1319-74.jpg');
	background-size: cover;
	background-position: center;
	opacity: 0.03;
	z-index: -1;
}

.section-healer {
	background-color: #fff;
}

.section-course {
	background-color: #f8f5ff;
}

/* 标题样式优化 */
.fancy-title {
	text-align: center;
	margin-bottom: 60px;
	position: relative;
}

.crystal-icon {
	margin: 0 auto 15px;
	width: 80px;
	height: 80px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: linear-gradient(135deg, #ddd6f3, #faaca8);
	box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.crystal-icon i {
	color: white;
}

.section--title {
	font-size: 36px;
	font-weight: 700;
	color: #3a2c58;
	margin-bottom: 20px;
	position: relative;
	display: inline-block;
}

.section--desc i {
	margin-right: 8px;
	color: #7b4397;
}

.title-decoration {
	position: relative;
	height: 2px;
	width: 120px;
	background: linear-gradient(to right, transparent, #564680, transparent);
	margin: 0 auto 25px;
}

.title-decoration span {
	position: absolute;
	top: -7px;
	left: 50%;
	transform: translateX(-50%);
	width: 15px;
	height: 15px;
	background: #564680;
	border-radius: 50%;
}

.title-decoration span::before,
.title-decoration span::after {
	content: '';
	position: absolute;
	top: 50%;
	width: 8px;
	height: 8px;
	background: #784ba0;
	border-radius: 50%;
}

.title-decoration span::before {
	left: -20px;
	transform: translateY(-50%);
}

.title-decoration span::after {
	right: -20px;
	transform: translateY(-50%);
}

.section--desc {
	font-size: 18px;
	color: #666;
	max-width: 700px;
	margin: 0 auto;
	line-height: 1.6;
}

/* 水晶疗愈师卡片样式优化 */
.crystal-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
	justify-content: center;
}

.crystal-grid-mobile {
	flex-direction: column;
	align-items: center;
}

.crystal-card {
	width: 350px;
	background: #fff;
	border-radius: 15px;
	overflow: hidden;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	cursor: pointer;
	transform: translateY(0);
	position: relative;
}

.crystal-card:hover {
	transform: translateY(-10px);
	box-shadow: 0 15px 30px rgba(86, 70, 128, 0.2);
}

.crystal-card-img {
	height: 230px;
	overflow: hidden;
	position: relative;
}

.crystal-card-img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.6s;
}

.card-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 50%;
	background: linear-gradient(to top, rgba(86, 70, 128, 0.7), transparent);
	opacity: 0;
	transition: all 0.3s;
}

.crystal-card:hover .card-overlay {
	opacity: 1;
}

.crystal-card:hover .crystal-card-img img {
	transform: scale(1.08);
}

.crystal-card-info {
	padding: 25px;
	position: relative;
}

.crystal-card-info h3 {
	font-size: 22px;
	color: #3a2c58;
	margin-bottom: 12px;
	font-weight: 600;
}

.crystal-card-info h3 i {
	color: #7b4397;
	margin-right: 8px;
}

.crystal-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin-bottom: 15px;
}

.crystal-tags span {
	background: linear-gradient(135deg, #ddd6f3, #faaca8);
	color: #3a2c58;
	font-size: 12px;
	padding: 5px 12px;
	border-radius: 20px;
	font-weight: 500;
}

.crystal-tags span i {
	font-size: 10px;
	margin-right: 4px;
}

.crystal-card-info p {
	color: #666;
	font-size: 15px;
	line-height: 1.6;
	margin-bottom: 20px;
	height: 72px;
	overflow: hidden;
}

.crystal-card-info p i {
	font-size: 16px;
	margin-right: 5px;
	opacity: 0.7;
}

.quote-icon,
.info-icon {
	color: #ddd6f3;
	font-size: 16px;
	margin-right: 5px;
	opacity: 0.7;
}

.crystal-card-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.crystal-card-location {
	color: #888;
	font-size: 14px;
}

.crystal-card-location i {
	margin-right: 8px;
}

.crystal-btn {
	background: linear-gradient(135deg, #7b4397, #dc2430);
	color: white;
	border: none;
	padding: 10px 20px;
	border-radius: 25px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s;
	box-shadow: 0 4px 15px rgba(123, 67, 151, 0.3);
}

.crystal-btn:hover {
	background: linear-gradient(135deg, #dc2430, #7b4397);
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(123, 67, 151, 0.4);
}

.crystal-btn i {
	margin-right: 8px;
}

.crystal-more {
	text-align: center;
	margin-top: 40px;
	display: inline-block;
	position: relative;
	left: 50%;
	transform: translateX(-50%);
}

.crystal-more span {
	color: #564680;
	font-size: 17px;
	font-weight: 500;
	margin-right: 5px;
	transition: all 0.3s;
}

.crystal-more i {
	transition: all 0.3s;
	position: relative;
	top: 1px;
}

.crystal-more:hover span {
	color: #dc2430;
}

.crystal-more:hover i {
	transform: translateX(5px);
	color: #dc2430;
}

/* 课程卡片样式优化 */
.crystal-course-card {
	width: 350px;
	background: #fff;
	border-radius: 15px;
	overflow: hidden;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	transform: translateY(0);
}

.crystal-course-card:hover {
	transform: translateY(-10px);
	box-shadow: 0 15px 30px rgba(86, 70, 128, 0.2);
}

.crystal-course-img {
	height: 230px;
	overflow: hidden;
	position: relative;
}

.crystal-course-img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.6s;
}

.course-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100%;
	background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
	opacity: 0;
	transition: all 0.3s;
}

.crystal-course-card:hover .course-overlay {
	opacity: 1;
}

.crystal-course-card:hover .crystal-course-img img {
	transform: scale(1.08);
}

.crystal-course-tag {
	position: absolute;
	top: 15px;
	right: 15px;
	background: linear-gradient(135deg, #7b4397, #dc2430);
	color: white;
	padding: 8px 15px;
	border-radius: 25px;
	font-size: 13px;
	font-weight: 500;
	z-index: 2;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.crystal-course-info {
	padding: 25px;
}

.crystal-course-info h3 {
	font-size: 20px;
	color: #3a2c58;
	margin-bottom: 12px;
	font-weight: 600;
	height: 48px;
	overflow: hidden;
}

.crystal-course-info h3 i {
	color: #7b4397;
	margin-right: 8px;
}

.crystal-course-info p {
	color: #666;
	font-size: 15px;
	line-height: 1.6;
	margin-bottom: 15px;
	height: 48px;
	overflow: hidden;
}

.crystal-course-info p i {
	font-size: 16px;
	margin-right: 5px;
	opacity: 0.7;
}

.crystal-course-meta {
	display: flex;
	flex-wrap: wrap;
	gap: 15px;
	margin-bottom: 20px;
}

.crystal-course-meta span {
	color: #888;
	font-size: 14px;
}

.crystal-course-meta i {
	margin-right: 5px;
	color: #7b4397;
}

.crystal-course-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.crystal-course-price {
	font-size: 20px;
	color: #dc2430;
	font-weight: bold;
}

.pulse-btn {
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		box-shadow: 0 0 0 0 rgba(123, 67, 151, 0.4);
	}
	70% {
		box-shadow: 0 0 0 10px rgba(123, 67, 151, 0);
	}
	100% {
		box-shadow: 0 0 0 0 rgba(123, 67, 151, 0);
	}
}

/* 会员机构部分优化 */
.member-section {
	/* background: linear-gradient(135deg, #f5f7fa, #eef2f7); */
	position: relative;
	overflow: hidden;
}

.member-section::before {
	content: '';
	position: absolute;
	width: 300px;
	height: 300px;
	background: radial-gradient(circle, rgba(123, 67, 151, 0.1), transparent);
	top: -150px;
	left: -150px;
	border-radius: 50%;
}

.member-section::after {
	content: '';
	position: absolute;
	width: 200px;
	height: 200px;
	background: radial-gradient(circle, rgba(220, 36, 48, 0.1), transparent);
	bottom: -100px;
	right: -100px;
	border-radius: 50%;
}

.member-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
	justify-content: center;
	margin-bottom: 60px;
}

.member-grid-mobile {
	flex-direction: column;
	align-items: center;
}

.member-benefit-card {
	width: 260px;
	background: white;
	border-radius: 15px;
	padding: 35px 25px;
	text-align: center;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
	transition: all 0.3s;
	position: relative;
	overflow: hidden;
}

.member-benefit-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 5px;
	background: linear-gradient(to right, #7b4397, #dc2430);
}

.member-benefit-card:hover {
	transform: translateY(-10px);
	box-shadow: 0 15px 40px rgba(86, 70, 128, 0.15);
}

.member-benefit-icon {
	margin-bottom: 25px;
	transition: all 0.3s;
}

.member-benefit-card:hover .member-benefit-icon {
	transform: translateY(-5px);
}

.member-benefit-icon i {
	font-size: 70px;
	color: #7b4397;
}

.member-benefit-card h3 {
	font-size: 20px;
	color: #3a2c58;
	margin-bottom: 15px;
	font-weight: 600;
}

.member-benefit-card p {
	color: #666;
	font-size: 15px;
	line-height: 1.6;
}

.member-join {
	text-align: center;
	padding: 20px;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 15px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
	max-width: 500px;
	margin: 0 auto;
}

.crystal-btn-large {
	background: linear-gradient(135deg, #7b4397, #dc2430);
	color: white;
	border: none;
	padding: 15px 40px;
	border-radius: 30px;
	font-size: 18px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s;
	margin-bottom: 20px;
	box-shadow: 0 10px 20px rgba(123, 67, 151, 0.3);
}

.crystal-btn-large:hover {
	background: linear-gradient(135deg, #dc2430, #7b4397);
	transform: translateY(-5px);
	box-shadow: 0 15px 30px rgba(123, 67, 151, 0.4);
}

.shine-btn {
	position: relative;
	overflow: hidden;
}

.shine-btn::after {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: rgba(255, 255, 255, 0.2);
	transform: rotate(30deg);
	animation: shine 3s infinite;
}

@keyframes shine {
	0% { transform: translateX(-100%) rotate(30deg); }
	100% { transform: translateX(100%) rotate(30deg); }
}

.member-join-text {
	color: #666;
	font-size: 16px;
}

.member-join-text i {
	margin-right: 8px;
	color: #7b4397;
}

.member-join-text span {
	color: #dc2430;
	font-weight: bold;
	font-size: 20px;
	margin: 0 5px;
}

/* 动画效果 */
@keyframes fadeInDown {
	from {
		opacity: 0;
		transform: translateY(-30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 装饰元素 */
.decoration-element {
	position: fixed;
	font-size: 160px;
	color: rgba(123, 67, 151, 0.03);
	z-index: 0;
	pointer-events: none;
}

.top-right {
	top: 100px;
	right: 50px;
}

.bottom-left {
	bottom: 100px;
	left: 50px;
}

/* 动画效果 */
.fa-spin-pulse {
	animation: spin-pulse 2s infinite alternate;
}

.fa-spin-slow {
	animation: spin 10s linear infinite;
}

@keyframes spin-pulse {
	0% {
		transform: scale(1) rotate(0);
	}
	100% {
		transform: scale(1.1) rotate(15deg);
	}
}

@keyframes spin {
	0% {
		transform: rotate(0);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* 移动端适配优化 */
@media (max-width: 768px) {
	.hero-section {
		height: 400px;
	}

	.hero-title {
		font-size: 36px;
	}

	.hero-subtitle {
		font-size: 18px;
	}
	
	.hero-buttons {
		flex-direction: column;
		gap: 15px;
		align-items: center;
	}
	
	.hero-btn {
		width: 200px;
	}

	.decoration-element {
		font-size: 100px;
	}
	
	.top-right {
		top: 50px;
		right: 20px;
	}
	
	.bottom-left {
		bottom: 50px;
		left: 20px;
	}
}
.crystal-badge {
	position: absolute;
	top: 15px;
	right: 15px;
	background: linear-gradient(135deg, #7b4397, #dc2430);
	color: white;
	padding: 5px 10px;
	border-radius: 25px;
	font-size: 12px;
	font-weight: 500;
	z-index: 2;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.crystal-btn-secondary {
	background: linear-gradient(135deg, #2bff00, #1900ff);
	color: white;
	border: none;
	padding: 10px 20px;
	border-radius: 25px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s;
	box-shadow: 0 4px 15px rgba(180, 180, 180, 0.18);
	margin-left: 10px;
	animation: none;
}
.crystal-btn-secondary:hover {
	background: linear-gradient(135deg, #2bff00, #1900ff);
	color: white;
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(180, 180, 180, 0.28);
}

/* 脉轮测试部分样式 */
.section-chakra {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	padding: 80px 0;
	position: relative;
	overflow: hidden;
}

.chakra-test-container {
	margin-top: 50px;
}

.chakra-intro {
	display: flex;
	align-items: center;
	gap: 50px;
	margin-bottom: 40px;
}

.chakra-intro-content {
	flex: 1;
}

.chakra-intro-content h3 {
	font-size: 24px;
	color: #333;
	margin-bottom: 20px;
	font-weight: 600;
}

.chakra-intro-content p {
	font-size: 16px;
	line-height: 1.6;
	color: #666;
	margin-bottom: 30px;
}

.chakra-features {
	display: flex;
	gap: 30px;
}

.chakra-feature {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 14px;
	color: #555;
}

.chakra-feature i {
	color: #c9ab79;
	font-size: 16px;
}

.chakra-intro-image {
	flex: 0 0 300px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.chakra-circles {
	display: flex;
	flex-direction: column;
	gap: 10px;
	align-items: center;
}

.chakra-circle {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
	animation: chakra-pulse 2s infinite;
}

@keyframes chakra-pulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.1); }
}

.chakra-actions {
	text-align: center;
}

.chakra-links {
	margin-top: 20px;
	display: flex;
	justify-content: center;
	gap: 30px;
}

.chakra-link {
	color: #c9ab79;
	text-decoration: none;
	font-size: 14px;
	cursor: pointer;
	transition: all 0.3s;
}

.chakra-link:hover {
	color: #b8996a;
	transform: translateY(-2px);
}

.chakra-link i {
	margin-right: 5px;
}

/* 移动端适配 */
@media (max-width: 768px) {
	.chakra-intro {
		flex-direction: column;
		gap: 30px;
		text-align: center;
	}

	.chakra-intro-image {
		flex: none;
	}

	.chakra-features {
		flex-direction: column;
		gap: 15px;
		align-items: center;
	}

	.chakra-links {
		flex-direction: column;
		gap: 15px;
	}
}

</style>
