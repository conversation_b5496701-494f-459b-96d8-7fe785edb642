{"ast": null, "code": "export default class AppFunctions {\n  static addClass(element, className) {\n    document.querySelector(element).classList.add(className);\n  }\n  static removeClass(element, className) {\n    document.querySelector(element).classList.remove(className);\n  }\n  static toggleClass(element, className) {\n    document.querySelector(element).classList.toggle(className);\n  }\n  static flatDeep(arr, d = 1) {\n    return d > 0 ? arr.reduce((acc, val) => acc.concat(Array.isArray(val) ? this.flatDeep(val, d - 1) : val), []) : arr.slice();\n  }\n  static slugify(text) {\n    return text.toString().toLowerCase().replace(/\\s+/g, '-') // Replace spaces with -\n    .replace(/[^\\w-]+/g, '') // Remove all non-word chars\n    .replace(/--+/g, '-') // Replace multiple - with single -\n    .replace(/^-+/, '') // Trim - from start of text\n    .replace(/-+$/, ''); // Trim - from end of text\n  }\n\n  static containsObject(obj, list) {\n    let i;\n    for (i = 0; i < list.length; i++) {\n      if (list[i].slug === obj.slug) {\n        return i;\n      }\n    }\n    return -1;\n  }\n}", "map": {"version": 3, "names": ["AppFunctions", "addClass", "element", "className", "document", "querySelector", "classList", "add", "removeClass", "remove", "toggleClass", "toggle", "flatDeep", "arr", "d", "reduce", "acc", "val", "concat", "Array", "isArray", "slice", "slugify", "text", "toString", "toLowerCase", "replace", "containsObject", "obj", "list", "i", "length", "slug"], "sources": ["C:/Users/<USER>/Desktop/code/crystal-mall/icha/crystal-website/src/utils/AppFunctions.js"], "sourcesContent": ["export default class AppFunctions {\r\n    static addClass(element, className) {\r\n        document.querySelector(element).classList.add(className)\r\n    }\r\n\r\n    static removeClass(element, className) {\r\n        document.querySelector(element).classList.remove(className)\r\n    }\r\n\r\n    static toggleClass(element, className) {\r\n        document.querySelector(element).classList.toggle(className)\r\n    }\r\n\r\n    static flatDeep(arr, d = 1) {\r\n        return d > 0 ? arr.reduce((acc, val) => acc.concat(Array.isArray(val) ? this.flatDeep(val, d - 1) : val), []) : arr.slice();\r\n    }\r\n\r\n    static slugify(text) {\r\n        return text\r\n            .toString()\r\n            .toLowerCase()\r\n            .replace(/\\s+/g, '-') // Replace spaces with -\r\n            .replace(/[^\\w-]+/g, '') // Remove all non-word chars\r\n            .replace(/--+/g, '-') // Replace multiple - with single -\r\n            .replace(/^-+/, '') // Trim - from start of text\r\n            .replace(/-+$/, '') // Trim - from end of text\r\n    }\r\n\r\n    static containsObject(obj, list) {\r\n        let i;\r\n        for (i = 0; i < list.length; i++) {\r\n            if (list[i].slug === obj.slug) {\r\n                return i;\r\n            }\r\n        }\r\n\r\n        return -1;\r\n    }\r\n}"], "mappings": "AAAA,eAAe,MAAMA,YAAY,CAAC;EAC9B,OAAOC,QAAQA,CAACC,OAAO,EAAEC,SAAS,EAAE;IAChCC,QAAQ,CAACC,aAAa,CAACH,OAAO,CAAC,CAACI,SAAS,CAACC,GAAG,CAACJ,SAAS,CAAC;EAC5D;EAEA,OAAOK,WAAWA,CAACN,OAAO,EAAEC,SAAS,EAAE;IACnCC,QAAQ,CAACC,aAAa,CAACH,OAAO,CAAC,CAACI,SAAS,CAACG,MAAM,CAACN,SAAS,CAAC;EAC/D;EAEA,OAAOO,WAAWA,CAACR,OAAO,EAAEC,SAAS,EAAE;IACnCC,QAAQ,CAACC,aAAa,CAACH,OAAO,CAAC,CAACI,SAAS,CAACK,MAAM,CAACR,SAAS,CAAC;EAC/D;EAEA,OAAOS,QAAQA,CAACC,GAAG,EAAEC,CAAC,GAAG,CAAC,EAAE;IACxB,OAAOA,CAAC,GAAG,CAAC,GAAGD,GAAG,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,CAACE,MAAM,CAACC,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAG,IAAI,CAACL,QAAQ,CAACK,GAAG,EAAEH,CAAC,GAAG,CAAC,CAAC,GAAGG,GAAG,CAAC,EAAE,EAAE,CAAC,GAAGJ,GAAG,CAACQ,KAAK,CAAC,CAAC;EAC/H;EAEA,OAAOC,OAAOA,CAACC,IAAI,EAAE;IACjB,OAAOA,IAAI,CACNC,QAAQ,CAAC,CAAC,CACVC,WAAW,CAAC,CAAC,CACbC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAAA,CACrBA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;IAAA,CACxBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAAA,CACrBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAAA,CACnBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAC;EAC5B;;EAEA,OAAOC,cAAcA,CAACC,GAAG,EAAEC,IAAI,EAAE;IAC7B,IAAIC,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAC9B,IAAID,IAAI,CAACC,CAAC,CAAC,CAACE,IAAI,KAAKJ,GAAG,CAACI,IAAI,EAAE;QAC3B,OAAOF,CAAC;MACZ;IACJ;IAEA,OAAO,CAAC,CAAC;EACb;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}