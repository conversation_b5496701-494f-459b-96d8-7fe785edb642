package com.crystal.admin.controller;

import com.crystal.common.model.cms.CmsWorkshopAttendEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsWorkshopAttendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;

/**
 * 工作坊报名 控制器
 * | Author: 陈佳音
 * ｜ @date Mon Apr 29 10:10:51 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/workshop/attend")
public class CmsWorkshopAttendController {
    @Autowired
    private CmsWorkshopAttendService cmsWorkshopAttendService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('cmsworkshopattend:list')")
    public CommonResult<CommonPage<CmsWorkshopAttendEntity>> list(@Validated CmsWorkshopAttendEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsWorkshopAttendEntity> page = CommonPage.restPage(cmsWorkshopAttendService.queryPage(request,pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('cmsworkshopattend:info')")
    public CommonResult<CmsWorkshopAttendEntity> info(@PathVariable("id") Long id){
        CmsWorkshopAttendEntity cmsWorkshopAttend = cmsWorkshopAttendService.getById(id);
        return CommonResult.success(cmsWorkshopAttend);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('cmsworkshopattend:save')")
    public CommonResult<String> save(@RequestBody CmsWorkshopAttendEntity cmsWorkshopAttend){
        cmsWorkshopAttend.setAddTime(new Date());
        cmsWorkshopAttendService.save(cmsWorkshopAttend);
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('cmsworkshopattend:update')")
    public CommonResult<String> update(@RequestBody CmsWorkshopAttendEntity cmsWorkshopAttend){
        cmsWorkshopAttendService.updateById(cmsWorkshopAttend);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('cmsworkshopattend:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids){
        if (cmsWorkshopAttendService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 