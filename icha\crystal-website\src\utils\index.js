
/**
 * 判断是否为移动设备
 * @returns {*|boolean}
 */
export function isMobilePhone() {
    // 获取访问链接
    let href = window.location.href;
    if(href.includes("pc=true")) {
      return false;
    }
    if(href.includes("pc=false")) {
      return true;
    }
    //获取访问的user-agent
    let ua = window.navigator.userAgent.toLowerCase();
    //判断user-agent
    // isWX = /MicroMessenger/i.test(ua); //微信端
    // isIOS = /(iPhone|iPad|iPod|iOS)/i.test(ua); //苹果家族
    // isAndroid = /(android|nexus)/i.test(ua); //安卓家族
    // isWindows = /(Windows Phone|windows[\s+]phone)/i.test(ua); //微软家族
    return /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|MicroMessenger)/i.test(ua)

}

/**
 * 手机号码
 * @param {*} s
 */
export function isMobile(s) {
    return /^1[0-9]{10}$/.test(s)
}
