<template>
	<Layout>
	<div class="forgot-page">
		<div class="forgot-container">
			<div class="forgot-left">
				<div class="forgot-logo">
					<img src="@/assets/images/logo.png" alt="国际水晶疗愈协会" class="logo-image" />
				</div>
				<div class="forgot-welcome">
					<h2>找回密码</h2>
					<h1>国际水晶疗愈协会</h1>
					<p>我们将协助您找回账户密码</p>
				</div>
				<div class="forgot-decoration">
					<div class="crystal-1"></div>
					<div class="crystal-2"></div>
					<div class="crystal-3"></div>
				</div>
			</div>

			<div class="forgot-right">
				<div class="forgot-form-container">
					<h2 class="forgot-title">找回密码</h2>
					<p class="forgot-subtitle">请按照步骤操作找回您的密码</p>

					<!-- 第一步：输入账号 -->
					<div v-if="currentStep == 1">
						<van-form @submit="submitUsername">
							<div class="form-item">
								<label for="username">用户名/手机号</label>
								<van-field
									v-model="forgotForm.username"
									name="username"
									placeholder="请输入用户名或手机号"
									:rules="[{ required: true, message: '请输入用户名/手机号' }]"
									class="forgot-input"
								/>
							</div>

							<div class="form-submit">
								<van-button round block type="primary" native-type="submit" :loading="loading">
									下一步
								</van-button>
							</div>
						</van-form>
					</div>

					<!-- 第二步：验证身份 -->
					<div v-if="currentStep == 2">
						<van-form @submit="submitVerification">
							<div class="form-item">
								<label for="phone">手机号</label>
								<van-field
									v-model="forgotForm.phone"
									name="phone"
									placeholder="请输入手机号"
									:rules="[{ required: true, message: '请输入手机号' }]"
									class="forgot-input"
									:disabled="forgotForm.phone !== ''"
								/>
							</div>
							
							<div class="form-item">
								<label for="verificationCode">验证码</label>
								<div class="verification-code-container">
									<van-field
										v-model="forgotForm.verificationCode"
										name="verificationCode"
										placeholder="请输入验证码"
										:rules="[{ required: true, message: '请输入验证码' }]"
										class="forgot-input verification-input"
									/>
									<van-button 
										size="small" 
										type="primary" 
										class="verification-btn"
										:disabled="counting > 0"
										@click="sendVerificationCode"
									>
										{{ counting > 0 ? `${counting}秒后重试` : '获取验证码' }}
									</van-button>
								</div>
							</div>

							<div class="form-submit">
								<van-button round block type="primary" native-type="submit" :loading="loading">
									下一步
								</van-button>
							</div>
							
							<div class="form-actions">
								<a @click="currentStep = 1" class="back-link">返回上一步</a>
							</div>
						</van-form>
					</div>

					<!-- 第三步：重置密码 -->
					<div v-if="currentStep == 3">
						<van-form @submit="submitReset">
							<div class="form-item">
								<label for="newPassword">新密码</label>
								<van-field
									v-model="forgotForm.newPassword"
									type="password"
									name="newPassword"
									placeholder="请输入新密码"
									:rules="[
										{ required: true, message: '请输入新密码' },
										{ pattern: /^.{8,20}$/, message: '密码长度为8-20个字符' }
									]"
									class="forgot-input"
								/>
							</div>
							
							<div class="form-item">
								<label for="confirmPassword">确认密码</label>
								<van-field
									v-model="forgotForm.confirmPassword"
									type="password"
									name="confirmPassword"
									placeholder="请再次输入新密码"
									:rules="[
										{ required: true, message: '请确认新密码' },
										{ validator: validateConfirmPassword, message: '两次输入密码不一致' }
									]"
									class="forgot-input"
								/>
							</div>

							<div class="form-submit">
								<van-button round block type="primary" native-type="submit" :loading="loading">
									重置密码
								</van-button>
							</div>
							
							<div class="form-actions">
								<a @click="currentStep = 2" class="back-link">返回上一步</a>
							</div>
						</van-form>
					</div>

					<!-- 重置成功 -->
					<div v-if="currentStep == 4" class="reset-success">
						<van-icon name="success" size="64" color="#5e258f" />
						<h3>密码重置成功</h3>
						<p>您已成功重置密码，现在可以使用新密码登录</p>
						<div class="form-submit">
							<van-button round block type="primary" @click="goToLogin">
								返回登录
							</van-button>
						</div>
					</div>

					<div class="login-link">
						<span>已经记起密码？</span>
						<router-link to="/login">立即登录</router-link>
					</div>
				</div>
			</div>
		</div>
	</div>
	</Layout>
</template>

<script>
import Layout from "@/components/common/Layout";
import { isMobilePhone } from "@/utils/index";
import Message from "@/utils/message";

export default {
	name: "ForgotPasswordView",
	data() {
		return {
			isMobilePhone: isMobilePhone(),
			loading: false,
			currentStep: 1,
			counting: 0,
			timer: null,
			forgotForm: {
				username: '',
				phone: '',
				verificationCode: '',
				newPassword: '',
				confirmPassword: ''
			}
		}
	},
	components: {
		Layout
	},
	mounted() {
		this.$wxShare();
	},
	beforeDestroy() {
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		// 验证两次密码是否一致
		validateConfirmPassword() {
			return this.forgotForm.newPassword == this.forgotForm.confirmPassword;
		},
		
		// 提交用户名
		submitUsername() {
			this.loading = true;
			// 验证用户名是否存在
			this.postRequest("/web/user/check-username", { username: this.forgotForm.username })
				.then(resp => {
					this.loading = false;
					if (resp && resp.code == 200) {
						// 用户存在，获取用户手机号（隐藏部分数字）
						this.forgotForm.phone = resp.data.phone || '';
						this.currentStep = 2;
					} else {
						Message.error(resp.message || "未找到该用户，请检查用户名");
					}
				});
		},
		
		// 发送验证码
		sendVerificationCode() {
			if (this.counting > 0) return;
			
			this.postRequest("/web/user/send-verification", { 
				username: this.forgotForm.username,
				phone: this.forgotForm.phone 
			})
				.then(resp => {
					if (resp && resp.code == 200) {
						Message.success("验证码已发送");
						this.startCounting();
					} else {
						Message.error(resp.message || "验证码发送失败");
					}
				});
		},
		
		// 开始倒计时
		startCounting() {
			this.counting = 60;
			this.timer = setInterval(() => {
				if (this.counting > 0) {
					this.counting--;
				} else {
					clearInterval(this.timer);
				}
			}, 1000);
		},
		
		// 提交验证码
		submitVerification() {
			this.loading = true;
			this.postRequest("/web/user/verify-code", {
				username: this.forgotForm.username,
				phone: this.forgotForm.phone,
				code: this.forgotForm.verificationCode
			})
				.then(resp => {
					this.loading = false;
					if (resp && resp.code == 200) {
						this.currentStep = 3;
					} else {
						Message.error(resp.message || "验证码错误或已过期");
					}
				});
		},
		
		// 提交重置密码
		submitReset() {
			if (this.forgotForm.newPassword !== this.forgotForm.confirmPassword) {
				Message.error("两次输入密码不一致");
				return;
			}
			
			this.loading = true;
			this.postRequest("/web/user/reset-password", {
				username: this.forgotForm.username,
				phone: this.forgotForm.phone,
				code: this.forgotForm.verificationCode,
				newPassword: this.forgotForm.newPassword
			})
				.then(resp => {
					this.loading = false;
					if (resp && resp.code == 200) {
						Message.success("密码重置成功");
						this.currentStep = 4;
					} else {
						Message.error(resp.message || "密码重置失败");
					}
				});
		},
		
		// 返回登录页
		goToLogin() {
			this.$router.push("/login");
		}
	}
}
</script>

<style lang="less" scoped>
.forgot-page {
	min-height: 100vh;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f9fa;
	background-image: url('@/assets/images/pattern-dark.png');
	background-repeat: repeat;
	padding: 40px 20px;
}

.forgot-container {
	display: flex;
	width: 1200px;
	min-height: 680px;
	border-radius: 20px;
	overflow: hidden;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
	background-color: #fff;
}

.forgot-left {
	flex: 1;
	background: linear-gradient(135deg, #5e258f, #8647ad);
	background-image: linear-gradient(135deg, rgba(94, 37, 143, 0.95), rgba(134, 71, 173, 0.9)), url('https://images.unsplash.com/photo-1521320226546-87b106cd055d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');
	background-size: cover;
	background-position: center;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	color: #fff;
	padding: 50px;
	overflow: hidden;
}

.forgot-logo {
	margin-bottom: 40px;
	text-align: center;
	position: relative;
	z-index: 2;
}

.logo-image {
	width: 180px;
	height: auto;
}

.forgot-welcome {
	text-align: center;
	margin-bottom: 40px;
	position: relative;
	z-index: 2;
}

.forgot-welcome h2 {
	font-size: 24px;
	font-weight: 400;
	margin-bottom: 20px;
	color: rgba(255, 255, 255, 0.9);
}

.forgot-welcome h1 {
	font-size: 38px;
	font-weight: 700;
	margin-bottom: 20px;
	text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.forgot-welcome p {
	font-size: 16px;
	line-height: 1.6;
	max-width: 400px;
	color: rgba(255, 255, 255, 0.8);
}

.forgot-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.crystal-1, .crystal-2, .crystal-3 {
	position: absolute;
	background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
	backdrop-filter: blur(5px);
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.crystal-1 {
	width: 300px;
	height: 300px;
	top: -100px;
	left: -150px;
}

.crystal-2 {
	width: 200px;
	height: 200px;
	bottom: 50px;
	right: -50px;
}

.crystal-3 {
	width: 150px;
	height: 150px;
	bottom: -50px;
	left: 100px;
}

.forgot-right {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px;
	background-color: #fff;
}

.forgot-form-container {
	width: 100%;
	max-width: 400px;
}

.forgot-title {
	font-size: 32px;
	font-weight: 700;
	color: #333;
	margin-bottom: 10px;
	text-align: center;
}

.forgot-subtitle {
	font-size: 16px;
	color: #666;
	margin-bottom: 40px;
	text-align: center;
}

.form-item {
	margin-bottom: 25px;
}

.form-item label {
	display: block;
	margin-bottom: 8px;
	font-size: 14px;
	color: #555;
	font-weight: 500;
}

.forgot-input {
	/deep/ .van-field__control {
		height: 48px;
		padding: 0 15px;
		font-size: 15px;
		background-color: #f5f7fa;
		border: 1px solid #e8eaee;
		border-radius: 8px;
	}

	/deep/ .van-field__control:focus {
		background-color: #fff;
		border-color: #5e258f;
		box-shadow: 0 0 0 3px rgba(94, 37, 143, 0.1);
	}
}

.verification-code-container {
	display: flex;
	align-items: center;
}

.verification-input {
	flex: 1;
	margin-right: 10px;
}

.verification-btn {
	height: 48px;
	background: linear-gradient(45deg, #5e258f, #8647ad);
	border-color: #5e258f;
	white-space: nowrap;
}

.form-submit {
	margin-bottom: 25px;
	
	.van-button {
		height: 50px;
		font-size: 16px;
		font-weight: 500;
		background: linear-gradient(45deg, #5e258f, #8647ad);
		border-color: #5e258f;
		transition: all 0.3s ease;
		
		&:hover {
			background: linear-gradient(45deg, #4b1e73, #733c94);
			border-color: #4b1e73;
			transform: translateY(-2px);
			box-shadow: 0 6px 15px rgba(94, 37, 143, 0.25);
		}
	}
}

.form-actions {
	text-align: center;
	margin-bottom: 20px;
	
	.back-link {
		color: #666;
		font-size: 14px;
		text-decoration: none;
		cursor: pointer;
		
		&:hover {
			color: #5e258f;
			text-decoration: underline;
		}
	}
}

.login-link {
	text-align: center;
	font-size: 14px;
	color: #666;
	
	a {
		color: #5e258f;
		font-weight: 500;
		text-decoration: none;
		margin-left: 5px;
		
		&:hover {
			text-decoration: underline;
		}
	}
}

.reset-success {
	text-align: center;
	padding: 20px 0 40px;
	
	h3 {
		font-size: 24px;
		font-weight: 600;
		margin: 20px 0 10px;
		color: #333;
	}
	
	p {
		font-size: 16px;
		color: #666;
		margin-bottom: 30px;
	}
}

@media (max-width: 1200px) {
	.forgot-container {
		width: 95%;
		flex-direction: column;
		min-height: auto;
	}
	
	.forgot-left {
		padding: 40px 20px;
	}
	
	.forgot-right {
		padding: 40px 20px;
	}
	
	.forgot-welcome h1 {
		font-size: 32px;
	}
}

@media (max-width: 767px) {
	.forgot-page {
		padding: 20px 10px;
	}
	
	.forgot-container {
		width: 100%;
		border-radius: 10px;
	}
	
	.forgot-left {
		padding: 30px 15px;
	}
	
	.forgot-right {
		padding: 30px 15px;
	}
	
	.forgot-logo .logo-image {
		width: 150px;
	}
	
	.forgot-welcome h1 {
		font-size: 28px;
	}
	
	.forgot-welcome p {
		font-size: 14px;
	}
	
	.forgot-title {
		font-size: 28px;
	}
	
	.forgot-subtitle {
		font-size: 14px;
		margin-bottom: 30px;
	}
}
</style> 