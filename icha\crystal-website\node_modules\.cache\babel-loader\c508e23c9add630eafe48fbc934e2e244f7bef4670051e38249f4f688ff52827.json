{"ast": null, "code": "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone } from \"@/utils/index\";\nimport '../assets/css/common-headers.css'; // 导入头部共用样式\n\nexport default {\n  name: \"JoinView\",\n  components: {\n    Layout\n  },\n  data() {\n    return {\n      aboutkefutime: '',\n      aboutaddress: '',\n      aboutemail: '',\n      aboutmobile: '',\n      isMobilePhone: isMobilePhone(),\n      companyInfo: {},\n      designers: [],\n      formData: {\n        name: '',\n        email: '',\n        phone: '',\n        memberType: '',\n        background: '',\n        intention: ''\n      }\n    };\n  },\n  mounted() {\n    this.$wxShare();\n    this.getConfigJoin();\n  },\n  methods: {\n    getConfigJoin() {\n      this.getRequest(\"/cms/config/join\").then(resp => {\n        if (resp && resp.code == 200) {\n          this.aboutkefutime = resp.data.aboutkefutime;\n          this.aboutaddress = resp.data.aboutaddress;\n          this.aboutemail = resp.data.aboutemail;\n          this.aboutmobile = resp.data.aboutmobile;\n        }\n      });\n    },\n    submitApplication() {\n      // 提交申请逻辑实现\n      console.log('提交的表单数据:', this.formData);\n      this.$message.success('申请已成功提交，我们将尽快与您联系！');\n      // 重置表单\n      this.formData = {\n        name: '',\n        email: '',\n        phone: '',\n        memberType: '',\n        background: '',\n        intention: ''\n      };\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "name", "components", "data", "aboutkefutime", "aboutaddress", "aboutemail", "aboutmobile", "companyInfo", "designers", "formData", "email", "phone", "memberType", "background", "intention", "mounted", "$wxShare", "getConfigJoin", "methods", "getRequest", "then", "resp", "code", "submitApplication", "console", "log", "$message", "success"], "sources": ["src/views/join.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t<div class=\"layout-container\" style=\"width: 100%\">\r\n\t\t\t<!-- 美化后的页面头部 -->\r\n\t\t\t<div class=\"hero-header-section join-header\">\r\n\t\t\t\t<div class=\"hero-content\">\r\n\t\t\t\t\t<h1 class=\"hero-title\"><i class=\"fas fa-handshake fa-spin-pulse\"></i> 加入我们</h1>\r\n\t\t\t\t\t<p class=\"hero-subtitle\">共同探索水晶能量的奥秘，成为专业疗愈社区的一员</p>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t<!-- 加入介绍 -->\r\n\t\t\t<section class=\"join-section intro-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">为什么加入ICHA</h2>\r\n\t\t\t\t\t<div class=\"join-benefits\">\r\n\t\t\t\t\t\t<div class=\"benefit-card\">\r\n\t\t\t\t\t\t\t<div class=\"benefit-icon\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-globe-asia\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>全球网络</h3>\r\n\t\t\t\t\t\t\t<p>成为ICHA一员，您将加入遍布全球的水晶疗愈专业网络，与来自不同文化和背景的同行建立联系。</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"benefit-card\">\r\n\t\t\t\t\t\t\t<div class=\"benefit-icon\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-certificate\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>专业认证</h3>\r\n\t\t\t\t\t\t\t<p>获得国际认可的专业资格证书，提升您在水晶疗愈领域的专业地位与信誉。</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"benefit-card\">\r\n\t\t\t\t\t\t\t<div class=\"benefit-icon\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-graduation-cap\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>持续学习</h3>\r\n\t\t\t\t\t\t\t<p>优先参与高级工作坊、大师班和专业培训，不断提升您的水晶疗愈技能和知识。</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"benefit-card\">\r\n\t\t\t\t\t\t\t<div class=\"benefit-icon\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-gem\"></i>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<h3>资源共享</h3>\r\n\t\t\t\t\t\t\t<p>获取专属教材、研究资料和实用工具，助力您的个人发展和专业实践。</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section>\r\n\r\n\t\t\t<!-- 会员类型 -->\r\n\t\t\t<!-- <section class=\"join-section membership-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">会员类型</h2>\r\n\t\t\t\t\t<div class=\"membership-types\">\r\n\t\t\t\t\t\t<div class=\"membership-card\">\r\n\t\t\t\t\t\t\t<div class=\"membership-header\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-user\"></i>\r\n\t\t\t\t\t\t\t\t<h3>个人会员</h3>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"membership-content\">\r\n\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 水晶爱好者</li>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 初学疗愈师</li>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 能量工作者</li>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 身心灵从业者</li>\r\n\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"membership-card\">\r\n\t\t\t\t\t\t\t<div class=\"membership-header\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-user-tie\"></i>\r\n\t\t\t\t\t\t\t\t<h3>专业会员</h3>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"membership-content\">\r\n\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 认证水晶疗愈师</li>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 授权讲师</li>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 能量疗愈专家</li>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 自然医学从业者</li>\r\n\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"membership-card\">\r\n\t\t\t\t\t\t\t<div class=\"membership-header\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-building\"></i>\r\n\t\t\t\t\t\t\t\t<h3>机构会员</h3>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"membership-content\">\r\n\t\t\t\t\t\t\t\t<ul>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 教育培训机构</li>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 水晶品牌与经销商</li>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 疗愈中心</li>\r\n\t\t\t\t\t\t\t\t\t<li><i class=\"fas fa-check\"></i> 研究组织</li>\r\n\t\t\t\t\t\t\t\t</ul>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section> -->\r\n\r\n\t\t\t<!-- 加入流程 -->\r\n\t\t\t<!-- <section class=\"join-section process-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">加入流程</h2>\r\n\t\t\t\t\t<div class=\"join-process\">\r\n\t\t\t\t\t\t<div class=\"process-step\">\r\n\t\t\t\t\t\t\t<div class=\"step-number\">1</div>\r\n\t\t\t\t\t\t\t<div class=\"step-content\">\r\n\t\t\t\t\t\t\t\t<h3><i class=\"fas fa-wpforms\"></i> 提交申请</h3>\r\n\t\t\t\t\t\t\t\t<p>填写完整的会员申请表，提供相关资质证明和个人/机构介绍。</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"process-step\">\r\n\t\t\t\t\t\t\t<div class=\"step-number\">2</div>\r\n\t\t\t\t\t\t\t<div class=\"step-content\">\r\n\t\t\t\t\t\t\t\t<h3><i class=\"fas fa-search\"></i> 资格审核</h3>\r\n\t\t\t\t\t\t\t\t<p>协会委员会将审核您的申请，确认您的专业背景和资质。</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"process-step\">\r\n\t\t\t\t\t\t\t<div class=\"step-number\">3</div>\r\n\t\t\t\t\t\t\t<div class=\"step-content\">\r\n\t\t\t\t\t\t\t\t<h3><i class=\"fas fa-comment-dots\"></i> 面谈交流</h3>\r\n\t\t\t\t\t\t\t\t<p>通过线上或线下方式与协会代表进行简短面谈，了解彼此期望。</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"process-step\">\r\n\t\t\t\t\t\t\t<div class=\"step-number\">4</div>\r\n\t\t\t\t\t\t\t<div class=\"step-content\">\r\n\t\t\t\t\t\t\t\t<h3><i class=\"fas fa-handshake\"></i> 正式加入</h3>\r\n\t\t\t\t\t\t\t\t<p>完成会费支付，签署会员协议，获得会员资格与专属权益。</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section> -->\r\n\r\n\t\t\t<!-- 申请表单 -->\r\n\t\t\t<!-- <section class=\"join-section application-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">申请加入</h2>\r\n\t\t\t\t\t<div class=\"application-form-container\">\r\n\t\t\t\t\t\t<form class=\"application-form\" @submit.prevent=\"submitApplication\">\r\n\t\t\t\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t\t\t\t<label><i class=\"fas fa-user\"></i> 姓名</label>\r\n\t\t\t\t\t\t\t\t<input type=\"text\" v-model=\"formData.name\" required placeholder=\"请输入您的姓名\">\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t\t\t\t<label><i class=\"fas fa-envelope\"></i> 电子邮箱</label>\r\n\t\t\t\t\t\t\t\t<input type=\"email\" v-model=\"formData.email\" required placeholder=\"请输入您的电子邮箱\">\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t\t\t\t<label><i class=\"fas fa-phone\"></i> 联系电话</label>\r\n\t\t\t\t\t\t\t\t<input type=\"tel\" v-model=\"formData.phone\" required placeholder=\"请输入您的联系电话\">\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t\t\t\t<label><i class=\"fas fa-users\"></i> 会员类型</label>\r\n\t\t\t\t\t\t\t\t<select v-model=\"formData.memberType\" required>\r\n\t\t\t\t\t\t\t\t\t<option value=\"\">请选择会员类型</option>\r\n\t\t\t\t\t\t\t\t\t<option value=\"individual\">个人会员</option>\r\n\t\t\t\t\t\t\t\t\t<option value=\"professional\">专业会员</option>\r\n\t\t\t\t\t\t\t\t\t<option value=\"institutional\">机构会员</option>\r\n\t\t\t\t\t\t\t\t</select>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"form-group full-width\">\r\n\t\t\t\t\t\t\t\t<label><i class=\"fas fa-briefcase\"></i> 专业背景</label>\r\n\t\t\t\t\t\t\t\t<textarea v-model=\"formData.background\" rows=\"3\" placeholder=\"请简述您的专业背景与相关经验\"></textarea>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"form-group full-width\">\r\n\t\t\t\t\t\t\t\t<label><i class=\"fas fa-comment\"></i> 加入意向</label>\r\n\t\t\t\t\t\t\t\t<textarea v-model=\"formData.intention\" rows=\"3\" placeholder=\"请分享您希望加入ICHA的原因和期望\"></textarea>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"form-group submit-group\">\r\n\t\t\t\t\t\t\t\t<button type=\"submit\" class=\"submit-btn\">提交申请</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</form>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section> -->\r\n\r\n\t\t\t<!-- 联系信息 -->\r\n\t\t\t<section class=\"join-section contact-section\">\r\n\t\t\t\t<div class=\"content-wrapper\">\r\n\t\t\t\t\t<h2 class=\"section-title\">联系我们</h2>\r\n\t\t\t\t\t<div class=\"contact-methods\">\r\n\t\t\t\t\t\t<div class=\"contact-method\">\r\n\t\t\t\t\t\t\t<i class=\"fas fa-phone-alt\"></i>\r\n\t\t\t\t\t\t\t<h3>电话咨询</h3>\r\n\t\t\t\t\t\t\t<p>{{ aboutmobile }}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"contact-method\">\r\n\t\t\t\t\t\t\t<i class=\"fas fa-envelope-open-text\"></i>\r\n\t\t\t\t\t\t\t<h3>电子邮件</h3>\r\n\t\t\t\t\t\t\t<p>{{ aboutemail }}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"contact-method\">\r\n\t\t\t\t\t\t\t<i class=\"fas fa-map-marker-alt\"></i>\r\n\t\t\t\t\t\t\t<h3>总部地址</h3>\r\n\t\t\t\t\t\t\t<p>{{ aboutaddress }}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div class=\"contact-method\">\r\n\t\t\t\t\t\t\t<i class=\"fas fa-comments\"></i>\r\n\t\t\t\t\t\t\t<h3>咨询时间</h3>\r\n\t\t\t\t\t\t\t<p>{{ aboutkefutime }}</p>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</section>\r\n\t\t</div>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone } from \"@/utils/index\";\r\nimport '../assets/css/common-headers.css'; // 导入头部共用样式\r\n\r\nexport default {\r\n\tname: \"JoinView\",\r\n\tcomponents: { Layout },\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\taboutkefutime: '',\r\n\t\t\taboutaddress: '',\r\n\t\t\taboutemail: '',\r\n\t\t\taboutmobile: '',\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tcompanyInfo: {},\r\n\t\t\tdesigners: [],\r\n\t\t\tformData: {\r\n\t\t\t\tname: '',\r\n\t\t\t\temail: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tmemberType: '',\r\n\t\t\t\tbackground: '',\r\n\t\t\t\tintention: ''\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$wxShare();\r\n\t\tthis.getConfigJoin()\r\n\t},\r\n\tmethods: {\r\n\t\tgetConfigJoin() {\r\n\t\t\tthis.getRequest(\"/cms/config/join\").then(resp => {\r\n\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\tthis.aboutkefutime = resp.data.aboutkefutime;\r\n\t\t\t\t\tthis.aboutaddress = resp.data.aboutaddress;\r\n\t\t\t\t\tthis.aboutemail = resp.data.aboutemail;\r\n\t\t\t\t\tthis.aboutmobile = resp.data.aboutmobile;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tsubmitApplication() {\r\n\t\t\t// 提交申请逻辑实现\r\n\t\t\tconsole.log('提交的表单数据:', this.formData);\r\n\t\t\tthis.$message.success('申请已成功提交，我们将尽快与您联系！');\r\n\t\t\t// 重置表单\r\n\t\t\tthis.formData = {\r\n\t\t\t\tname: '',\r\n\t\t\t\temail: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tmemberType: '',\r\n\t\t\t\tbackground: '',\r\n\t\t\t\tintention: ''\r\n\t\t\t};\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 美化后的页面头部样式 */\r\n.join-header {\r\n\tbackground-image: url('https://img.freepik.com/free-photo/esoteric-concept-with-spiritual-crystals_23-2149320909.jpg') !important;\r\n}\r\n\r\n/* 页面整体样式 */\r\n.content-wrapper {\r\n\tmax-width: 1200px;\r\n\tmargin: 0 auto;\r\n\tpadding: 20px;\r\n}\r\n\r\n.section-title {\r\n\ttext-align: center;\r\n\tfont-size: 32px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 40px;\r\n\tposition: relative;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.section-title:after {\r\n\tcontent: '';\r\n\tdisplay: block;\r\n\twidth: 60px;\r\n\theight: 3px;\r\n\tbackground-color: #516790;\r\n\tmargin: 15px auto 0;\r\n}\r\n\r\n/* 加入介绍部分 */\r\n.join-section {\r\n\tpadding: 60px 0;\r\n}\r\n\r\n.join-section:nth-child(even) {\r\n\tbackground-color: #f9f9f9;\r\n}\r\n\r\n.join-benefits {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tjustify-content: center;\r\n\tgap: 30px;\r\n}\r\n\r\n.benefit-card {\r\n\tflex: 1;\r\n\tmin-width: 250px;\r\n\tmax-width: 300px;\r\n\tpadding: 25px;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n\ttext-align: center;\r\n\ttransition: transform 0.3s, box-shadow 0.3s;\r\n}\r\n\r\n.benefit-card:hover {\r\n\ttransform: translateY(-5px);\r\n\tbox-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.benefit-icon {\r\n\twidth: 80px;\r\n\theight: 80px;\r\n\tmargin: 0 auto 20px;\r\n\tbackground-color: #516790;\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.benefit-icon i {\r\n\tfont-size: 36px;\r\n\tcolor: white;\r\n}\r\n\r\n.benefit-card h3 {\r\n\tfont-size: 20px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 15px;\r\n}\r\n\r\n.benefit-card p {\r\n\tcolor: #666;\r\n\tline-height: 1.6;\r\n}\r\n\r\n/* 会员类型部分 */\r\n.membership-types {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tjustify-content: center;\r\n\tgap: 30px;\r\n}\r\n\r\n.membership-card {\r\n\tflex: 1;\r\n\tmin-width: 250px;\r\n\tmax-width: 350px;\r\n\tborder-radius: 10px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.membership-header {\r\n\tbackground-color: #516790;\r\n\tcolor: white;\r\n\tpadding: 20px;\r\n\ttext-align: center;\r\n}\r\n\r\n.membership-header i {\r\n\tfont-size: 32px;\r\n\tmargin-bottom: 10px;\r\n}\r\n\r\n.membership-header h3 {\r\n\tfont-size: 22px;\r\n\tmargin: 0;\r\n}\r\n\r\n.membership-content {\r\n\tpadding: 25px;\r\n}\r\n\r\n.membership-content ul {\r\n\tlist-style: none;\r\n\tpadding: 0;\r\n\tmargin: 0;\r\n}\r\n\r\n.membership-content li {\r\n\tmargin-bottom: 12px;\r\n\tpadding-left: 28px;\r\n\tposition: relative;\r\n\tcolor: #555;\r\n}\r\n\r\n.membership-content li i {\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\ttop: 3px;\r\n\tcolor: #516790;\r\n}\r\n\r\n/* 加入流程部分 */\r\n.join-process {\r\n\tmax-width: 800px;\r\n\tmargin: 0 auto;\r\n}\r\n\r\n.process-step {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 30px;\r\n\talign-items: center;\r\n}\r\n\r\n.step-number {\r\n\twidth: 50px;\r\n\theight: 50px;\r\n\tbackground-color: #516790;\r\n\tborder-radius: 50%;\r\n\tcolor: white;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 24px;\r\n\tfont-weight: bold;\r\n\tmargin-right: 20px;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.step-content {\r\n\tflex: 1;\r\n\tbackground-color: #fff;\r\n\tpadding: 20px;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.step-content h3 {\r\n\tmargin-top: 0;\r\n\tcolor: #333;\r\n\tfont-size: 18px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.step-content h3 i {\r\n\tmargin-right: 10px;\r\n\tcolor: #516790;\r\n}\r\n\r\n.step-content p {\r\n\tmargin-bottom: 0;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 申请表单部分 */\r\n.application-form-container {\r\n\tmax-width: 800px;\r\n\tmargin: 0 auto;\r\n\tbackground-color: #fff;\r\n\tpadding: 30px;\r\n\tborder-radius: 10px;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.application-form {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 20px;\r\n}\r\n\r\n.form-group {\r\n\tflex: 1 0 calc(50% - 10px);\r\n}\r\n\r\n.full-width {\r\n\tflex: 1 0 100%;\r\n}\r\n\r\n.form-group label {\r\n\tdisplay: block;\r\n\tmargin-bottom: 8px;\r\n\tfont-weight: 500;\r\n\tcolor: #333;\r\n}\r\n\r\n.form-group label i {\r\n\tmargin-right: 8px;\r\n\tcolor: #516790;\r\n}\r\n\r\n.form-group input,\r\n.form-group select,\r\n.form-group textarea {\r\n\twidth: 100%;\r\n\tpadding: 12px 15px;\r\n\tborder: 1px solid #ddd;\r\n\tborder-radius: 4px;\r\n\tfont-size: 16px;\r\n\ttransition: border-color 0.3s;\r\n}\r\n\r\n.form-group input:focus,\r\n.form-group select:focus,\r\n.form-group textarea:focus {\r\n\tborder-color: #516790;\r\n\toutline: none;\r\n}\r\n\r\n.submit-group {\r\n\tflex: 1 0 100%;\r\n\ttext-align: center;\r\n\tmargin-top: 10px;\r\n}\r\n\r\n.submit-btn {\r\n\tbackground-color: #516790;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tpadding: 12px 40px;\r\n\tfont-size: 16px;\r\n\tborder-radius: 4px;\r\n\tcursor: pointer;\r\n\ttransition: background-color 0.3s;\r\n}\r\n\r\n.submit-btn:hover {\r\n\tbackground-color: #405580;\r\n}\r\n\r\n/* 联系信息部分 */\r\n.contact-methods {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tjustify-content: center;\r\n\tgap: 30px;\r\n}\r\n\r\n.contact-method {\r\n\tflex: 1;\r\n\tmin-width: 200px;\r\n\tmax-width: 250px;\r\n\ttext-align: center;\r\n\tpadding: 25px;\r\n\tbackground-color: #fff;\r\n\tborder-radius: 8px;\r\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.contact-method i {\r\n\tfont-size: 40px;\r\n\tcolor: #516790;\r\n\tmargin-bottom: 15px;\r\n}\r\n\r\n.contact-method h3 {\r\n\tfont-size: 18px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10px;\r\n}\r\n\r\n.contact-method p {\r\n\tcolor: #666;\r\n}\r\n\r\n/* 响应式布局 */\r\n@media (max-width: 992px) {\r\n\t.benefit-card,\r\n\t.membership-card,\r\n\t.contact-method {\r\n\t\tmin-width: 200px;\r\n\t}\r\n}\r\n\r\n@media (max-width: 768px) {\r\n\t.section-title {\r\n\t\tfont-size: 28px;\r\n\t}\r\n\t\r\n\t.join-benefits,\r\n\t.membership-types,\r\n\t.contact-methods {\r\n\t\tgap: 20px;\r\n\t}\r\n\t\r\n\t.form-group {\r\n\t\tflex: 1 0 100%;\r\n\t}\r\n}\r\n\r\n@media (max-width: 576px) {\r\n\t.join-section {\r\n\t\tpadding: 40px 0;\r\n\t}\r\n\t\r\n\t.section-title {\r\n\t\tfont-size: 24px;\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n\t\r\n\t.benefit-card,\r\n\t.membership-card,\r\n\t.contact-method {\r\n\t\tmin-width: 100%;\r\n\t}\r\n\t\r\n\t.process-step {\r\n\t\tflex-direction: column;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.step-number {\r\n\t\tmargin-right: 0;\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\t\r\n\t.application-form-container {\r\n\t\tpadding: 20px;\r\n\t}\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoNA,OAAAA,MAAA;AACA,SAAAC,aAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IAAAH;EAAA;EACAI,KAAA;IACA;MACAC,aAAA;MACAC,YAAA;MACAC,UAAA;MACAC,WAAA;MACAP,aAAA,EAAAA,aAAA;MACAQ,WAAA;MACAC,SAAA;MACAC,QAAA;QACAT,IAAA;QACAU,KAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,SAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAD,cAAA;MACA,KAAAE,UAAA,qBAAAC,IAAA,CAAAC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,IAAA;UACA,KAAAnB,aAAA,GAAAkB,IAAA,CAAAnB,IAAA,CAAAC,aAAA;UACA,KAAAC,YAAA,GAAAiB,IAAA,CAAAnB,IAAA,CAAAE,YAAA;UACA,KAAAC,UAAA,GAAAgB,IAAA,CAAAnB,IAAA,CAAAG,UAAA;UACA,KAAAC,WAAA,GAAAe,IAAA,CAAAnB,IAAA,CAAAI,WAAA;QACA;MACA;IACA;IACAiB,kBAAA;MACA;MACAC,OAAA,CAAAC,GAAA,kBAAAhB,QAAA;MACA,KAAAiB,QAAA,CAAAC,OAAA;MACA;MACA,KAAAlB,QAAA;QACAT,IAAA;QACAU,KAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,SAAA;MACA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}