package com.crystal.admin.controller;

import com.crystal.common.model.question.QuestionOptionEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.QuestionOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * 用户手串 控制器
 * | Author: 陈佳音
 * ｜ @date Fri Nov 22 16:43:13 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/questionoption")
public class QuestionOptionController {
    @Autowired
    private QuestionOptionService questionOptionService;

    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('userbraceletsitem:list')")
    public CommonResult<CommonPage<QuestionOptionEntity>> list(@Validated QuestionOptionEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<QuestionOptionEntity> page = CommonPage.restPage(questionOptionService.queryPage(request,pageParamRequest));

        return CommonResult.success(page);
    }

    @RequestMapping("/findByQuestionId/{questionId}")
    public CommonResult<List<QuestionOptionEntity>> findByQuestionId(@PathVariable("questionId") Long questionId) {
        List<QuestionOptionEntity> page = questionOptionService.findByQuestionId(questionId);

        return CommonResult.success(page);
    }


    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('userbraceletsitem:info')")
    public CommonResult<QuestionOptionEntity> info(@PathVariable("id") Integer id){
		QuestionOptionEntity questionOption = questionOptionService.getById(id);

        return CommonResult.success(questionOption);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
    @Transactional(rollbackFor = Exception.class)
//    @PreAuthorize("hasAuthority('userbraceletsitem:save')")
    public CommonResult<String> save(@RequestBody QuestionOptionEntity questionOption){
        questionOption.setAddTime(new Date());
        if (questionOptionService.save(questionOption)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
    @Transactional(rollbackFor = Exception.class)
//    @PreAuthorize("hasAuthority('userbraceletsitem:update')")
    public CommonResult<String> update(@RequestBody QuestionOptionEntity questionOption){
        questionOption.setUpdateTime(new Date());
        if (questionOptionService.updateById(questionOption)) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('userbraceletsitem:delete')")
    public CommonResult<String> delete(@RequestBody Integer[] ids){
        if (questionOptionService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }

}
