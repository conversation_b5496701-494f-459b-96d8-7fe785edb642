package com.crystal.admin.controller;

import com.crystal.common.model.cms.CmsHealersEntity;
import com.crystal.common.page.CommonPage;
import com.crystal.common.request.PageParamRequest;
import com.crystal.common.response.CommonResult;
import com.crystal.service.service.CmsHealersService;
import com.crystal.service.service.SystemAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;

/**
 * 疗愈师 控制器
 * | Author: 陈佳音
 * ｜ @date Mon Apr 29 10:10:51 CST 2024
 * ｜ @date <EMAIL>
 */
@RestController
@RequestMapping("api/admin/cms/healers")
public class CmsHealersController {
    @Autowired
    private CmsHealersService cmsHealersService;
    @Autowired
    private SystemAttachmentService systemAttachmentService;


    /**
     * 列表信息
     */
    @RequestMapping("/list")
//    @PreAuthorize("hasAuthority('cmshealers:list')")
    public CommonResult<CommonPage<CmsHealersEntity>> list(@Validated CmsHealersEntity request, @Validated PageParamRequest pageParamRequest) {
        CommonPage<CmsHealersEntity> page = CommonPage.restPage(cmsHealersService.queryPage(request,pageParamRequest));
        return CommonResult.success(page);
    }

    /**
     * 详情数据
     */
    @RequestMapping("/info/{id}")
//    @PreAuthorize("hasAuthority('cmshealers:info')")
    public CommonResult<CmsHealersEntity> info(@PathVariable("id") Long id){
        CmsHealersEntity cmsHealers = cmsHealersService.getById(id);
        return CommonResult.success(cmsHealers);
    }

    /**
     * 新增数据
     */
    @RequestMapping("/save")
//    @PreAuthorize("hasAuthority('cmshealers:save')")
    public CommonResult<String> save(@RequestBody CmsHealersEntity cmsHealers){
        cmsHealers.setAddTime(new Date());
        cmsHealers.setAvatar(systemAttachmentService.clearPrefix(cmsHealers.getAvatar()));
        cmsHealers.setContent(systemAttachmentService.clearPrefix(cmsHealers.getContent()));
        cmsHealersService.save(cmsHealers);
        return CommonResult.success();
    }

    /**
     * 修改数据
     */
    @RequestMapping("/update")
//    @PreAuthorize("hasAuthority('cmshealers:update')")
    public CommonResult<String> update(@RequestBody CmsHealersEntity cmsHealers){
        cmsHealers.setAvatar(systemAttachmentService.clearPrefix(cmsHealers.getAvatar()));
        cmsHealers.setContent(systemAttachmentService.clearPrefix(cmsHealers.getContent()));
        cmsHealersService.updateById(cmsHealers);
        return CommonResult.success();
    }

    /**
     * 删除:根据id集合
     */
    @RequestMapping("/delete")
//    @PreAuthorize("hasAuthority('cmshealers:delete')")
    public CommonResult<String> delete(@RequestBody Long[] ids){
        if (cmsHealersService.removeByIds(Arrays.asList(ids))) {
            return CommonResult.success();
        }
        return CommonResult.failed();
    }
} 