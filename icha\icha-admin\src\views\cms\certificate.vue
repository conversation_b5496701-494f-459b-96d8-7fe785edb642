<template>
  <div class="certificate-container">
    <!-- 搜索栏 -->
    <el-card class="search-container">
      <el-form :inline="true" :model="queryParams" ref="queryForm" size="small">
        <el-form-item label="证书编号" prop="number">
          <el-input v-model="queryParams.number" placeholder="请输入证书编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="用户名" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入用户名" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="证书类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择证书类型" clearable>
            <el-option v-for="item in certificateTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="table-container">
      <div class="table-header">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple"
          @click="handleDelete">删除</el-button>
      </div>

      <!-- 证书表格 -->
      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="80" />
        <el-table-column label="证书编号" align="center" prop="number" />
        <el-table-column label="证书图片" align="center" prop="image">
          <template slot-scope="scope">
            <el-image :src="scope.row.image" style="width: 100px; height: 100px;" />
          </template>
        </el-table-column>
        <el-table-column label="用户名" align="center" prop="name" />
        <el-table-column label="证书类型" align="center" prop="type">
          <template slot-scope="scope">
            {{ getCertificateTypeName(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column label="颁证日期" align="center" width="240">
          <template slot-scope="scope">
            {{ scope.row.startTime }}
            <!-- {{ scope.row.startTime }} 至 {{ scope.row.endTime }} -->
          </template>
        </el-table-column>
        <el-table-column label="发证机构" align="center" prop="issuer" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="block">
        <el-pagination :page-sizes="[20, 40, 60, 80]" :page-size="queryParams.limit"
          :current-page="queryParams.page" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="pageChange" />
      </div>
    </el-card>

    <!-- 新增/修改弹窗 -->
    <certificate-add-or-update ref="addOrUpdate" @refreshDataList="getList"></certificate-add-or-update>
  </div>
</template>

<script>
import {
  cmsCertificateListApi,
  cmsCertificateDeleteApi
} from '@/api/cmsCertificate'
import CertificateAddOrUpdate from './certificate-add-or-update'
import { certificateType } from '@/data/common'

export default {
  name: 'Certificate',
  components: {
    CertificateAddOrUpdate
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 证书列表
      dataList: [],
      // 查询参数
      queryParams: {
        page: 1,
        limit: 20,
        number: undefined,
        name: undefined,
        type: undefined
      },
      // 证书类型选项
      certificateTypes: certificateType
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询证书列表 */
    getList() {
      this.loading = true
      cmsCertificateListApi(this.queryParams).then(res => {
        this.dataList = res.list || [];
        this.total = parseInt(res.total);
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.addOrUpdate.init()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.addOrUpdate.init(row.id)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除选中的数据?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return cmsCertificateDeleteApi([].concat(ids))
      }).then(() => {
        this.getList()
        this.$message.success('删除成功')
      }).catch(() => { })
    },
    /** 获取证书类型名称 */
    getCertificateTypeName(type) {
      const item = this.certificateTypes[type]
      return item ? item.value : ''
    },
    // 页码改变
    pageChange(page) {
      this.queryParams.page = page
      this.getList()
    },
    // 每页条数改变
    handleSizeChange(val) {
      this.queryParams.limit = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.certificate-container {
  padding: 20px;

  .search-container {
    margin-bottom: 20px;
  }

  .table-container {
    .table-header {
      margin-bottom: 20px;
    }
  }
}
</style>