<template>
  <div class="course-video-container">
    <!-- 搜索栏 -->
    <el-card class="search-container">
      <el-form :inline="true" :model="queryParams" ref="queryForm" size="small">
        <!-- <el-form-item label="课程ID" prop="cmsCourseId">
          <el-input v-model="queryParams.cmsCourseId" placeholder="请输入课程ID" clearable @keyup.enter.native="handleQuery" />
        </el-form-item> -->
        <el-form-item label="视频名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入视频名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <!-- <el-form-item label="视频类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择视频类型" clearable>
            <el-option v-for="item in videoTypeOptions" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="table-container">
      <div class="table-header">
        <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        <el-button type="danger" icon="el-icon-delete" size="small" :disabled="multiple"
          @click="handleDelete">删除</el-button>
          <el-button type="primary" style="float: right;" size="small" @click="$router.go(-1)">返回上一页</el-button>
      </div>

      <!-- 课程视频表格 -->
      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="视频名称" align="center" prop="name" />
        <el-table-column label="视频封面" align="center" prop="cover">
          <template slot-scope="scope">
            <img :src="scope.row.cover" alt="视频封面" style="width: 50px; height: 50px;">
          </template>
        </el-table-column>
        <!-- <el-table-column label="视频类型" align="center" prop="type">
          <template slot-scope="scope">
            {{ getTypeLabel(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column label="邀请码" align="center" prop="inviteCode" /> -->
        <el-table-column label="视频id" align="center" prop="fileId" />
        <el-table-column label="视频时长(秒)" align="center" prop="duration" />
        <el-table-column label="文件大小" align="center" prop="fileSize">
          <template slot-scope="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column label="资料下载" align="center" prop="file" width="120">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.file"
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="downloadFile(scope.row.file)"
            >
              下载
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="paixu" width="80" />
        <el-table-column label="添加时间" align="center" prop="addTime" width="160" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="block">
        <el-pagination :page-sizes="[20, 40, 60, 80]" :page-size="queryParams.limit"
          :current-page="queryParams.page" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="handleSizeChange" @current-change="pageChange" />
      </div>
    </el-card>

    <!-- 新增/修改弹窗 -->
    <course-video-add-or-update ref="addOrUpdate" @refreshDataList="getList"></course-video-add-or-update>
  </div>
</template>

<script>
import {
  cmsCourseVideoListApi,
  cmsCourseVideoDeleteApi
} from '@/api/cmsCourseVideo'
import CourseVideoAddOrUpdate from './course-video-add-or-update'
import { videoType } from '@/data/common'

export default {
  name: 'CourseVideo',
  components: {
    CourseVideoAddOrUpdate
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 课程视频列表
      dataList: [],
      // 查询参数
      queryParams: {
        page: 1,
        limit: 20,
        cmsCourseId: undefined,
        name: undefined,
        type: undefined
      },
      // 视频类型选项
      videoTypeOptions: videoType
    }
  },
  created() {
    this.queryParams.cmsCourseId = this.$route.query.id
    this.getList()
  },
  methods: {
    /** 查询课程视频列表 */
    getList() {
      this.loading = true
      cmsCourseVideoListApi(this.queryParams).then(res => {
        this.dataList = res.list || [];
        this.total = parseInt(res.total);
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.addOrUpdate.init('',this.queryParams.cmsCourseId)
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$refs.addOrUpdate.init(row.id, this.queryParams.cmsCourseId)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除选中的数据?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return cmsCourseVideoDeleteApi([].concat(ids))
      }).then(() => {
        this.getList()
        this.$message.success('删除成功')
      }).catch(() => { })
    },
    /** 获取类型标签 */
    getTypeLabel(type) {
      const found = this.videoTypeOptions.find(item => item.key === type)
      return found ? found.value : ''
    },
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let i = 0
      while (size >= 1024 && i < units.length - 1) {
        size /= 1024
        i++
      }
      return size.toFixed(2) + ' ' + units[i]
    },
    // 页码改变
    pageChange(page) {
      this.queryParams.page = page
      this.getList()
    },
    // 每页条数改变
    handleSizeChange(val) {
      this.queryParams.limit = val
      this.getList()
    },
    /** 下载文件 */
    downloadFile(fileUrl) {
      if (!fileUrl) {
        this.$message.warning('文件不存在')
        return
      }
      // 创建一个临时的a标签来下载文件
      const link = document.createElement('a')
      link.href = fileUrl
      link.download = fileUrl.split('/').pop() || 'download'
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

<style lang="scss" scoped>
.course-video-container {
  padding: 20px;

  .search-container {
    margin-bottom: 20px;
  }

  .table-container {
    .table-header {
      margin-bottom: 20px;
    }
  }
}
</style> 