<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.crystal.service.dao.CmsCourseVideoDao">

    <!-- 根据包名 模块名 以及类名 生成Mapper XML 配置文件 -->
    <resultMap type="com.crystal.common.model.cms.CmsCourseVideoEntity" id="cmsCourseVideoMap">
        <result property="id" column="id"/>
        <result property="cmsCourseId" column="cms_course_id"/>
        <result property="name" column="name"/>
        <result property="fileId" column="file_id"/>
        <result property="fileSize" column="file_size"/>
        <result property="duration" column="duration"/>
        <result property="mediaUrl" column="media_url"/>
        <result property="frameRate" column="frame_rate"/>
        <result property="updateTime" column="update_time"/>
        <result property="paixu" column="paixu"/>
        <result property="addTime" column="add_time"/>
        <result property="isDel" column="is_del"/>
        <result property="type" column="type"/>
        <result property="inviteCode" column="invite_code"/>
    </resultMap>

</mapper> 