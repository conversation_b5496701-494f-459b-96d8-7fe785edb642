<template>
  <Layout>
    <div class="chakra-intro-page">
    <!-- 标题栏 -->
    <div class="nav-title">
      <div class="nav-left">
        <van-button 
          class="back-button"
          @click="goBack"
          plain
        >
          <van-icon name="arrow-left" size="18" />
        </van-button>
        <div class="color-bar"></div>
        <div class="title-text">脉轮简介</div>
      </div>
      <div class="nav-right">
        <van-button 
          class="test-button-small"
          @click="goToTest"
          size="small"
        >
          <van-icon name="play" size="14" />
          <span>开始测试</span>
        </van-button>
        <van-button 
          class="balance-button-small"
          @click="goToBalance"
          size="small"
          type="primary"
        >
          <van-icon name="balance" size="14" />
          <span>平衡脉轮</span>
        </van-button>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-container">
      <div class="intro-content">
        <div class="chakra-item" v-for="(chakra, index) in chakraList" :key="index">
          <div class="chakra-header">
            <div class="chakra-dot" :style="{ backgroundColor: chakra.color }"></div>
            <div class="chakra-info">
              <h3 class="chakra-name">{{ chakra.name }}</h3>
              <p class="chakra-english">{{ chakra.english }}</p>
            </div>
          </div>
          <div class="chakra-description">
            <p>{{ chakra.description }}</p>
          </div>
          <div class="chakra-details">
            <div class="detail-item">
              <span class="label">位置：</span>
              <span class="value">{{ chakra.location }}</span>
            </div>
            <div class="detail-item">
              <span class="label">功能：</span>
              <span class="value">{{ chakra.function }}</span>
            </div>
            <div class="detail-item">
              <span class="label">关键词：</span>
              <span class="value">{{ chakra.keywords }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/common/Layout.vue'

export default {
  components: {
    Layout
  },
  name: 'ChakraTestIntro',
  data() {
    return {
      chakraList: [
        {
          name: '海底轮',
          english: 'Root Chakra',
          color: '#993734',
          location: '脊椎底部',
          function: '生存、安全感、稳定',
          keywords: '根基、安全、生存',
          description: '海底轮是人体能量系统的根基，掌管着我们的生存本能、安全感和与大地的连接。当海底轮平衡时，我们会感到稳定、安全和有根基。'
        },
        {
          name: '脐轮',
          english: 'Sacral Chakra',
          color: '#be6f2a',
          location: '下腹部',
          function: '创造力、情感、性能量',
          keywords: '创造、情感、欲望',
          description: '脐轮是创造力和情感的中心，掌管着我们的创造力、性能量和情感表达。平衡的脐轮让我们能够自由地表达情感和创造力。'
        },
        {
          name: '太阳轮',
          english: 'Solar Plexus Chakra',
          color: '#d7c34a',
          location: '上腹部',
          function: '个人力量、自信、意志',
          keywords: '力量、自信、意志',
          description: '太阳轮是个人力量的中心，掌管着我们的自信、意志力和个人力量。当太阳轮平衡时，我们会感到自信、有力量和能够掌控自己的生活。'
        },
        {
          name: '心轮',
          english: 'Heart Chakra',
          color: '#5f9057',
          location: '胸部中央',
          function: '爱、同情、连接',
          keywords: '爱、同情、和谐',
          description: '心轮是爱和同情的中心，掌管着我们给予和接受爱的能力。平衡的心轮让我们能够无条件地爱自己和他人。'
        },
        {
          name: '喉轮',
          english: 'Throat Chakra',
          color: '#5b8aa4',
          location: '喉咙',
          function: '沟通、表达、真实',
          keywords: '表达、沟通、真实',
          description: '喉轮是沟通和表达的中心，掌管着我们说出真相和表达自己的能力。平衡的喉轮让我们能够清晰、诚实地表达自己。'
        },
        {
          name: '眉心轮',
          english: 'Third Eye Chakra',
          color: '#2c3485',
          location: '眉心',
          function: '直觉、洞察、智慧',
          keywords: '直觉、洞察、智慧',
          description: '眉心轮是直觉和洞察的中心，掌管着我们的第六感和内在智慧。平衡的眉心轮让我们能够清晰地看到真相和拥有敏锐的直觉。'
        },
        {
          name: '顶轮',
          english: 'Crown Chakra',
          color: '#7e4997',
          location: '头顶',
          function: '灵性、连接、觉醒',
          keywords: '灵性、觉醒、连接',
          description: '顶轮是灵性连接的中心，掌管着我们与宇宙和神圣的连接。平衡的顶轮让我们感到与万物合一和拥有深刻的灵性体验。'
        }
      ]
    }
  },
  methods: {
    goToTest() {
      this.$router.push('/chakra-test')
    },
    
    goToBalance() {
      this.$router.push('/chakra-test/balance')
    },
    
    // 返回上一页
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.chakra-intro-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.nav-title {
  padding: 15px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 4px 15px rgba(86, 70, 128, 0.1);
  margin-bottom: 20px;
  border-radius: 0 0 20px 20px;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(86, 70, 128, 0.08);
  border: 1px solid rgba(86, 70, 128, 0.2);
  color: #564680;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(86, 70, 128, 0.15);
  border-color: rgba(86, 70, 128, 0.4);
  transform: translateX(-2px);
}

.color-bar {
  width: 6px;
  height: 25px;
  background: linear-gradient(135deg, #564680, #516790, #c9ab79);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(86, 70, 128, 0.3);
}

.title-text {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  letter-spacing: 0.5px;
}

.test-button-small {
  background: linear-gradient(135deg, #564680, #516790);
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  color: white;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(86, 70, 128, 0.3);
  transition: all 0.3s ease;
}

.test-button-small:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(86, 70, 128, 0.4);
}

.test-button-small span {
  margin-left: 4px;
}

.balance-button-small {
  background: linear-gradient(135deg, #c9ab79, #b8996a);
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  color: white;
  font-weight: 600;
  box-shadow: 0 3px 10px rgba(201, 171, 121, 0.3);
  transition: all 0.3s ease;
}

.balance-button-small:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(201, 171, 121, 0.4);
}

.balance-button-small span {
  margin-left: 4px;
}

.content-container {
  padding: 0 15px 20px;
}

.chakra-item {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(86, 70, 128, 0.08);
  transition: all 0.4s ease;
  border: 2px solid transparent;
  position: relative;
}

.chakra-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #564680, #516790, #c9ab79);
}

.chakra-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(86, 70, 128, 0.12);
  border-color: rgba(86, 70, 128, 0.2);
}

.chakra-header {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 2px solid rgba(86, 70, 128, 0.1);
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  position: relative;
}

.chakra-header::after {
  content: '';
  position: absolute;
  left: 20px;
  bottom: -2px;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #564680, #c9ab79);
}

.chakra-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.chakra-item:hover .chakra-dot {
  transform: scale(1.2) rotate(15deg);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.4);
}

.chakra-info {
  flex: 1;
}

.chakra-name {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin: 0 0 6px 0;
  letter-spacing: 0.5px;
}

.chakra-english {
  font-size: 13px;
  color: #666;
  margin: 0;
  font-style: italic;
  font-weight: 500;
}

.chakra-description {
  padding: 20px;
  border-bottom: 1px solid rgba(86, 70, 128, 0.08);
  
  p {
    font-size: 15px;
    line-height: 1.7;
    color: #555;
    margin: 0;
  }
}

.chakra-details {
  padding: 15px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  color: #666;
  min-width: 50px;
}

.value {
  color: #333;
  flex: 1;
}


/* 移动端适配 */
@media (max-width: 768px) {
  .chakra-item {
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .chakra-header {
    padding: 16px;
  }
  
  .chakra-name {
    font-size: 16px;
  }
  
  .chakra-description {
    padding: 16px;
    
    p {
      font-size: 14px;
    }
  }
  
  .nav-title {
    padding: 12px 20px;
    height: 55px;
  }
  
  .nav-left {
    gap: 8px;
  }
  
  .nav-right {
    gap: 6px;
  }
  
  .back-button {
    width: 36px;
    height: 36px;
  }
  
  .title-text {
    font-size: 18px;
  }
  
  .test-button-small,
  .balance-button-small {
    padding: 5px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .nav-title {
    padding: 10px 15px;
    height: 50px;
  }
  
  .nav-left {
    gap: 6px;
  }
  
  .nav-right {
    gap: 4px;
  }
  
  .back-button {
    width: 32px;
    height: 32px;
  }
  
  .color-bar {
    width: 4px;
    height: 20px;
  }
  
  .title-text {
    font-size: 16px;
  }
  
  .test-button-small,
  .balance-button-small {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .content-container {
    padding: 0 12px 20px;
  }
  
  .chakra-header {
    padding: 14px;
  }
  
  .chakra-name {
    font-size: 15px;
  }
  
  .chakra-description {
    padding: 14px;
  }
  
  .chakra-details {
    padding: 14px;
  }
}
</style>
