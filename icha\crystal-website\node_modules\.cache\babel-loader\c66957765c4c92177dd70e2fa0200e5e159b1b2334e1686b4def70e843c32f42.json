{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Layout from \"@/components/common/Layout\";\nimport { isMobilePhone, isMobile } from \"@/utils/index\";\nimport Message from \"@/utils/message\";\nexport default {\n  name: \"RegisterView\",\n  data() {\n    return {\n      isMobilePhone: isMobilePhone(),\n      loading: false,\n      currentStep: 1,\n      counting: 0,\n      timer: null,\n      registerForm: {\n        username: '',\n        phone: '',\n        verificationCode: '',\n        password: '',\n        confirmPassword: '',\n        nickName: '',\n        agreement: false\n      }\n    };\n  },\n  components: {\n    Layout\n  },\n  mounted() {\n    this.$wxShare();\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    // 验证手机号格式\n    validatePhone() {\n      return isMobile(this.registerForm.phone);\n    },\n    // 验证两次密码是否一致\n    validateConfirmPassword() {\n      return this.registerForm.password == this.registerForm.confirmPassword;\n    },\n    // 发送验证码\n    sendVerificationCode() {\n      if (this.counting > 0) return;\n      if (!this.validatePhone()) {\n        Message.error(\"请输入正确的手机号\");\n        return;\n      }\n\n      // 用户名可用，发送验证码\n      this.postRequestParams(\"/sendCode\", {\n        // username: this.registerForm.username,\n        phone: this.registerForm.phone\n      }).then(resp => {\n        console.log(resp);\n        if (resp && resp.code == 200) {\n          Message.success(\"验证码已发送\");\n          this.startCounting();\n        } else {\n          Message.error(resp.message || \"验证码发送失败\");\n        }\n      });\n    },\n    // 开始倒计时\n    startCounting() {\n      this.counting = 60;\n      this.timer = setInterval(() => {\n        if (this.counting > 0) {\n          this.counting--;\n        } else {\n          clearInterval(this.timer);\n        }\n      }, 1000);\n    },\n    // 一步完成注册\n    submitRegister() {\n      if (!this.registerForm.agreement) {\n        Message.error(\"请阅读并同意会员服务协议\");\n        return;\n      }\n      if (this.registerForm.password !== this.registerForm.confirmPassword) {\n        Message.error(\"两次输入密码不一致\");\n        return;\n      }\n      this.loading = true;\n\n      // 验证码正确，提交注册信息\n      this.completeRegistration();\n    },\n    // 完成注册\n    completeRegistration() {\n      this.postRequest(\"/registerCms\", {\n        // username: this.registerForm.username,\n        phone: this.registerForm.phone,\n        password: this.registerForm.password,\n        nickName: this.registerForm.nickName,\n        code: this.registerForm.verificationCode\n      }).then(resp => {\n        this.loading = false;\n        if (resp && resp.code == 200) {\n          this.currentStep = 2;\n          localStorage.setItem(\"token\", resp.data.token);\n          localStorage.setItem(\"userInfo\", JSON.stringify(resp.data.userInfo));\n          Message.success(\"注册成功\");\n        } else {\n          Message.error(resp.message || \"注册失败\");\n        }\n      });\n    },\n    // 显示会员协议\n    showAgreement() {\n      this.$dialog.alert({\n        title: '会员服务协议',\n        message: '国际水晶疗愈协会会员服务协议内容...',\n        confirmButtonText: '我已阅读并同意'\n      }).then(() => {\n        this.registerForm.agreement = true;\n      });\n    },\n    // 返回登录页\n    goToLogin() {\n      this.$router.push(\"/login\");\n    }\n  }\n};", "map": {"version": 3, "names": ["Layout", "isMobilePhone", "isMobile", "Message", "name", "data", "loading", "currentStep", "counting", "timer", "registerForm", "username", "phone", "verificationCode", "password", "confirmPassword", "nick<PERSON><PERSON>", "agreement", "components", "mounted", "$wxShare", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "validatePhone", "validateConfirmPassword", "sendVerificationCode", "error", "postRequestParams", "then", "resp", "console", "log", "code", "success", "startCounting", "message", "setInterval", "submitRegister", "completeRegistration", "postRequest", "localStorage", "setItem", "token", "JSON", "stringify", "userInfo", "showAgreement", "$dialog", "alert", "title", "confirmButtonText", "goToLogin", "$router", "push"], "sources": ["src/views/register.vue"], "sourcesContent": ["<template>\r\n\t<Layout>\r\n\t\t<div class=\"register-page\">\r\n\t\t\t<div class=\"register-container\">\r\n\t\t\t\t<div class=\"register-left\">\r\n\t\t\t\t\t<div class=\"register-logo\">\r\n\t\t\t\t\t\t<img src=\"@/assets/images/logo.png\" alt=\"国际水晶疗愈协会\" class=\"logo-image\" />\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"register-welcome\">\r\n\t\t\t\t\t\t<h2>欢迎加入</h2>\r\n\t\t\t\t\t\t<h1>国际水晶疗愈协会</h1>\r\n\t\t\t\t\t\t<p>成为会员后享受专业的水晶疗愈服务和丰富的学习资源</p>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"register-decoration\">\r\n\t\t\t\t\t\t<div class=\"crystal-1\"></div>\r\n\t\t\t\t\t\t<div class=\"crystal-2\"></div>\r\n\t\t\t\t\t\t<div class=\"crystal-3\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div class=\"register-right\">\r\n\t\t\t\t\t<div class=\"register-form-container\">\r\n\t\t\t\t\t\t<h2 class=\"register-title\">用户注册</h2>\r\n\t\t\t\t\t\t<p class=\"register-subtitle\">创建您的账号，加入我们的会员</p>\r\n\r\n\t\t\t\t\t\t<div v-if=\"currentStep == 1\">\r\n\t\t\t\t\t\t\t<van-form @submit=\"submitRegister\">\r\n\t\t\t\t\t\t\t\t<!-- <div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t<label for=\"username\">用户名</label>\r\n\t\t\t\t\t\t\t\t\t<van-field v-model=\"registerForm.username\" name=\"username\" placeholder=\"请输入用户名\"\r\n\t\t\t\t\t\t\t\t\t\t:rules=\"[\r\n\t\t\t\t\t\t\t\t\t\t\t{ required: true, message: '请输入用户名' },\r\n\t\t\t\t\t\t\t\t\t\t\t{ pattern: /^[a-zA-Z0-9_]{4,16}$/, message: '用户名为4-16位字母、数字或下划线' }\r\n\t\t\t\t\t\t\t\t\t\t]\" class=\"register-input\" />\r\n\t\t\t\t\t\t\t\t</div> -->\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t<label for=\"phone\">手机号</label>\r\n\t\t\t\t\t\t\t\t\t<van-field v-model=\"registerForm.phone\" name=\"phone\" placeholder=\"请输入手机号\" :rules=\"[\r\n\t\t\t\t\t\t\t\t\t\t{ required: true, message: '请输入手机号' },\r\n\t\t\t\t\t\t\t\t\t\t{ validator: validatePhone, message: '请输入正确的手机号' }\r\n\t\t\t\t\t\t\t\t\t]\" class=\"register-input\" />\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t<label for=\"verificationCode\">验证码</label>\r\n\t\t\t\t\t\t\t\t\t<div class=\"verification-code-container\">\r\n\t\t\t\t\t\t\t\t\t\t<van-field v-model=\"registerForm.verificationCode\" name=\"verificationCode\"\r\n\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入验证码\" :rules=\"[{ required: true, message: '请输入验证码' }]\"\r\n\t\t\t\t\t\t\t\t\t\t\tclass=\"register-input verification-input\" />\r\n\t\t\t\t\t\t\t\t\t\t<van-button size=\"small\" type=\"primary\" class=\"verification-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t:disabled=\"counting > 0\" @click=\"sendVerificationCode\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{ counting > 0 ? `${counting}秒后重试` : '获取验证码' }}\r\n\t\t\t\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t<label for=\"password\">设置密码</label>\r\n\t\t\t\t\t\t\t\t\t<van-field v-model=\"registerForm.password\" type=\"password\" name=\"password\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"请设置密码\" :rules=\"[\r\n\t\t\t\t\t\t\t\t\t\t\t{ required: true, message: '请设置密码' },\r\n\t\t\t\t\t\t\t\t\t\t\t{ pattern: /^.{8,20}$/, message: '密码长度为8-20个字符' }\r\n\t\t\t\t\t\t\t\t\t\t]\" class=\"register-input\" />\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t<label for=\"confirmPassword\">确认密码</label>\r\n\t\t\t\t\t\t\t\t\t<van-field v-model=\"registerForm.confirmPassword\" type=\"password\"\r\n\t\t\t\t\t\t\t\t\t\tname=\"confirmPassword\" placeholder=\"请再次输入密码\" :rules=\"[\r\n\t\t\t\t\t\t\t\t\t\t\t{ required: true, message: '请确认密码' },\r\n\t\t\t\t\t\t\t\t\t\t\t{ validator: validateConfirmPassword, message: '两次输入密码不一致' }\r\n\t\t\t\t\t\t\t\t\t\t]\" class=\"register-input\" />\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t<label for=\"nickName\">昵称</label>\r\n\t\t\t\t\t\t\t\t\t<van-field v-model=\"registerForm.nickName\" name=\"nickName\" placeholder=\"请输入昵称\"\r\n\t\t\t\t\t\t\t\t\t\t:rules=\"[{ required: true, message: '请输入昵称' }]\" class=\"register-input\" />\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"form-item\">\r\n\t\t\t\t\t\t\t\t\t<label>会员协议</label>\r\n\t\t\t\t\t\t\t\t\t<div class=\"agreement-container\">\r\n\t\t\t\t\t\t\t\t\t\t<van-checkbox v-model=\"registerForm.agreement\" shape=\"square\">\r\n\t\t\t\t\t\t\t\t\t\t\t我已阅读并同意<a @click.stop=\"showAgreement\" class=\"agreement-link\">《会员服务协议》</a>\r\n\t\t\t\t\t\t\t\t\t\t</van-checkbox>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t\t\t<van-button round block type=\"primary\" native-type=\"submit\" :loading=\"loading\"\r\n\t\t\t\t\t\t\t\t\t\t:disabled=\"!registerForm.agreement\">\r\n\t\t\t\t\t\t\t\t\t\t立即注册\r\n\t\t\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</van-form>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<!-- 注册成功 -->\r\n\t\t\t\t\t\t<div v-if=\"currentStep == 2\" class=\"register-success\">\r\n\t\t\t\t\t\t\t<van-icon name=\"success\" size=\"64\" color=\"#5e258f\" />\r\n\t\t\t\t\t\t\t<h3>注册成功</h3>\r\n\t\t\t\t\t\t\t<p>恭喜您已成功注册成为会员</p>\r\n\t\t\t\t\t\t\t<div class=\"form-submit\">\r\n\t\t\t\t\t\t\t\t<van-button round block type=\"primary\" @click=\"goToLogin\">\r\n\t\t\t\t\t\t\t\t\t立即登录\r\n\t\t\t\t\t\t\t\t</van-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"login-link\" v-if=\"currentStep !== 2\">\r\n\t\t\t\t\t\t\t<span>已有账号？</span>\r\n\t\t\t\t\t\t\t<router-link to=\"/login\">立即登录</router-link>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</Layout>\r\n</template>\r\n\r\n<script>\r\nimport Layout from \"@/components/common/Layout\";\r\nimport { isMobilePhone, isMobile } from \"@/utils/index\";\r\nimport Message from \"@/utils/message\";\r\n\r\nexport default {\r\n\tname: \"RegisterView\",\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisMobilePhone: isMobilePhone(),\r\n\t\t\tloading: false,\r\n\t\t\tcurrentStep: 1,\r\n\t\t\tcounting: 0,\r\n\t\t\ttimer: null,\r\n\t\t\tregisterForm: {\r\n\t\t\t\tusername: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tverificationCode: '',\r\n\t\t\t\tpassword: '',\r\n\t\t\t\tconfirmPassword: '',\r\n\t\t\t\tnickName: '',\r\n\t\t\t\tagreement: false\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tcomponents: {\r\n\t\tLayout\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$wxShare();\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\tif (this.timer) {\r\n\t\t\tclearInterval(this.timer);\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 验证手机号格式\r\n\t\tvalidatePhone() {\r\n\t\t\treturn isMobile(this.registerForm.phone);\r\n\t\t},\r\n\r\n\t\t// 验证两次密码是否一致\r\n\t\tvalidateConfirmPassword() {\r\n\t\t\treturn this.registerForm.password == this.registerForm.confirmPassword;\r\n\t\t},\r\n\r\n\t\t// 发送验证码\r\n\t\tsendVerificationCode() {\r\n\t\t\tif (this.counting > 0) return;\r\n\r\n\t\t\tif (!this.validatePhone()) {\r\n\t\t\t\tMessage.error(\"请输入正确的手机号\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 用户名可用，发送验证码\r\n\t\t\tthis.postRequestParams(\"/sendCode\", {\r\n\t\t\t\t// username: this.registerForm.username,\r\n\t\t\t\tphone: this.registerForm.phone\r\n\t\t\t})\r\n\t\t\t\t.then(resp => {\r\n\t\t\t\t\tconsole.log(resp)\r\n\t\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\t\tMessage.success(\"验证码已发送\");\r\n\t\t\t\t\t\tthis.startCounting();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tMessage.error(resp.message || \"验证码发送失败\");\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 开始倒计时\r\n\t\tstartCounting() {\r\n\t\t\tthis.counting = 60;\r\n\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\tif (this.counting > 0) {\r\n\t\t\t\t\tthis.counting--;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tclearInterval(this.timer);\r\n\t\t\t\t}\r\n\t\t\t}, 1000);\r\n\t\t},\r\n\r\n\t\t// 一步完成注册\r\n\t\tsubmitRegister() {\r\n\t\t\tif (!this.registerForm.agreement) {\r\n\t\t\t\tMessage.error(\"请阅读并同意会员服务协议\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (this.registerForm.password !== this.registerForm.confirmPassword) {\r\n\t\t\t\tMessage.error(\"两次输入密码不一致\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tthis.loading = true;\r\n\t\t\t\r\n\t\t\t// 验证码正确，提交注册信息\r\n\t\t\tthis.completeRegistration();\r\n\t\t},\r\n\r\n\t\t// 完成注册\r\n\t\tcompleteRegistration() {\r\n\t\t\tthis.postRequest(\"/registerCms\", {\r\n\t\t\t\t// username: this.registerForm.username,\r\n\t\t\t\tphone: this.registerForm.phone,\r\n\t\t\t\tpassword: this.registerForm.password,\r\n\t\t\t\tnickName: this.registerForm.nickName,\r\n\t\t\t\tcode: this.registerForm.verificationCode\r\n\t\t\t})\r\n\t\t\t\t.then(resp => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tif (resp && resp.code == 200) {\r\n\t\t\t\t\t\tthis.currentStep = 2;\r\n\t\t\t\t\t\tlocalStorage.setItem(\"token\", resp.data.token);\r\n\t\t\t\t\t\tlocalStorage.setItem(\"userInfo\", JSON.stringify(resp.data.userInfo));\r\n\t\t\t\t\t\tMessage.success(\"注册成功\");\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tMessage.error(resp.message || \"注册失败\");\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 显示会员协议\r\n\t\tshowAgreement() {\r\n\t\t\tthis.$dialog.alert({\r\n\t\t\t\ttitle: '会员服务协议',\r\n\t\t\t\tmessage: '国际水晶疗愈协会会员服务协议内容...',\r\n\t\t\t\tconfirmButtonText: '我已阅读并同意'\r\n\t\t\t}).then(() => {\r\n\t\t\t\tthis.registerForm.agreement = true;\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 返回登录页\r\n\t\tgoToLogin() {\r\n\t\t\tthis.$router.push(\"/login\");\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.register-page {\r\n\tmin-height: 100vh;\r\n\twidth: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground-color: #f8f9fa;\r\n\tbackground-image: url('@/assets/images/pattern-dark.png');\r\n\tbackground-repeat: repeat;\r\n\tpadding: 40px 20px;\r\n}\r\n\r\n.register-container {\r\n\tdisplay: flex;\r\n\twidth: 1200px;\r\n\tmin-height: 680px;\r\n\tborder-radius: 20px;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.register-left {\r\n\tflex: 1;\r\n\tbackground: linear-gradient(135deg, #5e258f, #8647ad);\r\n\tbackground-image: linear-gradient(135deg, rgba(94, 37, 143, 0.95), rgba(134, 71, 173, 0.9)), url('https://images.unsplash.com/photo-1521320226546-87b106cd055d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80');\r\n\tbackground-size: cover;\r\n\tbackground-position: center;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tposition: relative;\r\n\tcolor: #fff;\r\n\tpadding: 50px;\r\n\toverflow: hidden;\r\n}\r\n\r\n.register-logo {\r\n\tmargin-bottom: 40px;\r\n\ttext-align: center;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.logo-image {\r\n\twidth: 180px;\r\n\theight: auto;\r\n}\r\n\r\n.register-welcome {\r\n\ttext-align: center;\r\n\tmargin-bottom: 40px;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.register-welcome h2 {\r\n\tfont-size: 24px;\r\n\tfont-weight: 400;\r\n\tmargin-bottom: 20px;\r\n\tcolor: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.register-welcome h1 {\r\n\tfont-size: 38px;\r\n\tfont-weight: 700;\r\n\tmargin-bottom: 20px;\r\n\ttext-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.register-welcome p {\r\n\tfont-size: 16px;\r\n\tline-height: 1.6;\r\n\tmax-width: 400px;\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.register-decoration {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.crystal-1,\r\n.crystal-2,\r\n.crystal-3 {\r\n\tposition: absolute;\r\n\tbackground: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));\r\n\tbackdrop-filter: blur(5px);\r\n\tborder: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.crystal-1 {\r\n\twidth: 300px;\r\n\theight: 300px;\r\n\ttop: -100px;\r\n\tleft: -150px;\r\n}\r\n\r\n.crystal-2 {\r\n\twidth: 200px;\r\n\theight: 200px;\r\n\tbottom: 50px;\r\n\tright: -50px;\r\n}\r\n\r\n.crystal-3 {\r\n\twidth: 150px;\r\n\theight: 150px;\r\n\tbottom: -50px;\r\n\tleft: 100px;\r\n}\r\n\r\n.register-right {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 60px;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.register-form-container {\r\n\twidth: 100%;\r\n\tmax-width: 400px;\r\n}\r\n\r\n.register-title {\r\n\tfont-size: 32px;\r\n\tfont-weight: 700;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10px;\r\n\ttext-align: center;\r\n}\r\n\r\n.register-subtitle {\r\n\tfont-size: 16px;\r\n\tcolor: #666;\r\n\tmargin-bottom: 40px;\r\n\ttext-align: center;\r\n}\r\n\r\n.form-item {\r\n\tmargin-bottom: 25px;\r\n}\r\n\r\n.form-item label {\r\n\tdisplay: block;\r\n\tmargin-bottom: 8px;\r\n\tfont-size: 14px;\r\n\tcolor: #555;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.register-input {\r\n\t/deep/ .van-field__control {\r\n\t\theight: 48px;\r\n\t\tpadding: 0 15px;\r\n\t\tfont-size: 15px;\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tborder: 1px solid #e8eaee;\r\n\t\tborder-radius: 8px;\r\n\t}\r\n\r\n\t/deep/ .van-field__control:focus {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-color: #5e258f;\r\n\t\tbox-shadow: 0 0 0 3px rgba(94, 37, 143, 0.1);\r\n\t}\r\n}\r\n\r\n.verification-code-container {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.verification-input {\r\n\tflex: 1;\r\n\tmargin-right: 10px;\r\n}\r\n\r\n.verification-btn {\r\n\theight: 48px;\r\n\tbackground: linear-gradient(45deg, #5e258f, #8647ad);\r\n\tborder-color: #5e258f;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.agreement-container {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\r\n\t/deep/ .van-checkbox__label {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t/deep/ .van-checkbox__icon--checked {\r\n\t\tbackground-color: #5e258f;\r\n\t\tborder-color: #5e258f;\r\n\t}\r\n}\r\n\r\n.agreement-link {\r\n\tcolor: #5e258f;\r\n\ttext-decoration: none;\r\n\r\n\t&:hover {\r\n\t\ttext-decoration: underline;\r\n\t}\r\n}\r\n\r\n.form-submit {\r\n\tmargin-bottom: 25px;\r\n\r\n\t.van-button {\r\n\t\theight: 50px;\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 500;\r\n\t\tbackground: linear-gradient(45deg, #5e258f, #8647ad);\r\n\t\tborder-color: #5e258f;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground: linear-gradient(45deg, #4b1e73, #733c94);\r\n\t\t\tborder-color: #4b1e73;\r\n\t\t\ttransform: translateY(-2px);\r\n\t\t\tbox-shadow: 0 6px 15px rgba(94, 37, 143, 0.25);\r\n\t\t}\r\n\r\n\t\t&--disabled {\r\n\t\t\topacity: 0.6;\r\n\t\t\tbackground: linear-gradient(45deg, #5e258f, #8647ad);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.login-link {\r\n\ttext-align: center;\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n\r\n\ta {\r\n\t\tcolor: #5e258f;\r\n\t\tfont-weight: 500;\r\n\t\ttext-decoration: none;\r\n\t\tmargin-left: 5px;\r\n\r\n\t\t&:hover {\r\n\t\t\ttext-decoration: underline;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.register-success {\r\n\ttext-align: center;\r\n\tpadding: 20px 0 40px;\r\n\r\n\th3 {\r\n\t\tfont-size: 24px;\r\n\t\tfont-weight: 600;\r\n\t\tmargin: 20px 0 10px;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\tp {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n\t.register-container {\r\n\t\twidth: 95%;\r\n\t\tflex-direction: column;\r\n\t\tmin-height: auto;\r\n\t}\r\n\r\n\t.register-left {\r\n\t\tpadding: 40px 20px;\r\n\t}\r\n\r\n\t.register-right {\r\n\t\tpadding: 40px 20px;\r\n\t}\r\n\r\n\t.register-welcome h1 {\r\n\t\tfont-size: 32px;\r\n\t}\r\n}\r\n\r\n@media (max-width: 767px) {\r\n\t.register-page {\r\n\t\tpadding: 20px 10px;\r\n\t}\r\n\r\n\t.register-container {\r\n\t\twidth: 100%;\r\n\t\tborder-radius: 10px;\r\n\t}\r\n\r\n\t.register-left {\r\n\t\tpadding: 30px 15px;\r\n\t}\r\n\r\n\t.register-right {\r\n\t\tpadding: 30px 15px;\r\n\t}\r\n\r\n\t.register-logo .logo-image {\r\n\t\twidth: 150px;\r\n\t}\r\n\r\n\t.register-welcome h1 {\r\n\t\tfont-size: 28px;\r\n\t}\r\n\r\n\t.register-welcome p {\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.register-title {\r\n\t\tfont-size: 28px;\r\n\t}\r\n\r\n\t.register-subtitle {\r\n\t\tfont-size: 14px;\r\n\t\tmargin-bottom: 30px;\r\n\t}\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HA,OAAAA,MAAA;AACA,SAAAC,aAAA,EAAAC,QAAA;AACA,OAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAJ,aAAA,EAAAA,aAAA;MACAK,OAAA;MACAC,WAAA;MACAC,QAAA;MACAC,KAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;QACAC,gBAAA;QACAC,QAAA;QACAC,eAAA;QACAC,QAAA;QACAC,SAAA;MACA;IACA;EACA;EACAC,UAAA;IACAlB;EACA;EACAmB,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,cAAA;IACA,SAAAZ,KAAA;MACAa,aAAA,MAAAb,KAAA;IACA;EACA;EACAc,OAAA;IACA;IACAC,cAAA;MACA,OAAAtB,QAAA,MAAAQ,YAAA,CAAAE,KAAA;IACA;IAEA;IACAa,wBAAA;MACA,YAAAf,YAAA,CAAAI,QAAA,SAAAJ,YAAA,CAAAK,eAAA;IACA;IAEA;IACAW,qBAAA;MACA,SAAAlB,QAAA;MAEA,UAAAgB,aAAA;QACArB,OAAA,CAAAwB,KAAA;QACA;MACA;;MAEA;MACA,KAAAC,iBAAA;QACA;QACAhB,KAAA,OAAAF,YAAA,CAAAE;MACA,GACAiB,IAAA,CAAAC,IAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAG,IAAA;UACA9B,OAAA,CAAA+B,OAAA;UACA,KAAAC,aAAA;QACA;UACAhC,OAAA,CAAAwB,KAAA,CAAAG,IAAA,CAAAM,OAAA;QACA;MACA;IACA;IAEA;IACAD,cAAA;MACA,KAAA3B,QAAA;MACA,KAAAC,KAAA,GAAA4B,WAAA;QACA,SAAA7B,QAAA;UACA,KAAAA,QAAA;QACA;UACAc,aAAA,MAAAb,KAAA;QACA;MACA;IACA;IAEA;IACA6B,eAAA;MACA,UAAA5B,YAAA,CAAAO,SAAA;QACAd,OAAA,CAAAwB,KAAA;QACA;MACA;MAEA,SAAAjB,YAAA,CAAAI,QAAA,UAAAJ,YAAA,CAAAK,eAAA;QACAZ,OAAA,CAAAwB,KAAA;QACA;MACA;MAEA,KAAArB,OAAA;;MAEA;MACA,KAAAiC,oBAAA;IACA;IAEA;IACAA,qBAAA;MACA,KAAAC,WAAA;QACA;QACA5B,KAAA,OAAAF,YAAA,CAAAE,KAAA;QACAE,QAAA,OAAAJ,YAAA,CAAAI,QAAA;QACAE,QAAA,OAAAN,YAAA,CAAAM,QAAA;QACAiB,IAAA,OAAAvB,YAAA,CAAAG;MACA,GACAgB,IAAA,CAAAC,IAAA;QACA,KAAAxB,OAAA;QACA,IAAAwB,IAAA,IAAAA,IAAA,CAAAG,IAAA;UACA,KAAA1B,WAAA;UACAkC,YAAA,CAAAC,OAAA,UAAAZ,IAAA,CAAAzB,IAAA,CAAAsC,KAAA;UACAF,YAAA,CAAAC,OAAA,aAAAE,IAAA,CAAAC,SAAA,CAAAf,IAAA,CAAAzB,IAAA,CAAAyC,QAAA;UACA3C,OAAA,CAAA+B,OAAA;QACA;UACA/B,OAAA,CAAAwB,KAAA,CAAAG,IAAA,CAAAM,OAAA;QACA;MACA;IACA;IAEA;IACAW,cAAA;MACA,KAAAC,OAAA,CAAAC,KAAA;QACAC,KAAA;QACAd,OAAA;QACAe,iBAAA;MACA,GAAAtB,IAAA;QACA,KAAAnB,YAAA,CAAAO,SAAA;MACA;IACA;IAEA;IACAmC,UAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}